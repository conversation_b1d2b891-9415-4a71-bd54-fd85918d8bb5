import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { CircularService } from '../services';
import {
  CircularResponseDto,
  CreateCircularDto,
  PaginatedCircularResponseDto,
  QueryCircularDto,
  UpdateCircularDto,
} from '../dtos';
import { CircularStatusEnums } from '../enums';

@ApiTags('Circular')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('circular-self-service')
export class CircularSelfServiceController {
  constructor(private readonly circularService: CircularService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new circular' })
  @ApiResponse({ status: 201, type: CircularResponseDto })
  async create(
    @Body() createCircularDto: CreateCircularDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<CircularResponseDto> {
    return this.circularService.create(createCircularDto, jwtPayload.id);
  }

  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: CircularResponseDto })
  @Get('/outbox')
  getOutBoxCirculars(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryCircularDto,
  ): Promise<PaginatedCircularResponseDto> {
    return this.circularService.getAll(
      {
        ...query,
        from: jwtPayload.id,
        createdBy: jwtPayload.id,
        status: CircularStatusEnums.DRAFT,
        useOrConditionForEmployees: true,
      },
      jwtPayload.id,
    );
  }
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: CircularResponseDto })
  @Get('/sent')
  getSentCirculars(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryCircularDto,
  ): Promise<PaginatedCircularResponseDto> {
    return this.circularService.getAll(
      {
        ...query,
        from: jwtPayload.id,
        createdBy: jwtPayload.id,
        status: CircularStatusEnums.PUBLISH,
        useOrConditionForEmployees: true,
      },
      jwtPayload.id,
    );
  }
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: CircularResponseDto })
  @Get('/inbox')
  getInBoxCirculars(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryCircularDto,
  ): Promise<PaginatedCircularResponseDto> {
    return this.circularService.getAll(
      {
        ...query,
        status: CircularStatusEnums.PUBLISH,
        useOrConditionForEmployees: true,
      },
      jwtPayload.id,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get one circular record' })
  @ApiResponse({ status: 200, type: CircularResponseDto })
  getOne(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<CircularResponseDto> {
    return this.circularService.getOne({ id, employeeId: jwtPayload.id });
  }
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({
    status: 200,
    description: 'Circular updated successfully',
    type: CircularResponseDto,
  })
  @Patch(':id')
  updateCircular(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Body() dto: UpdateCircularDto,
  ): Promise<CircularResponseDto> {
    return this.circularService.updateRecord({ id, data: dto, employeeId: jwtPayload.id });
  }
}
