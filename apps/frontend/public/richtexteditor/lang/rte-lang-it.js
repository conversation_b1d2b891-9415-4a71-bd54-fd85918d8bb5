//Italian , Italiano
RTE_DefaultConfig.text_language = "lingua";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "annullare";	//"Cancel"
RTE_DefaultConfig.text_normal = "normale";	//"Normal"
RTE_DefaultConfig.text_h1 = "Titolo 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Titolo 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Titolo 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Titolo 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Titolo 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Titolo 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Titolo 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "chiudere";	//"Close"
RTE_DefaultConfig.text_bold = "deciso";	//"Bold"
RTE_DefaultConfig.text_italic = "corsivo";	//"Italic"
RTE_DefaultConfig.text_underline = "sottolineare";	//"Underline"
RTE_DefaultConfig.text_strike = "Linea d'attacco";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "apice";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Maiuscole/minuscole";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Maiuscole/minuscole";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Rimuovi formato";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Inserisci collegamento";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Apri collegamento";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Modifica collegamento";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Rimuovi collegamento";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Altezza linea";	//"Line Height"
RTE_DefaultConfig.text_indent = "trattino";	//"Indent"
RTE_DefaultConfig.text_outdent = "Rientro negativo";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Citazione blocco";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Lista ordinata";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Elenco non ordinato";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Inserisci regola orizzontale";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Inserisci data";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Inserisci tabella";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Inserisci immagine";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Inserisci video";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Inserisci codice";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Crea PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Inserisci Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Caratteri speciali";	//"Special characters"
RTE_DefaultConfig.text_characters = "caratteri";	//"Characters"
RTE_DefaultConfig.text_fontname = "fonte battesimale";	//"Font"
RTE_DefaultConfig.text_fontsize = "dimensione";	//"Size"
RTE_DefaultConfig.text_forecolor = "Colore testo";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Colore posteriore";	//"Back Color"
RTE_DefaultConfig.text_justify = "giustificare";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Giustifica a sinistra";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Giustifica a destra";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centro Giustifica";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Giustifica completo";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Giustifica nessuno";	//"Justify None"
RTE_DefaultConfig.text_delete = "eliminare";	//"Delete"
RTE_DefaultConfig.text_save = "Salva file";	//"Save file"
RTE_DefaultConfig.text_selectall = "Seleziona tutto";	//"Select All"
RTE_DefaultConfig.text_code = "Codice HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "anteprima";	//"Preview"
RTE_DefaultConfig.text_print = "stampa";	//"Print"
RTE_DefaultConfig.text_undo = "slacciare";	//"Undo"
RTE_DefaultConfig.text_redo = "rifare";	//"Redo"
RTE_DefaultConfig.text_more = "Più...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Nuovo documento";	//"New Doc"
RTE_DefaultConfig.text_help = "aiutare";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Adatta alla finestra";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Esci dallo schermo intero";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor di immagini";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Stili immagine";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Stili in linea";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Stili di paragrafo";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Stili di collegamento";	//"Link Styles"
RTE_DefaultConfig.text_link = "collegamento";	//"Link"
RTE_DefaultConfig.text_style = "stili";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Classi Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Per URL";	//"By Url"
RTE_DefaultConfig.text_upload = "caricare";	//"Upload"
RTE_DefaultConfig.text_size = "dimensione";	//"Size"
RTE_DefaultConfig.text_text = "testo";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Apri in una nuova scheda";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "inserire";	//"Insert"
RTE_DefaultConfig.text_update = "aggiornare";	//"Update"
RTE_DefaultConfig.text_find = "Trova e sostituisci";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "trovare";	//"Find"
RTE_DefaultConfig.text_replacewith = "sostituire";	//"Replace"
RTE_DefaultConfig.text_findnext = "prossimo";	//"Next"
RTE_DefaultConfig.text_replaceonce = "sostituire";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Sostituisci tutto";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Maiuscole/minuscole";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Parola di corrispondenza";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Sposta giù";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Sposta su";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Dimensioni automatiche";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "Larghezza 100%";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "Larghezza 75%";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "Larghezza 50%";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "Larghezza 25%";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Imposta dimensione";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Testo alternativo";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "giustificare";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Didascalia immagine";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Unisci celle";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Dividi celle verticalmente";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Dividi celle orizzontalmente";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Colore testo cella";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Colore Sfondo cella";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Inserisci riga sopra";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Inserisci riga sotto";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Inserisci colonna a sinistra";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Inserisci colonna a destra";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Elimina colonna";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Elimina riga";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Elimina tabella";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Dimensioni automatiche";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Intestazione tabella";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Aggiungere un nuovo paragrafo";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "di strass";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "di strass";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Incolla testo";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Incolla come Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Incolla parola";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Si prega di utilizzare CTRL -V per incollare il contenuto nella casella sottostante. Il contenuto verrà pulito automaticamente.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragrafi";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragrafi";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Sposta su";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Sposta giù";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplicato";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "eliminare";	//"Delete"
RTE_DefaultConfig.text_pmore = "Più..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Più..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Attiva/disattiva bordo";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "tagliare";	//"Cut"
RTE_DefaultConfig.text_copy = "copia";	//"Copy"
RTE_DefaultConfig.text_copied = "copiato";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Inserisci galleria";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Inserisci documento";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Inserisci modello";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "anteprima";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normale";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "compressa";	//"Tablet"
RTE_DefaultConfig.text_table = "tabella";	//"Table"
RTE_DefaultConfig.text_tablecell = "Cella tabella";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Riga tabella";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Colonna tabella";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatico";	//"Automatic"
RTE_DefaultConfig.text_colormore = "più";	//"More"
RTE_DefaultConfig.text_colorpicker = "Selettore colore";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Tavolozza Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Colori con nome";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "semplice";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "inoltre";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Trascinamento della selezione";	//"Drag and drop"
RTE_DefaultConfig.text_or = "oppure";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Clicca per caricare";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Didascalia immagine predefinita";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "ricerca";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Il testo da aggiungere ha raggiunto il limite di caratteri per questo campo.";	//"The text to be added has reached the character limit for this field."
