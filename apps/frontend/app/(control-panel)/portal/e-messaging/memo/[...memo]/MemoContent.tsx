import { MemoResponseDto, MemoStatusEnums } from '@/services/api/types/memo.types';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import OrgProfile from '@/app/(control-panel)/portal/shared/components/OrgProfile/OrgProfile';

type MemoContentProps = {
  memo: MemoResponseDto;
  fromEmployeeSignature: string | null;
};

function MemoContent({ memo, fromEmployeeSignature }: MemoContentProps) {
  return (
    <Paper
      className="w-full mx-auto sm:my-2 lg:mt-4 p-6 sm:p-10 sm:py-12 rounded-xl shadow-sm"
      sx={{
        minHeight: 'fit-content',
        height: 'auto',
        maxHeight: 'none',
        overflow: 'visible',
      }}
    >
      {/* Organization Profile (centered) */}
      <div className="mt-3 mb-5">
        <OrgProfile size="medium" showFullDetails={true} />
      </div>

      {/* INTERNAL MEMO (underlined, centered) */}
      <div className="text-center mb-8">
        <Typography 
          variant="h5" 
          className="font-bold" 
          sx={{ textDecoration: 'underline' }}
        >
          INTERNAL MEMO
        </Typography>
      </div>

      {/* Left-aligned memo details */}
      <div className="flex flex-col gap-2 mb-8">
        <Typography variant="body1" color="text.secondary">
          <strong>Subject:</strong> {memo.title}
        </Typography>
        
        <Typography variant="body1" color="text.secondary">
          <strong>Reference No:</strong> {memo.reference}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>To:</strong>{' '}
          {memo.toEmployees && memo.toEmployees.length > 0
            ? memo.toEmployees.map(emp => `${emp.firstName} ${emp.lastName}`).join(', ')
            : 'N/A'}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>Through:</strong>{' '}
          {memo.throughEmployees && memo.throughEmployees.length > 0
            ? memo.throughEmployees.map(emp => `${emp.firstName} ${emp.lastName}`).join(', ')
            : 'N/A'}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>From:</strong>{' '}
          {memo.fromEmployee
            ? `${memo.fromEmployee.firstName} ${memo.fromEmployee.lastName}`
            : 'N/A'}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>Date:</strong> {moment(memo.createdAt).format('DD MMM YYYY, h:mm A')}
        </Typography>
      </div>

      {/* Memo Content */}
      <div
        className="prose prose-sm dark:prose-invert w-full max-w-full min-h-fit pb-6"
        dangerouslySetInnerHTML={{ __html: memo?.body || '' }}
        dir="ltr"
        style={{
          wordWrap: 'break-word',
          overflowWrap: 'break-word',
          hyphens: 'auto',
        }}
      />

      {/* Signature Section - Only show for approved memos (no divider) */}
      {memo?.status === MemoStatusEnums.Approved && memo?.fromEmployee && (
        <div className="mt-8">
          <Typography variant="body2" className="mb-3 font-medium text-gray-700">
            Signatory:
          </Typography>
          {fromEmployeeSignature ? (
            <img
              className="w-32 h-16 object-contain mb-2"
              src={fromEmployeeSignature}
              alt="Employee signature"
              style={{
                maxWidth: '128px',
                maxHeight: '64px',
                width: 'auto',
                height: 'auto',
              }}
            />
          ) : null}
          <Typography variant="body2" className="font-medium text-gray-800">
            {memo?.fromEmployee?.firstName} {memo?.fromEmployee?.lastName}
          </Typography>
        </div>
      )}
    </Paper>
  );
}

export default MemoContent;
