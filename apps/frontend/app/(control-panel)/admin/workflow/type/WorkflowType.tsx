'use client';

import React, { useMemo, useState } from 'react';
import {
  <PERSON>ton,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  CircularProgress,
  Paper,
  ListItem,
  Breadcrumbs,
  Typography,
  Link,
  ListItemText,
} from '@mui/material';
import FusePageCarded from '@fuse/core/FusePageCarded/FusePageCarded';
import { Edit, Trash } from 'lucide-react';
import { apiClient } from '@/services/api/apiClient';
import { enqueueSnackbar } from 'notistack';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';
import FuseLoading from '@fuse/core/FuseLoading';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TextInput from '@/components/formInputs/TextInput';

interface WorkflowType {
  id?: string;
  workflowType: string;
}

export default function WorkflowTypePage() {
  const [workflowTypes, setWorkflowTypes] = useState([]);
  const [addmode, setAddMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentWorkflowType, setCurrentWorkflowType] = useState<WorkflowType | null>(null);

  const handleOpenAdd = () => {
    setEditMode(false);
    setAddMode(true);
  };

  const handleOpenEdit = (wType) => {
    setEditMode(true);
    setCurrentWorkflowType(wType);
  };

  const handleDelete = async (id) => {
    try {
      const confirmDelete = window.confirm(
        'Are you sure you want to delete this workflow type? This action cannot be undone.',
      );
      if (!confirmDelete) {
        return;
      }
      await apiClient.delete(`/workflow-types/${id}`);
      enqueueSnackbar('Workflow type deleted successfully', {
        variant: 'success',
      });
      fetchWorkflowTypes();
    } catch (error) {
      enqueueSnackbar('Attempt to delete workflow type failed', {
        variant: 'error',
      });
    } finally {
      fetchWorkflowTypes();
    }
  };

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
    setCurrentWorkflowType(null);
  };

  const fetchWorkflowTypes = async () => {
    setLoading(true);
    try {
      const responseData: [] = await apiClient.get('/workflow-types');
      setWorkflowTypes(responseData);
    } catch (_err) {
      console.error('Failed to fetch workflow types:', _err);
    } finally {
      setLoading(false);
    }
  };
  const handleFormSubmit = async (values: WorkflowType, actions: FormikHelpers<WorkflowType>) => {
    try {
      if (editMode) {
        await apiClient.patch(`/workflow-types/${currentWorkflowType.id}`, values);
      } else {
        await apiClient.post('/workflow-types', values);
      }
      enqueueSnackbar('Workflow data saved successfully', {
        variant: 'success',
      });
      fetchWorkflowTypes();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };

  const tableColumns = useMemo<GridColDef<WorkflowType>[]>(
    () => [
      {
        field: 'number',
        headerName: '#',
        renderCell: (params) => params.api.getAllRowIds().indexOf(params.id) + 1,
      },
      { field: 'workflowType', headerName: 'Workflow Type', flex: 2 },
      {
        field: 'actions',
        headerName: 'Actions',
        flex: 1,
        renderCell: (params) => (
          <Box display="flex" justifyContent="flex-end">
            <Tooltip title="Edit">
              <IconButton
                aria-label="Edit"
                onClick={() => handleOpenEdit(params.row)}
                sx={{ mr: 1 }}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                aria-label="Delete"
                onClick={() => handleDelete(params.row.id)}
                sx={{ color: 'error.main' }}
              >
                <Trash size={18} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [],
  );

  React.useEffect(() => {
    fetchWorkflowTypes();
  }, []);

  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 'bold',
                      color: 'white',
                    }}
                  >
                    Workflow Types
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/workflow/type" color="inherit">
                      Workflow Types
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
              style={{ color: 'white' }}
              onClick={handleOpenAdd}
            >
              Add Workflow Type
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Box p={2}>
          {loading ? (
            <FuseLoading />
          ) : (
            <Grid container spacing={2} mb={2}>
              <Grid item xs={12}>
                <Paper>
                  <DataGrid
                    rows={workflowTypes}
                    columns={tableColumns}
                    autoHeight
                    sx={(theme) => ({
                      backgroundColor: 'transparent',
                      borderColor: theme.palette.primary.main,
                      '& .MuiDataGrid-columnHeaderRow': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeaders': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeader': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row': {
                        border: '1px solid lightgray',
                      },
                      '& .MuiDataGrid-columnHeaderTitle': {
                        fontWeight: 'bold',
                        color: theme.palette.common.white,
                      },
                      '& .MuiDataGrid-overlay': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row:first-of-type': {
                        // Changed from first-child
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row.Mui-hovered': {
                        backgroundColor: 'transparent',
                      },
                      // Take out the hover colour
                      '& .MuiDataGrid-row:hover': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-cell': {
                        backgroundColor: 'transparent',
                      },
                    })}
                    getRowId={(row) => row.id}
                  />
                </Paper>
              </Grid>
            </Grid>
          )}

          <Dialog open={addmode || editMode} onClose={handleDialogClose} maxWidth="sm" fullWidth>
            <DialogTitle>{editMode ? 'Edit Workflow Type' : 'Add Workflow Type'}</DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  workflowType: currentWorkflowType?.workflowType || '',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting }: FormikProps<WorkflowType>) => {
                  return (
                    <Form>
                      <Grid container spacing={2}>
                        <Grid item xs={12} p={2}>
                          <TextInput
                            label="Workflow Type"
                            id="workflowType"
                            value={values.workflowType}
                            floatingLabel
                            name="workflowType"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid
                          item
                          container
                          justifyContent={'space-between'}
                          alignItems={'center'}
                          xs={12}
                        >
                          <Button variant="outlined" onClick={handleDialogClose} type="button">
                            Cancel
                          </Button>
                          <Button variant="contained" type="submit" color="primary">
                            {isSubmitting ? <CircularProgress /> : 'Submit'}
                          </Button>
                        </Grid>
                      </Grid>
                    </Form>
                  );
                }}
              </Formik>
            </DialogContent>
          </Dialog>
        </Box>
      }
    />
  );
}
