'use client';

import React, { useCallback, useMemo, useState } from 'react';
import {
  Button,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  Paper,
  ListItem,
  Typography,
  ListItemText,
  Breadcrumbs,
  Link,
  styled,
} from '@mui/material';
import FusePageCarded from '@fuse/core/FusePageCarded/FusePageCarded';
import { Edit, Trash } from 'lucide-react';
import { apiClient } from '@/services/api/apiClient';
import { enqueueSnackbar } from 'notistack';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';
import TextInput from '../../../../components/formInputs/TextInput';
import FuseLoading from '@fuse/core/FuseLoading';
import {
  DataGrid,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridColDef,
  GridRowSpacingParams,
} from '@mui/x-data-grid';
import SelectInput from '@/components/formInputs/SelectInput';
import { Close, Delete, Visibility } from '@mui/icons-material';
import { Employee } from '../manage-employee/page';
import { Radio, RadioChangeEvent } from 'antd';

interface Registry {
  id?: string;
  registryName: string;
  ownerId: string;
}

const StyledRadioGroup = styled(Radio.Group)(({ theme }) => ({
  '.ant-radio-button-wrapper': {
    color: theme.palette.text.primary,
    borderColor: theme.palette.primary.main,

    '&:hover': {
      color: theme.palette.primary.main,
    },

    '&-checked': {
      backgroundColor: theme.palette.primary.main + '!important',
      borderColor: theme.palette.primary.main + '!important',
      color: 'white!important',

      '&::before': {
        backgroundColor: theme.palette.primary.main + '!important',
      },
    },
  },
}));

export default function RegistryPage() {
  const [registries, setRegisteries] = useState([]);
  const [selectedRegistryCollaborators, setSelectedRegistryCollaborators] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [viewMode, setViewMode] = useState(false);
  const [addMode, setAddMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentRegistry, setCurrentRegistry] = useState<Registry | null>(null);
  const [manageSelectedRegistryCollaborators, setManageSelectedRegistryCollaborators] =
    useState(false);

  const handleModeChange = (e: RadioChangeEvent) => {
    setManageSelectedRegistryCollaborators(e.target.value);
  };
  const handleOpenAdd = () => {
    setEditMode(false);
    setViewMode(false);
    setCurrentRegistry(null);
    setAddMode(true);
  };
  const handleOpenView = (pos) => {
    setEditMode(false);
    setCurrentRegistry(pos);
    setAddMode(false);
    setViewMode(true);
    fetchRegistryCollaborators(pos.id);
  };

  const handleOpenEdit = (pos) => {
    setEditMode(true);
    setCurrentRegistry(pos);
    setAddMode(false);
    setViewMode(false);
  };

  const fetchRegistries = async (id?: string) => {
    setLoading(true);
    try {
      if (id) {
        const responseData: Registry = await apiClient.get(`/registries/${id}`);
        setCurrentRegistry(responseData);
        return;
      }
      const { data } = await apiClient.get('/registries');
      setRegisteries(data);
    } catch (_err) {
      console.error('Failed to fetch registry:', _err);
    } finally {
      setLoading(false);
    }
  };
  const fetchRegistryCollaborators = async (registryId) => {
    setLoading(true);
    try {
      const responseData: [] = await apiClient.get(`/registries/${registryId}/collaborators`);
      setSelectedRegistryCollaborators(responseData);
    } catch (_err) {
      console.error('Failed to fetch registry collaborators:', _err);
      setSelectedRegistryCollaborators([]);
    } finally {
      setLoading(false);
    }
  };
  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const confirmation = window.confirm(
        'Are you sure you want to delete this registry? This action cannot be undone.',
      );
      if (!confirmation) {
        return;
      }
      await apiClient.delete(`/registries/${id}`);
      enqueueSnackbar('Deleted Successfully', {
        variant: 'success',
      });
      fetchRegistries();
    } catch (error) {
      enqueueSnackbar('Attempt to delete unit failed', {
        variant: 'error',
      });
      console.error('Failed to delete unit:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleCollaboratorDelete = async (id) => {
    setLoading(true);
    try {
      const confirmation = window.confirm(
        'Are you sure you want to remove this collaborator from this registry?',
      );
      if (!confirmation) {
        return;
      }
      await apiClient.delete(`/registries/${currentRegistry?.id}/collaborators/${id}`);
      enqueueSnackbar('Deleted Successfully', {
        variant: 'success',
      });
      fetchRegistryCollaborators(currentRegistry?.id);
      fetchRegistries();
    } catch (error) {
      enqueueSnackbar('Attempt to delete unit failed', {
        variant: 'error',
      });
      console.error('Failed to delete unit:', error);
    } finally {
      setLoading(false);
    }
  };

  const employeeColumns = React.useMemo<GridColDef<Employee>[]>(
    () => [
      {
        field: 'title',
        headerName: 'Title',
      },
      {
        field: 'firstName',
        headerName: 'First Name',
        flex: 1,
      },
      {
        field: 'lastName',
        headerName: 'Last Name',
        flex: 1,
      },
      {
        field: 'otherNames',
        headerName: 'Other Names',
        flex: 1,
      },
      {
        field: 'gender',
        headerName: 'Gender',
        flex: 1,
      },
      {
        field: 'email',
        headerName: 'Email',
        flex: 2,
      },
      {
        field: 'stateOfOrigin',
        headerName: 'State of Origin',
        hide: true,
      },
      {
        field: 'gradeLevel',
        headerName: 'Grade Level',
        hide: true,
      },
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
      },
    ],
    [],
  );
  const collaboratorsColumns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'serialNumber',
        headerName: 'S/N',
        flex: 0.5,
        renderCell: (params) => {
          // Get the index of the current row in the grid
          return params.api.getAllRowIds().indexOf(params.id) + 1;
        },
      },
      {
        field: 'firstName',
        headerName: 'Name',
        flex: 2,
        renderCell: (params) => {
          const employee = employees.find((emp) => emp.userId === params.row.employeeId);
          return `${employee?.lastName} ${employee?.firstName} ${employee?.otherNames}`;
        },
      },
      {
        field: 'gender',
        headerName: 'Gender',
        flex: 1,
        renderCell: (params) => {
          const employee = employees.find((emp) => emp.userId === params.row.employeeId);
          return `${employee?.gender}`;
        },
      },
      {
        field: 'email',
        headerName: 'Email',
        flex: 2,
        renderCell: (params) => {
          const employee = employees.find((emp) => emp.userId === params.row.employeeId);
          return `${employee?.email}`;
        },
      },
      {
        field: 'action',
        headerName: 'Actions',
        flex: 1,
        renderCell: (params) => (
          <IconButton
            aria-label="Delete collaborator"
            onClick={() => handleCollaboratorDelete(params.row.employeeId)}
            sx={{ color: 'error.main' }}
          >
            <Delete />
          </IconButton>
        ),
      },
    ],
    [],
  );
  const registryColumns = useMemo<GridColDef<Registry>[]>(
    () => [
      {
        field: 'serialNumber',
        headerName: 'S/N',
        flex: 0.5,
        renderCell: (params) => {
          // Get the index of the current row in the grid
          return params.api.getAllRowIds().indexOf(params.id) + 1;
        },
      },
      {
        field: 'registryName',
        headerName: 'Registry Name',
        flex: 2,
      },
      {
        field: 'ownerId',
        headerName: 'Owner',
        flex: 2,
        renderCell: (params) => {
          const employee = employees.find((emp) => emp.userId === params.row.ownerId);
          return employee
            ? `${employee.lastName} ${employee.firstName} ${employee.otherNames}`
            : 'N/A';
        },
      },
      {
        field: 'actions',
        headerName: 'Actions',
        flex: 1,
        renderCell: (params) => (
          <Box>
            <Tooltip title="View">
              <IconButton
                aria-label="View"
                onClick={() => handleOpenView(params.row)}
                sx={{ mr: 1 }}
              >
                <Visibility />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit">
              <IconButton
                aria-label="Edit"
                onClick={() => handleOpenEdit(params.row)}
                sx={{ mr: 1 }}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                aria-label="Delete"
                onClick={() => handleDelete(params.row.id)}
                sx={{ color: 'error.main' }}
              >
                <Trash size={18} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [],
  );

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
    setViewMode(false);
    setCurrentRegistry(null);
  };

  const fetchEmployees = async () => {
    try {
      const { employees } = await apiClient.get(`/employees`);

      setEmployees(employees);
    } catch (_err) {
      console.error('Failed to fetch requested data:', _err);
    }
  };
  const handleFormSubmit = async (values: Registry, actions: FormikHelpers<Registry>) => {
    try {
      if (editMode) {
        await apiClient.patch(`/registries/${currentRegistry.id}`, values);
      } else {
        await apiClient.post('/registries', values);
      }
      enqueueSnackbar('Registry saved successfully', {
        variant: 'success',
      });
      fetchRegistries();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };

  const cVisibModel = useMemo(() => {
    return {
      id: false,
      userId: false,
      stateOfOrigin: false,
      bloodGroup: false,
      gradeLevel: false,
      firstAppointmentDate: false,
      presentAppointmentDate: false,
    };
  }, []);
  const getRowSpacing = useCallback((params: GridRowSpacingParams) => {
    return {
      top: params.isFirstVisible ? 0 : 5,
      bottom: params.isLastVisible ? 0 : 5,
    };
  }, []);
  const handleCollaboratorsFormSubmit = async (values, actions) => {
    try {
      await apiClient.post(`/registries/${currentRegistry.id}/collaborators/batch`, values);
      enqueueSnackbar('Registry collaborators saved successfully', {
        variant: 'success',
      });
      fetchRegistryCollaborators(currentRegistry.id);
      setManageSelectedRegistryCollaborators(false);
      // handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };
  React.useEffect(() => {
    const fetchData = async () => {
      await fetchEmployees();
    };
    fetchData();
    fetchRegistries();
  }, []);

  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 'bold',
                      color: 'white',
                    }}
                  >
                    Registry
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/registry" color="inherit">
                      Registry
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
              style={{ color: 'white' }}
              onClick={handleOpenAdd}
            >
              Add Registry
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Box p={2}>
          {loading ? (
            <FuseLoading />
          ) : (
            <Grid container spacing={2} mb={2}>
              <Grid item xs={12}>
                <Paper>
                  <DataGrid
                    rows={registries}
                    columns={registryColumns}
                    sx={(theme) => ({
                      backgroundColor: 'transparent',
                      borderColor: theme.palette.primary.main,
                      '& .MuiDataGrid-columnHeaderRow': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-overlay': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeaders': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeader': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeaderTitle': {
                        fontWeight: 'bold',
                        color: theme.palette.common.white,
                      },
                      '& .MuiDataGrid-row:first-of-type': {
                        // Changed from first-child
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row.Mui-hovered': {
                        backgroundColor: 'transparent',
                      },
                      // Take out the hover colour
                      '& .MuiDataGrid-row:hover': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-cell': {
                        backgroundColor: 'transparent',
                      },
                    })}
                    getRowId={(row) => row.id}
                    showToolbar
                  />
                </Paper>
              </Grid>
            </Grid>
          )}

          <Dialog open={viewMode} onClose={handleDialogClose} maxWidth="md" fullWidth>
            <DialogTitle>
              <Grid container spacing={2} p={2} justifyContent={'space-between'}>
                <Typography variant="h6">Registry Details</Typography>
                <IconButton aria-label="close" onClick={handleDialogClose}>
                  <Close />
                </IconButton>
              </Grid>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Registry Name
                  </Typography>
                  <Typography variant="body1" color="textPrimary">
                    {currentRegistry?.registryName}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Primary Owner
                  </Typography>
                  <Typography variant="body1" color="textPrimary">
                    {employees.find((emp) => emp.userId === currentRegistry?.ownerId)?.lastName ||
                      'N/A'}{' '}
                    {employees.find((emp) => emp.userId === currentRegistry?.ownerId)?.firstName ||
                      'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <hr />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Collaborators
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <StyledRadioGroup
                        onChange={handleModeChange}
                        value={manageSelectedRegistryCollaborators}
                        style={{ marginBottom: 8 }}
                      >
                        <Radio.Button value={false}>View Collaborators</Radio.Button>
                        <Radio.Button value={true}>Manage Collaborators</Radio.Button>
                      </StyledRadioGroup>
                      {manageSelectedRegistryCollaborators ? (
                        <Formik
                          initialValues={{
                            employeeIds: selectedRegistryCollaborators.map(
                              (collab) => collab.userId,
                            ),
                          }}
                          onSubmit={handleCollaboratorsFormSubmit}
                        >
                          {({ isSubmitting, setFieldValue }) => (
                            <Form>
                              <Grid container spacing={2}>
                                <Grid item xs={12}>
                                  <DataGrid
                                    initialState={{
                                      columns: {
                                        columnVisibilityModel: cVisibModel,
                                      },
                                    }}
                                    getRowSpacing={getRowSpacing}
                                    checkboxSelection
                                    keepNonExistentRowsSelected
                                    columns={employeeColumns}
                                    rows={employees.filter(
                                      (emp) =>
                                        !selectedRegistryCollaborators.find(
                                          (collab) => collab.employeeId === emp.userId,
                                        ),
                                    )}
                                    getRowId={(row) => row.userId}
                                    onRowSelectionModelChange={(newSelection) => {
                                      setFieldValue('employeeIds', [...newSelection.ids]);
                                    }}
                                    sx={(theme) => ({
                                      backgroundColor: 'transparent',
                                      borderColor: theme.palette.primary.main,
                                      '& .MuiDataGrid-columnHeaders': {
                                        backgroundColor: 'transparent',
                                        color: theme.palette.common.white,
                                      },
                                      '& .MuiDataGrid-row.Mui-hovered': {
                                        backgroundColor: 'transparent',
                                      },
                                      '& .MuiDataGrid-row': {
                                        border: '1px solid lightgray',
                                      },
                                      // Take out the hover colour
                                      '& .MuiDataGrid-row:hover': {
                                        backgroundColor: 'transparent',
                                      },
                                      // Take out the hover colour
                                      '& .Mui-selected': {
                                        backgroundColor: 'transparent',
                                      },
                                    })}
                                    showToolbar
                                  />
                                </Grid>
                                <Grid item xs={12} container justifyContent="flex-end">
                                  <Button
                                    variant="outlined"
                                    onClick={() => setManageSelectedRegistryCollaborators(false)}
                                    type="button"
                                    sx={{ mr: 1 }}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    variant="contained"
                                    type="submit"
                                    disabled={isSubmitting}
                                    color="primary"
                                  >
                                    {isSubmitting ? 'Submitting..' : 'Submit'}
                                  </Button>
                                </Grid>
                              </Grid>
                            </Form>
                          )}
                        </Formik>
                      ) : (
                        <DataGrid
                          initialState={{
                            columns: {
                              columnVisibilityModel: cVisibModel,
                            },
                          }}
                          getRowSpacing={getRowSpacing}
                          columns={collaboratorsColumns}
                          rows={selectedRegistryCollaborators}
                          getRowId={(row) => row.id}
                          sx={(theme) => ({
                            backgroundColor: 'transparent',
                            borderColor: theme.palette.primary.main,
                            '& .MuiDataGrid-columnHeaders': {
                              backgroundColor: 'transparent',
                              color: theme.palette.common.white,
                            },
                            '& .MuiDataGrid-row.Mui-hovered': {
                              backgroundColor: 'transparent',
                            },
                            '& .MuiDataGrid-row': {
                              border: '1px solid lightgray',
                            },
                            // Take out the hover colour
                            '& .MuiDataGrid-row:hover': {
                              backgroundColor: 'transparent',
                            },
                            // Take out the hover colour
                            '& .Mui-selected': {
                              backgroundColor: 'transparent',
                            },
                          })}
                          showToolbar
                        />
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </DialogContent>
          </Dialog>
          <Dialog open={addMode || editMode} onClose={handleDialogClose} maxWidth="sm" fullWidth>
            <DialogTitle>{editMode ? 'Edit Position' : 'Add Position'}</DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  registryName: currentRegistry?.registryName || '',
                  ownerId: currentRegistry?.ownerId || '',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting, setFieldValue }: FormikProps<Registry>) => {
                  return (
                    <Form>
                      <Grid container spacing={2}>
                        <Grid item xs={12} p={2}>
                          <TextInput
                            label="Name"
                            id="registryName"
                            value={values.registryName}
                            name="registryName"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            label="Registry Owner"
                            id="ownerId"
                            options={employees.map((emp) => ({
                              label: `${emp.lastName}, ${emp.firstName} ${emp.otherNames}`,
                              value: emp.userId,
                            }))}
                            field={'value'}
                            fieldDisplay={'label'}
                            value={values.ownerId}
                            name="ownerId"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid
                          item
                          container
                          justifyContent={'space-between'}
                          alignItems={'center'}
                          xs={12}
                        >
                          <Button variant="outlined" onClick={handleDialogClose} type="button">
                            Cancel
                          </Button>
                          <Button
                            variant="contained"
                            type="submit"
                            disabled={isSubmitting}
                            color="primary"
                          >
                            {isSubmitting ? 'Submitting..' : 'Submit'}
                          </Button>
                        </Grid>
                      </Grid>
                    </Form>
                  );
                }}
              </Formik>
            </DialogContent>
          </Dialog>
        </Box>
      }
    />
  );
}
