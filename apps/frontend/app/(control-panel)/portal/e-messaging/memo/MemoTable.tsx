import { useMemo, useCallback } from 'react';
import { MRT_ColumnDef } from 'material-react-table';
import { MemoResponseDto, MemoStatusEnums } from '@/services/api/types/memo.types';
import {
  Avatar,
  darken,
  lighten,
  ListItemIcon,
  MenuItem,
  Tooltip,
  Typography,
} from '@mui/material';
import Link from '@fuse/core/Link';
import Paper from '@mui/material/Paper';
import DataTable from '@/components/data-table/DataTable';
import MemoStatus from './MemoCategory'; // Using the renamed component
import clsx from 'clsx';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import AvatarGroup from '@mui/material/AvatarGroup';
import { useAuth } from '@/contexts/AuthContext';
import FuseTabs from '@/components/tabs/FuseTabs';
import FuseTab from '@/components/tabs/FuseTab';
import { MemoTabType } from './useMemos';

interface MemoTableProps {
  data: MemoResponseDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  globalFilter: string;
  activeTab: MemoTabType;
  onPaginationChange: (pagination: { pageIndex: number; pageSize: number }) => void;
  onGlobalFilterChange: (filter: string) => void;
  onTabChange: (tab: MemoTabType) => void;
}

function MemoTable({
  data,
  totalCount,
  loading,
  error,
  pagination,
  globalFilter,
  activeTab,
  onPaginationChange,
  onGlobalFilterChange,
  onTabChange,
}: MemoTableProps) {
  const router = useRouter();
  const { employeeDetails } = useAuth();

  const columns = useMemo<MRT_ColumnDef<MemoResponseDto>[]>(
    () => [
      {
        id: 'serialNumber',
        header: 'S/n',
        Cell: ({ row }) => {
          // Calculate serial number based on current page and row index
          const serialNumber = pagination.pageIndex * pagination.pageSize + row.index + 1;
          return <Typography>{serialNumber}</Typography>;
        },
        enableColumnFilter: false,
        enableColumnDragging: false,
        size: 10,
        enableSorting: false,
      },
      {
        accessorKey: 'reference',
        header: 'Ref No',
        Cell: ({ row }) => (
          <Typography
            component={Link}
            to={`/portal/e-messaging/memo/${row.original.id}`}
            role="button"
          >
            {row.original.reference}
          </Typography>
        ),
      },
      {
        accessorKey: 'title',
        header: 'Subject',
        Cell: ({ row }) => <Typography>{row.original.title}</Typography>,
      },
      {
        accessorKey: 'fromEmployee',
        header: 'From',
        Cell: ({ row }) => {
          const employee = row.original.fromEmployee;
          return (
            <div className="flex items-center gap-2">
              <Avatar
                className="mx-0 h-8 w-8 text-sm"
                alt={`${employee.firstName} ${employee.lastName}`}
                src={employee.photoUrl || ''}
              >
                {employee.firstName?.[0]}
                {employee.lastName?.[0]}
              </Avatar>
              <div className="flex flex-col">
                <Typography className="text-sm font-medium">
                  {employee.firstName} {employee.lastName}
                </Typography>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'throughEmployees',
        header: 'Through',
        Cell: ({ row }) => {
          const throughEmployees = row.original.throughEmployees || [];
          if (throughEmployees.length === 0) return <Typography>-</Typography>;

          return (
            <AvatarGroup max={3} className="justify-end">
              {throughEmployees.map((employee, index) => (
                <Tooltip
                  key={employee.userId}
                  title={`${employee.firstName} ${employee.lastName}`}
                  placement="top"
                >
                  <Avatar
                    className="h-8 w-8 border-2 text-sm"
                    alt={`${employee.firstName} ${employee.lastName}`}
                    src={employee.photoUrl || ''}
                  >
                    {employee.firstName?.[0]}
                    {employee.lastName?.[0]}
                  </Avatar>
                </Tooltip>
              ))}
            </AvatarGroup>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'toEmployees',
        header: 'To',
        Cell: ({ row }) => {
          const recipients = row.original.toEmployees || [];
          if (recipients.length === 0) return <Typography>-</Typography>;

          return (
            <AvatarGroup max={3} className="justify-end">
              {recipients.map((employee, index) => (
                <Tooltip
                  key={employee.userId}
                  title={`${employee.firstName} ${employee.lastName}`}
                  placement="top"
                >
                  <Avatar
                    className="h-8 w-8 border-2 text-sm"
                    alt={`${employee.firstName} ${employee.lastName}`}
                    src={employee.photoUrl || ''}
                  >
                    {employee.firstName?.[0]}
                    {employee.lastName?.[0]}
                  </Avatar>
                </Tooltip>
              ))}
            </AvatarGroup>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: 'Created by',
        Cell: ({ row }) => {
          const createdBy = row.original.createdByEmployee;
          const formattedDate = moment(row.original.createdAt).format('DD MMM YYYY');

          return (
            <div className="flex items-center gap-2">
              <Avatar
                className="mx-0 h-8 w-8 text-sm"
                alt={`${createdBy.firstName} ${createdBy.lastName}`}
                src={createdBy.photoUrl || ''}
              >
                {createdBy.firstName?.[0]}
                {createdBy.lastName?.[0]}
              </Avatar>
              <div className="flex flex-col">
                <Typography className="text-sm font-medium">
                  {createdBy.firstName} {createdBy.lastName}
                </Typography>
                <Typography className="text-xs text-gray-500">
                  {formattedDate}
                </Typography>
              </div>
            </div>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: 'Status',
        Cell: ({ row }) => <MemoStatus status={row.original.status} />,
      },
    ],
    [pagination.pageIndex, pagination.pageSize],
  );

  const handlePaginationChange = useCallback(
    (updaterOrValue: any) => {
      onPaginationChange(updaterOrValue);
    },
    [onPaginationChange],
  );

  const handleTabChange = useCallback(
    (_: React.SyntheticEvent, newValue: MemoTabType) => {
      onTabChange(newValue);
    },
    [onTabChange],
  );

  if (error) {
    return (
      <Paper className="flex flex-col flex-auto shadow rounded-2xl overflow-hidden">
        <div className="flex items-center justify-center p-8">
          <Typography color="error">Error loading memos: {error}</Typography>
        </div>
      </Paper>
    );
  }

  return (
    <Paper
      className="flex flex-col flex-auto shadow rounded-2xl overflow-hidden w-full h-full"
      elevation={0}
    >
      {/* Tab Navigation */}
      <div className="px-6 py-4 border-b border-gray-200">
        <FuseTabs value={activeTab} onChange={handleTabChange} aria-label="Memo tabs">
          <FuseTab value="inbox" label="Inbox" />
          <FuseTab value="sent" label="Sent" />
          <FuseTab value="outbox" label="Outbox" />
        </FuseTabs>
      </div>
      <DataTable
        data={loading ? Array(3).fill({}) : data || []}
        columns={columns}
        // Server-side pagination and filtering configuration
        manualPagination
        manualFiltering
        rowCount={totalCount}
        state={{
          isLoading: loading,
          pagination: {
            pageIndex: pagination.pageIndex,
            pageSize: pagination.pageSize,
          },
          globalFilter,
        }}
        onPaginationChange={handlePaginationChange}
        onGlobalFilterChange={onGlobalFilterChange}
        // Enable global filtering only (column filters disabled in DataTable component)
        enableGlobalFilter={true}
        // Enable page size selection
        enablePagination
        paginationDisplayMode="pages"
        muiPaginationProps={{
          rowsPerPageOptions: [10, 25, 50],
          showFirstButton: true,
          showLastButton: true,
        }}
        // Loading skeleton configuration
        muiSkeletonProps={{
          animation: 'wave',
          height: 28,
        }}
        renderRowActionMenuItems={({ closeMenu, row, table }) =>
          [
            <MenuItem
              key={0}
              onClick={() => {
                router.push(`/portal/e-messaging/memo/${row.original.id}`);
              }}
            >
              <ListItemIcon>
                <FuseSvgIcon>heroicons-outline:eye</FuseSvgIcon>
              </ListItemIcon>
              View Memo
            </MenuItem>,
            // Show edit option only for draft memos created by the current user
            row.original.status === MemoStatusEnums.Draft &&
              row.original.createdByEmployee.userId === employeeDetails?.userId && (
                <MenuItem
                  key={1}
                  onClick={() => {
                    closeMenu();
                    router.push(`/portal/e-messaging/memo/edit/${row.original.id}`);
                  }}
                >
                  <ListItemIcon>
                    <FuseSvgIcon>heroicons-outline:pencil-square</FuseSvgIcon>
                  </ListItemIcon>
                  Edit Memo
                </MenuItem>
              ),
          ].filter(Boolean)
        }
        muiTableProps={{ sx: { caption: { captionSide: 'top' } } }}
      />
    </Paper>
  );
}

export default MemoTable;
