import { Injectable, NotFoundException } from '@nestjs/common';
import { WorkflowTypeRepository } from '../repositories';
import { createWorkflowTypeDto, updateWorkflowTypeDto, WorkflowTypeResponseDto } from '../dtos';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class WorkflowTypeService {
  constructor(private readonly workflowTypeRepository: WorkflowTypeRepository) {}

  async create(data: createWorkflowTypeDto): Promise<WorkflowTypeResponseDto> {
    const record = await this.workflowTypeRepository.save(data);
    return plainToInstance(WorkflowTypeResponseDto, record);
  }
  async findOne(id: string): Promise<WorkflowTypeResponseDto> {
    const record = await this.workflowTypeRepository.findOne(id);
    if (!record) throw new NotFoundException('Workflow type not found');
    return plainToInstance(WorkflowTypeResponseDto, record);
  }

  async findAll(): Promise<WorkflowTypeResponseDto[]> {
    const result = await this.workflowTypeRepository.findAll();
    return result.map((item) => plainToInstance(WorkflowTypeResponseDto, item));
  }

  async update(id: string, data: updateWorkflowTypeDto): Promise<WorkflowTypeResponseDto> {
    const existing = await this.findOne(id);
    const updated = await this.workflowTypeRepository.save({ ...existing, ...data });
    return plainToInstance(WorkflowTypeResponseDto, updated);
  }

  async delete(id: string): Promise<void> {
    await this.findOne(id);
    await this.workflowTypeRepository.delete(id);
  }
}
