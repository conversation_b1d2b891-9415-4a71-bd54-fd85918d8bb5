import { BaseRepository } from '@igov/common';
import { MemoHistoryEntity } from '../entity';
import { Injectable, Logger } from '@nestjs/common';
import { IMemoHistory } from '../interfaces';

@Injectable()
export class MemoHistoryRepository extends BaseRepository<MemoHistoryEntity> {
  private readonly logger = new Logger(MemoHistoryRepository.name);
  override entityClassName = MemoHistoryEntity;

  async save(data: IMemoHistory): Promise<MemoHistoryEntity> {
    this.logger.log('Saving memo history...');
    return this.repository.save(data);
  }
}
