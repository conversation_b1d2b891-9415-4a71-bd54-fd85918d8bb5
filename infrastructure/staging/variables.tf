# Variable definitions for Terraform configuration

variable "aws_region" {
  description = "The AWS region to deploy resources in"
  type        = string
  default     = "eu-west-1"
}

variable "environment" {
  description = "The deployment environment (staging, production, etc.)"
  type        = string
  default     = "staging"
}

variable "project_name" {
  description = "The name of the project"
  type        = string
  default     = "igov"
}

variable "instance_type" {
  description = "The EC2 instance type"
  type        = string
  default     = "t3.small"
}

variable "profile" {
  description = "The local aws profile to use"
  type        = string
  default     = "igov-dev"
}

variable "root_volume_size" {
  description = "The root volume size in GB"
  type        = number
  default     = 30
}
