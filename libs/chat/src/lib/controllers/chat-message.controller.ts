import { Controller, Get, Param, Query, UseGuards, Patch } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ChatMessageService } from '../services';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { ChatMessagePaginationDto, ChatMessageResponseDto } from '../dtos';
import { plainToInstance } from 'class-transformer';

@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@ApiTags('Chat Messages')
@Controller('chat-messages')
export class ChatMessageController {
  constructor(private readonly chatMessageService: ChatMessageService) {}

  @Get()
  @ApiOperation({ summary: 'Get User Messages' })
  @ApiResponse({
    status: 200,
    description: 'Messages retrieved successfully',
    type: [ChatMessageResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 409, description: 'Conflict' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  async getUserMessages(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: ChatMessagePaginationDto,
  ): Promise<ChatMessageResponseDto[]> {
    return this.chatMessageService.getUserMessages(jwtPayload.id, query);
  }

  @Get(':receiverId')
  @ApiOperation({ summary: 'Get User Conversations' })
  @ApiResponse({
    status: 200,
    description: 'Messages retrieved successfully',
    type: [ChatMessageResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 409, description: 'Conflict' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  async getConversations(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Param('receiverId') receiverId: string,
    @Query() query: ChatMessagePaginationDto,
  ): Promise<ChatMessageResponseDto[]> {
    const messages = await this.chatMessageService.getUserConservation(
      { user1: jwtPayload.id, user2: receiverId },
      query,
    );
    return messages.map((message) => plainToInstance(ChatMessageResponseDto, message));
  }

  @Patch(':messageId/read')
  @ApiOperation({ summary: 'Mark message as read' })
  @ApiResponse({
    status: 200,
    description: 'Message marked as read successfully',
    type: ChatMessageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Message not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async markAsRead(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Param('messageId') messageId: string,
  ): Promise<ChatMessageResponseDto> {
    const message = await this.chatMessageService.markRead(messageId);
    return plainToInstance(ChatMessageResponseDto, message);
  }
}
