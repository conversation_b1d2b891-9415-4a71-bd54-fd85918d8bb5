//Turkish , Türkçe
RTE_DefaultConfig.text_language = "dil";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "iptal";	//"Cancel"
RTE_DefaultConfig.text_normal = "normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Başlık 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Başlık 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Başlık 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Başlık 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Başlık 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Başlık 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Başlık 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "kapat";	//"Close"
RTE_DefaultConfig.text_bold = "kalın";	//"Bold"
RTE_DefaultConfig.text_italic = "ıtalic";	//"Italic"
RTE_DefaultConfig.text_underline = "altı çizili";	//"Underline"
RTE_DefaultConfig.text_strike = "Grev Hattı";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "üstsimge";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Büyük Harf";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Küçük Harf";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Biçimi Kaldır";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Bağlantı Ekle";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Bağlantıyı Aç";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Bağlantıyı Edit";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Bağlantıyı Kaldır";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Çizgi Yüksekliği";	//"Line Height"
RTE_DefaultConfig.text_indent = "Girinti";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Teklifi Engelle";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Sipariş Listesi";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Sırasız Liste";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Yatay Kural Ekle";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Ek Tarih";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Tablo Ekle";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Resim Ekle";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Video Ekle";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Kod Ekle";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF Oluşturma";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Emoji Ekle";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Özel karakterler";	//"Special characters"
RTE_DefaultConfig.text_characters = "karakter";	//"Characters"
RTE_DefaultConfig.text_fontname = "yazı tipi";	//"Font"
RTE_DefaultConfig.text_fontsize = "boyutu";	//"Size"
RTE_DefaultConfig.text_forecolor = "Metin Rengi";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Arka Renk";	//"Back Color"
RTE_DefaultConfig.text_justify = "haklı";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Sola Yaslama";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Sağ'ı Yaslama";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Merkezi Yaslama";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Tam'ı Yaslama";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Yok'u Yaslama";	//"Justify None"
RTE_DefaultConfig.text_delete = "silmek";	//"Delete"
RTE_DefaultConfig.text_save = "Dosyayı kaydet";	//"Save file"
RTE_DefaultConfig.text_selectall = "Tümünü Seç";	//"Select All"
RTE_DefaultConfig.text_code = "HTML Kodu";	//"HTML Code"
RTE_DefaultConfig.text_preview = "önizleme";	//"Preview"
RTE_DefaultConfig.text_print = "yazdırma";	//"Print"
RTE_DefaultConfig.text_undo = "geri alma";	//"Undo"
RTE_DefaultConfig.text_redo = "yinele";	//"Redo"
RTE_DefaultConfig.text_more = "-nda daha fazla...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Yeni Doc";	//"New Doc"
RTE_DefaultConfig.text_help = "yardım";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Pencereye Sığdır";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Tam Ekran Çıkış";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Görüntü Düzenleyicisi";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Görüntü Stilleri";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Satır Satır Stilleri";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Paragraf Stilleri";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Bağlantı Stilleri";	//"Link Styles"
RTE_DefaultConfig.text_link = "bağlantı";	//"Link"
RTE_DefaultConfig.text_style = "stil";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css Sınıfları";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url'ye Göre";	//"By Url"
RTE_DefaultConfig.text_upload = "yüklemek";	//"Upload"
RTE_DefaultConfig.text_size = "boyutu";	//"Size"
RTE_DefaultConfig.text_text = "metin";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Yeni sekmede aç";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "ınsert";	//"Insert"
RTE_DefaultConfig.text_update = "güncelleştirme";	//"Update"
RTE_DefaultConfig.text_find = "Bul&Amp; Değiştir";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "bul";	//"Find"
RTE_DefaultConfig.text_replacewith = "değiştirmek";	//"Replace"
RTE_DefaultConfig.text_findnext = "sonraki";	//"Next"
RTE_DefaultConfig.text_replaceonce = "değiştirmek";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Tümlerini Değiştir";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Maç Örneği";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Maç Kelime";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Aşağı Taşı";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Yukarı Taşıyın";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Otomatik boyut";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "%100 genişlik";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "%75 genişlik";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "%50 genişlik";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "%25 genişlik";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Boyut Ayarlama";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt metin";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "haklı";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Resim Yazısı";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Hücreleri Birleştirme";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Hücreleri Dikey Bölme";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Hücreleri Yatay Olarak Böl";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Hücre Metin Rengi";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Hücre Arka Rengi";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Üst sıra ekle";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Aşağıdaki Satır Ekle";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Sola Sütun Ekle";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Sütun Sağa Ekle";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Sütunu Sil";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Satırı Sil";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Tabloyu Sil";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Otomatik boyut";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Tablo Üstbilgi";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Yeni bir paragraf ekleme";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "yapıştır";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "yapıştır";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Metni Yapıştır";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Html olarak yapıştır";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Kelime Yapıştır";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "İçeriği aşağıdaki kutuya yapıştırmak için lütfen CTRL+V kullanın. \r\nİçerik otomatik olarak temizlenir.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Yukarı Taşıyın";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Aşağı Taşı";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "yine -lenen";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "silmek";	//"Delete"
RTE_DefaultConfig.text_pmore = "-nda daha fazla..";	//"More.."
RTE_DefaultConfig.text_togglemore = "-nda daha fazla..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Sınırı Geçiş";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "kesilmiş";	//"Cut"
RTE_DefaultConfig.text_copy = "kopya";	//"Copy"
RTE_DefaultConfig.text_copied = "kopya -lanan";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Galeri Ekle";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Belge Ekle";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Şablon Ekle";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "önizleme";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobil";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "tablo";	//"Table"
RTE_DefaultConfig.text_tablecell = "Tablo Hücresi";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Tablo Satırı";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Tablo Sütunu";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "otomatik";	//"Automatic"
RTE_DefaultConfig.text_colormore = "-nda daha fazla";	//"More"
RTE_DefaultConfig.text_colorpicker = "Renk Seçici";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Web Paleti";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Adlandırılmış Renkler";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "temel";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "eklenmesi";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Sürükle ve bırak";	//"Drag and drop"
RTE_DefaultConfig.text_or = "veya";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Yüklemek için tıklayın";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Varsayılan Resim Başlığı";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "arama";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Eklenecek metin bu alan için karakter sınırına ulaştı.";	//"The text to be added has reached the character limit for this field."
