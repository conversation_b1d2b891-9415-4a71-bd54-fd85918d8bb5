server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: docker
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s

    pipeline_stages:
      - docker: {}

    relabel_configs:
      # Get logs path
      - source_labels: [__meta_docker_container_log_stream]
        target_label: __path__
        replacement: /var/lib/docker/containers/__meta_docker_container_id/*.log

      # Assign app labels based on container name
      - source_labels: [__meta_docker_container_name]
        regex: '/nestjs-api-staging'
        target_label: app
        replacement: 'api'

      - source_labels: [__meta_docker_container_name]
        regex: '/nestjs-consumer-staging'
        target_label: app
        replacement: 'consumer'

      - source_labels: [__meta_docker_container_name]
        regex: '/nestjs-websocket-staging'
        target_label: app
        replacement: 'websocket'

      - source_labels: [__meta_docker_container_name]
        regex: '/nextjs-frontend-staging'
        target_label: app
        replacement: 'frontend'

      # Assign 'unknown' to containers that don't match
      - source_labels: [app]
        regex: '^$'
        target_label: app
        replacement: 'unknown'
        action: replace
