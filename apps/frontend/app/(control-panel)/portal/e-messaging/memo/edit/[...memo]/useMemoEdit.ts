import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { memoApiService } from '@/services/api/memoService';
import { UpdateMemoDto, MemoResponseDto } from '@/services/api/types/memo.types';

export interface EditMemoHookParams {
  title: string;
  body: string;
}

export interface UseMemoEditReturn {
  isSubmitting: boolean;
  submissionError: string | null;
  submitMemo: (params: EditMemoHookParams) => Promise<void>;
}

export function useMemoEdit(memoId: string, existingMemo?: MemoResponseDto): UseMemoEditReturn {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const submitMemo = useCallback(
    async (params: EditMemoHookParams) => {
      setIsSubmitting(true);
      setSubmissionError(null);

      try {
        // Prepare memo data - only include fields supported by UpdateMemoDto
        const updateMemoData: UpdateMemoDto = {
          title: params.title,
          body: params.body,
        };

        // Update the memo
        const updatedMemo = await memoApiService.updateMemo(memoId, updateMemoData);

        enqueueSnackbar('Memo updated successfully!', { variant: 'success' });

        // Navigate to the updated memo
        router.push(`/portal/e-messaging/memo/${updatedMemo.id}`);
      } catch (error: any) {
        console.error('Failed to update memo:', error);
        setSubmissionError(error.message || 'Failed to update memo. Please try again.');
        enqueueSnackbar('Failed to update memo', { variant: 'error' });
      } finally {
        setIsSubmitting(false);
      }
    },
    [memoId, enqueueSnackbar, router],
  );

  return {
    isSubmitting,
    submissionError,
    submitMemo,
  };
}
