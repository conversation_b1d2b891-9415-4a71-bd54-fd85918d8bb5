import {
  WorkflowResponseDto,
  AuthorizationStatusEnum,
  WorkflowStatusEnum,
} from '@/services/api/types/workflow.types';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import clsx from 'clsx';
import { Avatar, useTheme, Button } from '@mui/material';
import moment from 'moment';
import React from 'react';

// Helper for status badge color - using AuthorizationStatusEnum values
function getStatusColor(status: AuthorizationStatusEnum) {
  switch (status) {
    case AuthorizationStatusEnum.Pending:
      return 'bg-yellow-100 text-yellow-700';
    case AuthorizationStatusEnum.Approve:
      return 'bg-green-100 text-green-700';
    case AuthorizationStatusEnum.Decline:
      return 'bg-red-100 text-red-700';
    default:
      return 'bg-gray-100 text-gray-700';
  }
}

function getInitials(name: string) {
  return name
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();
}

type RequestTrailProps = {
  workflow: WorkflowResponseDto;
};

function RequestTrail({ workflow }: RequestTrailProps) {
  const theme = useTheme();
  const actors = workflow.actors || [];

  // Find current state
  const currentIdx = actors.findIndex(
    (actor) => actor.authStatus === AuthorizationStatusEnum.Pending,
  );
  const declinedIdx = actors.findIndex(
    (actor) => actor.authStatus === AuthorizationStatusEnum.Decline,
  );

  const isCompleted = workflow.status === WorkflowStatusEnum.Completed;
  const isDeclined = workflow.status === WorkflowStatusEnum.Declined;
  const isFinal = isCompleted || isDeclined;
  const primary = theme.palette.primary.main || '#1976d2';

  // Determine which actors to show
  let visibleActors = actors;
  if (!isFinal) {
    // If pending, show up to current + 1
    if (currentIdx !== -1) {
      visibleActors = actors.slice(0, currentIdx + 2);
    }
  } else if (isDeclined) {
    // If declined, show up to and including the declined actor only
    if (declinedIdx !== -1) {
      visibleActors = actors.slice(0, declinedIdx + 1);
    }
  }

  // Helper to determine if an actor is faded/disabled
  function isFaded(idx: number) {
    // Only the immediate next actor is faded
    if (!isFinal && currentIdx !== -1 && idx === currentIdx + 1) return true;
    if (isDeclined && declinedIdx !== -1 && idx === declinedIdx + 1) return true;
    return false;
  }

  // Helper for line style
  function getLineStyle(idx: number): React.CSSProperties {
    if (isCompleted) {
      return {
        borderLeft: '3px solid #22c55e',
        height: 40,
        margin: '0 auto',
        position: 'relative' as const,
      };
    }
    if (isDeclined && idx < declinedIdx) {
      return {
        borderLeft: '3px solid #ef4444',
        height: 40,
        margin: '0 auto',
        position: 'relative' as const,
      };
    }
    if (isDeclined && idx === declinedIdx) {
      return {
        borderLeft: '3px solid #ef4444',
        height: 40,
        margin: '0 auto',
        position: 'relative' as const,
      };
    }
    // In progress
    return {
      borderLeft: '3px dotted #bdbdbd',
      height: 40,
      margin: '0 auto',
      position: 'relative' as const,
    };
  }

  return (
    <Box className="mt-8 px-6 pb-10">
      <Typography
        variant="h6"
        className="mb-2 text-lg leading-none tracking-tight text-gray-900"
        style={{ textTransform: 'none', background: 'none', padding: 0 }}
      >
        Request Trail
      </Typography>
      <Box className="flex flex-col items-center gap-0 mt-4" style={{ position: 'relative' }}>
        {visibleActors.map((actor, idx) => {
          const isCurrent = idx === currentIdx;
          const isPrev = idx < currentIdx;
          const faded = isFaded(idx);
          const actorName = actor.actor
            ? `${actor.actor.firstName} ${actor.actor.lastName}`
            : 'Unknown';

          return (
            <Box
              key={actor.id || idx}
              className="flex flex-col items-center w-full relative"
              style={{ minHeight: 100 }}
            >
              {/* Node */}
              <Box
                className={clsx(
                  'flex items-center gap-4 p-3 rounded-xl w-full',
                  isCurrent && 'bg-white',
                  isPrev && 'opacity-80',
                  faded && 'opacity-60',
                )}
                style={
                  isCurrent
                    ? {
                        border: `2px solid ${primary}`,
                        boxShadow: `0 0 0 2px ${primary}22`,
                        zIndex: 2,
                      }
                    : { border: '1px solid #eee', zIndex: 2 }
                }
              >
                <Avatar
                  sx={{
                    bgcolor: '#E8E8E8',
                    color: '#222',
                    width: 48,
                    height: 48,
                    fontWeight: 700,
                    fontSize: 22,
                  }}
                >
                  {getInitials(actorName)}
                </Avatar>
                <Box className="flex-1">
                  <Typography variant="subtitle1" className="font-semibold">
                    {actorName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Authorizer
                  </Typography>
                  <Box className="flex items-center gap-2 mt-1">
                    <span
                      className={clsx(
                        'px-2 py-0.5 rounded text-xs font-bold',
                        getStatusColor(actor.authStatus),
                      )}
                    >
                      {actor.authStatus}
                    </span>
                    <Typography variant="caption" color="text.secondary">
                      {actor.actionDate
                        ? moment(actor.actionDate).format('MMM DD, YYYY h:mm A')
                        : ''}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              {/* Vertical line (except after last node) */}
              {idx < visibleActors.length - 1 && (
                <Box
                  style={{
                    width: 0,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'relative',
                    height: 40,
                  }}
                >
                  <Box style={getLineStyle(idx)} />
                </Box>
              )}
            </Box>
          );
        })}
      </Box>
      {isFinal && (
        <Box className="flex justify-end mt-6">
          <Button variant="contained" color="primary" disabled>
            Archive
          </Button>
        </Box>
      )}
    </Box>
  );
}

export default RequestTrail;
