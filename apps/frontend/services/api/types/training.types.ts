import { User as UserResponseDto } from './auth.types';
import { EmployeeResponseDto } from './employee.types';

// Enums
export enum TrainingStatus {
  Draft = 'draft',
  Published = 'published',
  Archived = 'archived',
  Deleted = 'deleted',
}

export enum EmployeeTrainingStatus {
  Enrolled = 'enrolled',
  Ongoing = 'ongoing',
  Completed = 'completed',
}

// Training Module Types
export interface TrainingModuleResponseDto {
  id: string;
  title: string;
  description: string;
  material: string[];
  trainingId: string;
  createdAt: string;
  updatedAt: string;
}

// Training Types - Updated to match actual API response
export interface TrainingResponseDto {
  id: string;
  title: string;
  description: string;
  material: string[];
  userId: string;
  createdBy?: UserResponseDto; // Optional since API doesn't always return this
  ratings: string | number; // API returns strings like "0"
  ratingsCount: string | number; // API returns strings like "0"
  enrolmentCount: string | number; // API returns strings like "0"
  finishedCount: string | number; // API returns strings like "0"
  inProgressCount: string | number; // API returns strings like "0"
  publishedDate: string | null; // API can return null
  status: TrainingStatus;
  createdAt: string;
  updatedAt: string;
  autoEnrollEmployeeIds: string[];
  modules?: TrainingModuleResponseDto; // Optional since API doesn't always return this
}

export interface PaginatedTrainingResponseDto {
  data: TrainingResponseDto[];
  total: number;
}

// Employee Training Types - Updated to match actual API response
export interface EmployeeTrainingResponseDto {
  id: string;
  employeeId: string;
  trainingId: string;
  status: EmployeeTrainingStatus;
  feedback?: string | null; // API can return null
  rating?: number | null; // API can return null
  createdAt: string;
  updatedAt: string;
  // Note: training and employee objects are NOT included in my-trainings response
  training?: TrainingResponseDto; // Optional, only present in some contexts
  employee?: EmployeeResponseDto; // Optional, only present in some contexts
}

export interface PaginatedEmployeeTrainingResponseDto {
  data: EmployeeTrainingResponseDto[];
  total: number;
}

// DTOs for API requests
export interface CreateFeedbackEmployeeTrainingDto {
  feedback: string;
  rating: number; // 1-5
}

// Query Options
export interface TrainingQueryOptions {
  skip?: number;
  limit?: number;
  title?: string;
  status?: TrainingStatus;
}

export interface EmployeeTrainingQueryOptions {
  skip?: number;
  limit?: number;
  employeeId?: string;
  trainingId?: string;
  status?: EmployeeTrainingStatus;
  feedback?: string;
  rating?: number;
}
