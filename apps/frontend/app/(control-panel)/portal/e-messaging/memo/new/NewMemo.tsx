'use client';

import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { generateReferenceNumber } from '@/app/(control-panel)/portal/shared/utils/referenceGenerator';
import { useMemoCreation } from './useMemoCreation';
import { useEmployees } from '@/app/(control-panel)/portal/shared/hooks/useEmployees';

import { useRouter } from 'next/navigation';
import { styled } from '@mui/material/styles';
import FusePageSimple from '@/@fuse/core/FusePageSimple';
import NewMemoHeader from '@/app/(control-panel)/portal/e-messaging/memo/new/NewMemoHeader';
import Card from '@mui/material/Card';
import { motion } from 'motion/react';
import {
  Alert,
  AlertTitle,
  Autocomplete,
  Button,
  CircularProgress,
  Divider,
  FormHelperText,
  InputLabel,
  TextField,
} from '@mui/material';
import { useEffect, useMemo, useRef, useState } from 'react';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import { useDropzone } from 'react-dropzone';
import {
  acceptStyle,
  baseStyle,
  focusedStyle,
  img,
  rejectStyle,
  thumb,
  thumbInner,
  thumbsContainer,
} from './drop-zone-styles';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.text.primary,
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

interface MemoFormData {
  referenceNo: string;
  to: string[];
  through?: string[];
  from: string;
  subject: string;
  body: string;
}

const memoSchema = z.object({
  referenceNo: z.string(),
  to: z.array(z.string()).min(1, 'At least one recipient is required for "To" field.'),
  through: z.array(z.string()).optional(),
  from: z.string().min(1, '"From" field is required.'),
  subject: z.string().min(3, 'Subject must be at least 3 characters long.'),
  body: z.string().min(10, 'Memo body must be at least 10 characters long.'),
});

/**
 * NewMemo component for creating a new memo.
 * It handles form input, validation, file attachments, reference number generation,
 * and submission to the backend API.
 */
function NewMemo() {
  const editorRef = useRef<RTEEditorRef>(null);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset: resetForm,
    formState: { errors, isSubmitting: isRHFSubmitting },
  } = useForm<MemoFormData>({
    resolver: zodResolver(memoSchema),
    defaultValues: {
      referenceNo: '',
      to: [],
      through: [],
      from: '',
      subject: '',
      body: '',
    },
  });

  const { employeeDetails } = useAuth();
  const {
    createMemo: submitMemoToApi,
    isSubmitting: isApiSubmitting,
    submissionError: apiSubmissionError,
    uploadedFiles,
    handleFileChange,
    removeFile,
    attachmentValidationErrors,
    reset: resetMemoCreationHook,
  } = useMemoCreation();

  const {
    groupedEmployeesByDepartment,
    isLoading: isLoadingEmployees,
    error: employeesError,
  } = useEmployees();

  const [generatedRefNo, setGeneratedRefNo] = useState<string>('');
  const [isGeneratingRef, setIsGeneratingRef] = useState<boolean>(true);

  // Helper function to flatten employees with department grouping for Autocomplete
  const getFlattenedEmployeeOptions = (excludeCurrentUser: boolean = false) => {
    if (!groupedEmployeesByDepartment) return [];

    const options: { value: string; label: string; department: string }[] = [];
    
    groupedEmployeesByDepartment.forEach((group) => {
      group.employees.forEach((employee) => {
        // Exclude current user if specified
        if (excludeCurrentUser && employee.value === employeeDetails?.userId) {
          return;
        }
        options.push({
          value: employee.value,
          label: employee.label,
          department: group.departmentName,
        });
      });
    });
    
    return options;
  };

  useEffect(() => {
    if (employeeDetails?.departmentId) {
      setIsGeneratingRef(true);
      generateReferenceNumber(employeeDetails.departmentId)
        .then((refNo) => {
          setGeneratedRefNo(refNo);
          setValue('referenceNo', refNo, { shouldValidate: false });
        })
        .catch((err) => {
          console.error('Failed to generate reference number:', err);
          const fallbackRef = 'REF/ERROR/GEN';
          setGeneratedRefNo(fallbackRef);
          setValue('referenceNo', fallbackRef);
        })
        .finally(() => {
          setIsGeneratingRef(false);
        });
    }
  }, [employeeDetails?.departmentId, setValue]);

  useEffect(() => {
    if (employeeDetails?.userId) {
      setValue('from', employeeDetails.userId, { shouldValidate: true });
    }
  }, [employeeDetails?.userId, setValue]);

  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.svg'], 'application/pdf': ['.pdf'] },
    maxFiles: 5,
    maxSize: 2097152,
    onDrop: (acceptedFiles, fileRejections) => {
      handleFileChange(acceptedFiles, fileRejections);
    },
  });

  const formatFileSize = (sizeInBytes: number) => {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} bytes`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  const smallerThumb = {
    ...thumb,
    width: 60,
    height: 60,
  };

  const smallerThumbInner = {
    ...thumbInner,
    width: 60,
    height: 60,
  };

  const displayedThumbs = uploadedFiles.map((upFile) => (
    <div
      className="flex items-center gap-3 mb-2 p-2 border rounded"
      key={upFile.id}
      style={{ width: '100%' }}
    >
      {/*@ts-ignore*/}
      <div style={smallerThumb}>
        <div style={smallerThumbInner}>
          {upFile.file.type === 'application/pdf' ? (
            <img
              src="/assets/images/icons/pdf-icon.png"
              alt="PDF file"
              style={{ ...img, objectFit: 'contain' as const }}
            />
          ) : (
            <img src={upFile.preview} style={img} alt={upFile.file.name} />
          )}
        </div>
      </div>
      <div className="file-info flex flex-col flex-grow">
        <Typography variant="subtitle1" className="">
          {upFile.file.name}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {upFile.file.type || 'Unknown type'} • {formatFileSize(upFile.file.size)}
        </Typography>
        {upFile.error && (
          <Typography variant="caption" color="error">
            Error: {upFile.error}
          </Typography>
        )}
        {upFile.status === 'uploading' && (
          <Typography variant="caption" color="textSecondary">
            Uploading: {upFile.progress}%
          </Typography>
        )}
        {upFile.status === 'completed' && (
          <Typography variant="caption" color="success.main">
            Uploaded
          </Typography>
        )}
      </div>
      <Button
        size="small"
        color="error"
        onClick={() => removeFile(upFile.id)}
        disabled={isApiSubmitting || upFile.status === 'uploading'}
      >
        Remove
      </Button>
    </div>
  ));

  const dropzoneStyle = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject],
  );

  const handleEditorChange = (content: string) => {
    setValue('body', content, { shouldValidate: true });
  };

  const onSubmit = async (data: MemoFormData) => {
    const success = await submitMemoToApi({
      title: data.subject,
      body: data.body,
      reference: data.referenceNo,
      fromId: data.from,
      recipientIds: data.to,
      throughIds: data.through,
    });

    if (success) {
      resetForm();
      resetMemoCreationHook();
      // Note: Editor content is now cleared automatically via the value prop when form resets
    }
  };

  return (
    <Root
      header={<NewMemoHeader />}
      content={
        <motion.div
          className="md:px-16 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, transition: { delay: 0 } }}
        >
          <Card className="p-4 sm:p-6 max-w-5-xl mb-20">
            {isLoadingEmployees || isGeneratingRef ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                <Typography>Loading form data...</Typography>
              </Box>
            ) : employeesError ? (
              <Box sx={{ padding: 2 }}>
                <Typography color="error">Error loading employees: {employeesError}</Typography>
              </Box>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                {apiSubmissionError && (
                  <Alert severity="error" className="mb-4">
                    <AlertTitle>Memo Creation Failed</AlertTitle>
                    {apiSubmissionError}
                  </Alert>
                )}

                <Controller
                  name="referenceNo"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Reference No."
                      id="ref-no"
                      variant="outlined"
                      fullWidth
                      InputProps={{
                        readOnly: true,
                      }}
                      error={!!errors.referenceNo}
                      helperText={errors.referenceNo?.message}
                    />
                  )}
                />

                <Controller
                  name="to"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      multiple
                      options={getFlattenedEmployeeOptions(true)} // Exclude current user
                      getOptionLabel={(option) => option.label}
                      groupBy={(option) => option.department}
                      value={field.value?.map((userId: string) => 
                        getFlattenedEmployeeOptions(true).find(emp => emp.value === userId)
                      ).filter(Boolean) || []}
                      onChange={(_, newValue) => {
                        field.onChange(newValue.map(emp => emp.value));
                      }}
                      disabled={isLoadingEmployees || isApiSubmitting}
                      loading={isLoadingEmployees}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="To *"
                          className="mt-2 mb-4"
                          error={!!errors.to}
                          helperText={errors.to?.message}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {isLoadingEmployees ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                      renderOption={(props, option) => (
                        <li {...props}>
                          <div>
                            <Typography variant="body2">{option.label}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {option.department}
                            </Typography>
                          </div>
                        </li>
                      )}
                      noOptionsText={
                        isLoadingEmployees 
                          ? "Loading employees..." 
                          : employeesError 
                            ? `Error: ${employeesError}` 
                            : "No employees found"
                      }
                    />
                  )}
                />

                <Controller
                  name="subject"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Subject"
                      id="subject"
                      variant="outlined"
                      fullWidth
                      error={!!errors.subject}
                      helperText={errors.subject?.message}
                      disabled={isApiSubmitting}
                    />
                  )}
                />

                <div className="mt-2 mb-4">
                  <InputLabel required error={!!errors.body} sx={{ mb: 1 }}>
                    Memo Body
                  </InputLabel>
                  <Controller
                    name="body"
                    control={control}
                    render={({ field }) => (
                      <RTEEditor 
                        ref={editorRef} 
                        onChange={handleEditorChange} 
                        value={field.value || ''} 
                      />
                    )}
                  />
                  {errors.body && <FormHelperText error>{errors.body.message}</FormHelperText>}
                </div>

                <div className="mt-2 mb-4">
                  <Typography variant="subtitle1" gutterBottom>
                    Attachments
                  </Typography>
                  <div {...getRootProps({ style: dropzoneStyle })}>
                    <input {...getInputProps()} />
                    <p>Drag 'n' drop some files here, or click to select files</p>
                    <small>Max 5 files, 2MB each. Allowed: images, PDF.</small>
                  </div>
                  {attachmentValidationErrors && attachmentValidationErrors.length > 0 && (
                    <Alert severity="warning" className="mt-2">
                      <AlertTitle>File Issues</AlertTitle>
                      <ul>
                        {attachmentValidationErrors.map((err, index) => (
                          <li key={index}>
                            {err.fileName}: {err.message}
                          </li>
                        ))}
                      </ul>
                    </Alert>
                  )}
                  <aside style={{ ...thumbsContainer, marginTop: '16px' }}>{displayedThumbs}</aside>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2 mb-4">
                  <Controller
                    name="through"
                    control={control}
                    defaultValue={[]}
                    render={({ field }) => (
                      <Autocomplete
                        multiple
                        options={getFlattenedEmployeeOptions(false)} // Don't exclude current user for through field
                        getOptionLabel={(option) => option.label}
                        groupBy={(option) => option.department}
                        value={field.value?.map((userId: string) => 
                          getFlattenedEmployeeOptions(false).find(emp => emp.value === userId)
                        ).filter(Boolean) || []}
                        onChange={(_, newValue) => {
                          field.onChange(newValue.map(emp => emp.value));
                        }}
                        disabled={isLoadingEmployees || isApiSubmitting}
                        loading={isLoadingEmployees}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Through (Optional)"
                            className="mt-2 mb-4"
                            error={!!errors.through}
                            helperText={errors.through?.message}
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {isLoadingEmployees ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <div>
                              <Typography variant="body2">{option.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.department}
                              </Typography>
                            </div>
                          </li>
                        )}
                        noOptionsText={
                          isLoadingEmployees 
                            ? "Loading employees..." 
                            : employeesError 
                              ? `Error: ${employeesError}` 
                              : "No employees found"
                        }
                      />
                    )}
                  />

                  <Controller
                    name="from"
                    control={control}
                    render={({ field }) => (
                      <Autocomplete
                        options={getFlattenedEmployeeOptions(false)} // Don't exclude current user for from field
                        getOptionLabel={(option) => option.label}
                        groupBy={(option) => option.department}
                        value={getFlattenedEmployeeOptions(false).find(emp => emp.value === field.value) || null}
                        onChange={(_, newValue) => {
                          field.onChange(newValue?.value || '');
                        }}
                        disabled={isLoadingEmployees || isApiSubmitting}
                        loading={isLoadingEmployees}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="From *"
                            className="mt-2 mb-4"
                            error={!!errors.from}
                            helperText={errors.from?.message}
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {isLoadingEmployees ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                        renderOption={(props, option) => (
                          <li {...props}>
                            <div>
                              <Typography variant="body2">{option.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.department}
                              </Typography>
                            </div>
                          </li>
                        )}
                        noOptionsText={
                          isLoadingEmployees 
                            ? "Loading employees..." 
                            : employeesError 
                              ? `Error: ${employeesError}` 
                              : "No employees found"
                        }
                      />
                    )}
                  />
                </div>

                <div className="flex items-center justify-end space-x-2 mt-10">
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={isRHFSubmitting || isApiSubmitting || isLoadingEmployees}
                  >
                    {isApiSubmitting ? (
                      <CircularProgress size={15} color="inherit" />
                    ) : (
                      'Submit Memo'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </Card>
        </motion.div>
      }
      scroll="page"
    />
  );
}

export default NewMemo;
