import { useMemo, useCallback } from 'react';
import { MRT_ColumnDef } from 'material-react-table';
import { WorkflowResponseDto, WorkflowStatusEnum } from '@/services/api/types/workflow.types';
import {
  Avatar,
  darken,
  lighten,
  ListItemIcon,
  MenuItem,
  Tooltip,
  Typography,
} from '@mui/material';
import Link from '@fuse/core/Link';
import Paper from '@mui/material/Paper';
import DataTable from '@/components/data-table/DataTable';
import clsx from 'clsx';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import AvatarGroup from '@mui/material/AvatarGroup';
import WorkflowStatus from './WorkflowStatus';
import { stripHtml, getWorkflowDisplayDescription } from './utils/workflowHelpers';
import { useAuth } from '@/contexts/AuthContext';
import FuseTabs from '@/components/tabs/FuseTabs';
import FuseTab from '@/components/tabs/FuseTab';
import { WorkflowTabType } from './useWorkflows';

interface WorkflowTableProps {
  data: WorkflowResponseDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  globalFilter: string;
  activeTab: WorkflowTabType;
  onPaginationChange: (pagination: { pageIndex: number; pageSize: number }) => void;
  onGlobalFilterChange: (filter: string) => void;
  onTabChange: (tab: WorkflowTabType) => void;
}

function WorkflowTable({
  data,
  totalCount,
  loading,
  error,
  pagination,
  globalFilter,
  activeTab,
  onPaginationChange,
  onGlobalFilterChange,
  onTabChange,
}: WorkflowTableProps) {
  const router = useRouter();
  const { employeeDetails } = useAuth();

  // Tab change handler
  const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: string) => {
    onTabChange(newValue as WorkflowTabType);
  }, [onTabChange]);

  // Pagination change handler
  const handlePaginationChange = useCallback((updater: any) => {
    if (typeof updater === 'function') {
      const newPagination = updater(pagination);
      onPaginationChange(newPagination);
    } else {
      onPaginationChange(updater);
    }
  }, [pagination, onPaginationChange]);

  const columns = useMemo<MRT_ColumnDef<WorkflowResponseDto>[]>(
    () => [
      {
        id: 'serialNumber',
        header: 'S/n',
        Cell: ({ row, table }) => {
          const pageRows = table.getRowModel().rows;
          const visibleIndex = pageRows.findIndex((r) => r.id === row.id);
          return <Typography>{visibleIndex + 1}</Typography>;
        },
        enableColumnFilter: false,
        enableColumnDragging: false,
        size: 10,
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: 'Date',
        Cell: ({ row }) => (
          <Typography>{moment(row.original.createdAt).format('DD MMM YYYY')}</Typography>
        ),
      },
      {
        accessorKey: 'initiatedBy',
        header: 'Initiated By',
        Cell: ({ row }) => {
          const initiator = row.original.initiatedBy;
          const displayName = initiator 
            ? `${initiator.firstName} ${initiator.lastName}`
            : 'Unknown';
          
          return (
            <div className="flex items-center gap-2">
              <Avatar
                sx={{
                  width: 24,
                  height: 24,
                  fontSize: 10,
                  bgcolor: '#E8E8E8',
                  color: '#222',
                  fontWeight: 700,
                }}
              >
                {initiator?.firstName && initiator?.lastName
                  ? `${initiator.firstName[0]}${initiator.lastName[0]}`
                  : 'U'}
              </Avatar>
              <div className="flex flex-col">
                <Typography variant="body2" className="truncate">
                  {displayName}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {moment(row.original.createdAt).format('DD MMM YYYY')}
                </Typography>
              </div>
            </div>
          );
        },
        size: 180,
      },
      {
        accessorKey: 'amount',
        header: 'Amount',
        Cell: ({ row }) => (
          <Typography>
            ₦
            {row.original.amount.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Typography>
        ),
      },
      {
        accessorKey: 'title',
        header: 'Title',
        Cell: ({ row }) => <Typography>{row.original.title}</Typography>,
      },
      {
        accessorKey: 'workflowType',
        header: 'Type',
        Cell: ({ row }) => <Typography>{row.original.workflowType?.workflowType || 'N/A'}</Typography>,
      },
      {
        accessorKey: 'status',
        header: 'Status',
        Cell: ({ row }) => <WorkflowStatus status={row.original.status} />,
      },
    ],
    [],
  );

  // Handle error state
  if (error) {
    return (
      <Paper className="flex flex-col flex-auto shadow rounded-2xl overflow-hidden">
        <div className="flex items-center justify-center p-8">
          <Typography color="error">Error loading workflows: {error}</Typography>
        </div>
      </Paper>
    );
  }

  return (
    <Paper
      className="flex flex-col flex-auto shadow rounded-2xl overflow-hidden w-full h-full"
      elevation={0}
    >
      {/* Tab Navigation */}
      <div className="px-6 py-4 border-b border-gray-200">
        <FuseTabs value={activeTab} onChange={handleTabChange} aria-label="Workflow tabs">
          <FuseTab value="assigned" label="Assigned" />
          <FuseTab value="initiated" label="Initiated" />
          <FuseTab value="all" label="All" />
        </FuseTabs>
      </div>
      <DataTable
        data={loading ? Array(3).fill({}) : data || []}
        columns={columns}
        // Server-side pagination and filtering configuration
        manualPagination
        manualFiltering
        rowCount={totalCount}
        state={{
          isLoading: loading,
          pagination: {
            pageIndex: pagination.pageIndex,
            pageSize: pagination.pageSize,
          },
          globalFilter,
        }}
        onPaginationChange={handlePaginationChange}
        onGlobalFilterChange={onGlobalFilterChange}
        // Enable global filtering only (column filters disabled in DataTable component)
        enableGlobalFilter={true}
        // Enable page size selection
        enablePagination
        paginationDisplayMode="pages"
        muiPaginationProps={{
          rowsPerPageOptions: [10, 25, 50],
          showFirstButton: true,
          showLastButton: true,
        }}
        // Loading skeleton configuration
        muiSkeletonProps={{
          animation: 'wave',
          height: 56,
        }}
        renderRowActionMenuItems={({ closeMenu, row, table }) => [
          <MenuItem
            key={0}
            onClick={() => {
              router.push(`/portal/e-office/workflow/${row.original.id}`);
              closeMenu();
            }}
          >
            <ListItemIcon>
              <FuseSvgIcon>heroicons-outline:eye</FuseSvgIcon>
            </ListItemIcon>
            View Workflow
          </MenuItem>,
          // Edit functionality temporarily disabled
          // View PDF - Available for all workflows
          <MenuItem
            key={2}
            onClick={() => {
              router.push(`/portal/e-office/workflow/pdf/${row.original.id}`);
              closeMenu();
            }}
          >
            <ListItemIcon>
              <FuseSvgIcon>heroicons-outline:document-text</FuseSvgIcon>
            </ListItemIcon>
            View PDF
          </MenuItem>,
        ]}
        muiTableProps={{ sx: { caption: { captionSide: 'top' } } }}
      />
    </Paper>
  );
}

export default WorkflowTable;
