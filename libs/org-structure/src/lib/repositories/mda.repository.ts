import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { MDA } from '../entities';
import { IMda } from '../interfaces';

@Injectable()
export class MdaRepository extends BaseRepository<MDA> {
  private readonly logger = new Logger(MdaRepository.name);
  override entityClassName = MDA;

  findOne(id: string): Promise<MDA | null> {
    return this.repository.findOne({ where: { id } });
  }

  save(mda: IMda): Promise<MDA> {
    return this.repository.save(mda);
  }

  findAll(): Promise<MDA[]> {
    return this.repository.find();
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}
