import { Module } from '@nestjs/common';
import {
  EmployeeCorrespondenceRepository,
  GDriveFileRepository,
  GDriveFolderRepository,
  GDriveShareRepository,
  RegistryCollaboratorRepository,
  RegistryRepository,
} from './repositories';
import {
  CorrespondenceService,
  GDriveFileService,
  GDriveFolderService,
  GDriveShareService,
  RegistryCollaboratorService,
  RegistryService,
} from './services';
import {
  GDriveFileController,
  GDriveFolderController,
  GDriveShareController,
  RegistryController,
  RegistrySelfServiceController,
} from './controllers';
import { DatabaseModule } from '@igov/common';
import {
  CorrespondenceEntity,
  EmployeeCorrespondenceEntity,
  GDriveFileEntity,
  GDriveFolderEntity,
  GDriveShareEntity,
  RegistryCollaboratorEntity,
  RegistryEntity,
} from './entities';
import { CorrespondenceRepository } from './repositories/correspondence.repository';

@Module({
  imports: [
    DatabaseModule.forFeature([
      GDriveFileEntity,
      GDriveShareEntity,
      GDriveFolderEntity,
      RegistryEntity,
      RegistryCollaboratorEntity,
      CorrespondenceEntity,
      EmployeeCorrespondenceEntity,
    ]),
  ],
  controllers: [
    GDriveFileController,
    GDriveShareController,
    GDriveFolderController,
    RegistryController,
    RegistrySelfServiceController,
  ],
  providers: [
    GDriveFileService,
    GDriveShareService,
    GDriveFolderService,
    GDriveFolderRepository,
    GDriveFileRepository,
    GDriveShareRepository,
    RegistryService,
    CorrespondenceRepository,
    EmployeeCorrespondenceRepository,
    RegistryCollaboratorRepository,
    RegistryRepository,
    CorrespondenceService,
    RegistryCollaboratorService,
  ],
  exports: [
    GDriveFileService,
    GDriveShareService,
    GDriveFolderService,
    GDriveFolderRepository,
    GDriveFileRepository,
    GDriveShareRepository,
    RegistryRepository,
    RegistryService,
    CorrespondenceRepository,
    EmployeeCorrespondenceRepository,
    RegistryCollaboratorRepository,
    RegistryRepository,
    CorrespondenceService,
    RegistryCollaboratorService,
  ],
})
export class GDriveModule {}
