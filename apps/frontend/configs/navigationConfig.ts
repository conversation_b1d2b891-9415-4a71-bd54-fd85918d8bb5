import i18n from '@i18n';
import { FuseNavItemType } from '@fuse/core/FuseNavigation/types/FuseNavItemType';
import ar from './navigation-i18n/ar';
import en from './navigation-i18n/en';
import tr from './navigation-i18n/tr';

i18n.addResourceBundle('en', 'navigation', en);
i18n.addResourceBundle('tr', 'navigation', tr);
i18n.addResourceBundle('ar', 'navigation', ar);

/**
 * The navigationConfig object is an array of navigation items for the Fuse application.
 */
const navigationConfig: FuseNavItemType[] = [
  {
    id: 'e-messaging',
    title: 'e-Messaging',
    subtitle: 'Messaging',
    type: 'group',
    icon: 'heroicons-outline:chat-bubble-oval-left-ellipsis',
    children: [
      {
        id: 'e-messaging.memo',
        title: 'Memo',
        type: 'item',
        icon: 'heroicons-outline:document-text',
        url: '/portal/e-messaging/memo',
      },
      {
        id: 'e-messaging.circular',
        title: 'Circular',
        type: 'item',
        icon: 'heroicons-outline:document',
        url: '/portal/e-messaging/circular',
      },
      {
        id: 'e-messaging.chat',
        title: 'Chat',
        type: 'item',
        icon: 'heroicons-outline:chat-bubble-left-right',
        url: '/portal/e-messaging/chat',
      },
      {
        id: 'e-messaging.email',
        title: 'Email',
        type: 'item',
        icon: 'heroicons-outline:at-symbol',
        url: '/portal/e-messaging/email',
      },
    ],
  },
  {
    id: 'communication',
    title: 'Communication',
    subtitle: 'Communication',
    type: 'group',
    icon: 'heroicons-outline:chat-bubble-bottom-center-text',
    children: [
      {
        id: 'communication.announcement',
        title: 'Announcement',
        type: 'item',
        icon: 'heroicons-outline:megaphone',
        url: '/portal/communication/announcement',
      },
      {
        id: 'communication.query',
        title: 'Query',
        type: 'item',
        icon: 'heroicons-outline:hand-thumb-down',
        url: '/portal/communication/query',
      },
    ],
  },
  {
    id: 'e-office',
    title: 'e-Office',
    subtitle: 'Office',
    type: 'group',
    icon: 'heroicons-outline:briefcase',
    children: [
      {
        id: 'e-office.workflow',
        title: 'Workflow',
        type: 'item',
        icon: 'heroicons-outline:arrow-path-rounded-square',
        url: '/portal/e-office/workflow',
      },
      {
        id: 'e-office.task',
        title: 'Task',
        type: 'item',
        icon: 'heroicons-outline:rectangle-stack',
        url: '/portal/e-office/task',
      },
      {
        id: 'e-office.training',
        title: 'Training',
        type: 'item',
        icon: 'heroicons-outline:presentation-chart-line',
        url: '/portal/e-office/training',
      },
      {
        id: 'e-office.gdoc',
        title: 'GDoc',
        type: 'item',
        icon: 'heroicons-outline:document-text',
        url: '/portal/e-office/gdoc',
      },
      {
        id: 'e-office.g-drive',
        title: 'GDrive',
        type: 'item',
        icon: 'heroicons-outline:cloud-arrow-up',
        url: '/portal/e-office/g-drive',
      },
      {
        id: 'e-office.e-meeting',
        title: 'e-Meeting',
        type: 'item',
        icon: 'heroicons-outline:video-camera',
        url: '/portal/e-office/e-meeting',
      },
    ],
  },
  {
    id: 'correspondence',
    title: 'Correspondence',
    subtitle: 'Correspondence',
    type: 'group',
    icon: 'heroicons-outline:archive-box',
    children: [
      {
        id: 'correspondence.incoming',
        title: 'Incoming',
        type: 'item',
        icon: 'heroicons-outline:archive-box-arrow-down',
        url: '/portal/incoming',
      },
      {
        id: 'correspondence.outgoing',
        title: 'Outgoing',
        type: 'item',
        icon: 'heroicons-outline:archive-box-x-mark',
        url: '/portal/outgoing',
      },
    ],
  },
  {
    id: 'service-forms',
    title: 'Service Forms',
    subtitle: 'Service Forms',
    type: 'group',
    icon: 'heroicons-outline:document-check',
    children: [
      {
        id: 'service-forms.cash-retirement',
        title: 'Cash Retirement',
        type: 'item',
        icon: 'heroicons-outline:banknotes',
        url: '/portal/cash-retirement',
      },
      {
        id: 'service-forms.leave-request',
        title: 'Leave Request',
        type: 'item',
        icon: 'heroicons-outline:paper-airplane',
        url: '/portal/leave-request',
      },
      {
        id: 'service-forms.fleet-request',
        title: 'Fleet Request',
        type: 'item',
        icon: 'heroicons-outline:wrench-screwdriver',
        url: '/portal/fleet-request',
      },
    ],
  },
  {
    id: 'settings',
    title: 'Settings',
    type: 'item',
    icon: 'heroicons-outline:cog-6-tooth',
    url: '/portal/settings',
  },
  {
    id: 'reminders',
    title: 'Reminders',
    type: 'item',
    icon: 'heroicons-outline:bell-alert',
    url: '/portal/reminders',
  },
];

export default navigationConfig;
