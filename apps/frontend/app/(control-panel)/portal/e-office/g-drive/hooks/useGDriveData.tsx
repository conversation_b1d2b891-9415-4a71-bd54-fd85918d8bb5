import { useParams, useSearchParams } from 'next/navigation';
import _ from 'lodash';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { useEffect, useMemo, useState, useCallback, useReducer } from 'react';
import {
  selectSelectedItemId,
  selectSearchQuery,
  selectViewMode,
  setViewMode as setViewModeAction,
} from '../gDriveSlice';
import { gDriveApiService } from '@/services/api/gDriveService';
import {
  GDriveFolderResponseDto,
  GDriveFileResponseDto,
  GDriveShareResponseDto,
  GDriveFolderQueryOptions,
  GDriveFileQueryOptions,
} from '@/services/api/types/g-drive.types';

interface PaginationState {
  itemsPerPage: number;
  totalDisplayed: number;
  folderOffset: number;
  fileOffset: number;
  hasMoreFolders: boolean;
  hasMoreFiles: boolean;
  allFoldersFetched: boolean;
  allFilesFetched: boolean;
}

interface UseGDriveDataState {
  folders: GDriveFolderResponseDto[];
  files: GDriveFileResponseDto[];
  sharedItems: GDriveShareResponseDto[];
  loading: boolean;
  error: string | null;
  pagination: PaginationState;
}

interface UseGDriveDataActions {
  refetch: () => void;
  loadMore: () => Promise<void>;
  createFolder: (folderName: string) => Promise<void>;
  renameFolder: (folderId: string, newName: string) => Promise<void>;
  cacheFolderName: (folderId: string, folderName: string) => void;
}

/**
 * Custom hook to manage and provide GDrive data.
 * Integrated with real API service.
 */
function useGDriveData(): UseGDriveDataState &
  UseGDriveDataActions & {
    selectedItem: GDriveFolderResponseDto | GDriveFileResponseDto | null;
    selectedItemId: string | null;
    path: GDriveFolderResponseDto[];
    currentFolderId: string | null;
    searchQuery: string;
    viewMode: 'personal' | 'shared-incoming' | 'shared-outgoing';
    displayedItems: (GDriveFolderResponseDto | GDriveFileResponseDto)[];
    hasMore: boolean;
  } {
  const dispatch = useAppDispatch();
  const routeParams = useParams<{ folderId?: string[] }>();
  const searchParams = useSearchParams();
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  // Get state from Redux store
  const searchQuery = useAppSelector(selectSearchQuery);
  const viewMode = useAppSelector(selectViewMode);
  
  // Handle URL query params for view mode
  useEffect(() => {
    const viewFromUrl = searchParams.get('view');
    
    if (viewFromUrl === 'shared-incoming') {
      dispatch(setViewModeAction('shared-incoming'));
    } else if (viewFromUrl === 'shared-outgoing') {
      dispatch(setViewModeAction('shared-outgoing'));
    } else if (!viewFromUrl) {
      dispatch(setViewModeAction('personal'));
    }
  }, [searchParams, dispatch]);

  // Determine the current folder ID from the URL.
  const currentFolderId = useMemo(() => {
    const folderIdSegments = routeParams?.folderId;
    return folderIdSegments && folderIdSegments.length > 0
      ? folderIdSegments[folderIdSegments.length - 1]
      : null;
  }, [routeParams?.folderId]);

  const [state, setState] = useState<UseGDriveDataState>({
    folders: [],
    files: [],
    sharedItems: [],
    loading: true,
    error: null,
    pagination: {
      itemsPerPage: 20,
      totalDisplayed: 0,
      folderOffset: 0,
      fileOffset: 0,
      hasMoreFolders: true,
      hasMoreFiles: true,
      allFoldersFetched: false,
      allFilesFetched: false,
    },
  });

  const [pathState, setPathState] = useState<GDriveFolderResponseDto[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const loadNextBatch = useCallback(
    async (isLoadMore: boolean) => {
      try {
        setState((currentState) => {
          const asyncLoad = async () => {
            const { pagination } = currentState;
            let itemsStillNeeded = pagination.itemsPerPage;


            // First priority: Load folders if we haven't fetched them all
            if (!pagination.allFoldersFetched && itemsStillNeeded > 0) {
              const folderResponse = currentFolderId
                ? await gDriveApiService.getSubFolders({
                    limit: itemsStillNeeded + 10, // Get extra to check if more exist
                    skip: pagination.folderOffset,
                    search: searchQuery || undefined,
                    parentId: currentFolderId,
                  })
                : await gDriveApiService.getRootFolders({
                    limit: itemsStillNeeded + 10,
                    skip: pagination.folderOffset,
                    search: searchQuery || undefined,
                  });

              const fetchedFolders = Array.isArray(folderResponse.data) ? folderResponse.data : [];
              const newFolders = fetchedFolders.slice(0, itemsStillNeeded);
              const allFoldersFetched = fetchedFolders.length <= itemsStillNeeded;
              itemsStillNeeded -= newFolders.length;


              setState((prev) => ({
                ...prev,
                folders: isLoadMore ? [...prev.folders, ...newFolders] : newFolders,
                pagination: {
                  ...prev.pagination,
                  folderOffset: prev.pagination.folderOffset + newFolders.length,
                  allFoldersFetched,
                  totalDisplayed: isLoadMore
                    ? prev.pagination.totalDisplayed + newFolders.length
                    : newFolders.length,
                },
              }));
            }

            // Second priority: Load files if we still need items and haven't fetched all files
            if (!pagination.allFilesFetched && itemsStillNeeded > 0) {
              const fileResponse = currentFolderId
                ? await gDriveApiService.getFilesFromFolder(currentFolderId, {
                    limit: itemsStillNeeded + 10,
                    skip: pagination.fileOffset,
                    search: searchQuery || undefined,
                  })
                : await gDriveApiService.getRootFiles({
                    limit: itemsStillNeeded + 10,
                    skip: pagination.fileOffset,
                    search: searchQuery || undefined,
                  });

              const fetchedFiles = Array.isArray(fileResponse.data) ? fileResponse.data : [];
              const newFiles = fetchedFiles.slice(0, itemsStillNeeded);
              const allFilesFetched = fetchedFiles.length <= itemsStillNeeded;


              setState((prev) => ({
                ...prev,
                files: isLoadMore ? [...prev.files, ...newFiles] : newFiles,
                pagination: {
                  ...prev.pagination,
                  fileOffset: prev.pagination.fileOffset + newFiles.length,
                  allFilesFetched,
                  totalDisplayed: prev.pagination.totalDisplayed + newFiles.length,
                },
                loading: false,
              }));
            } else {
              setState((prev) => ({ ...prev, loading: false }));
            }
          };

          asyncLoad().catch((error: any) => {
            console.error('=== Load Batch Error ===', error);
            setState((prev) => ({
              ...prev,
              loading: false,
              error: error.message || 'Failed to load data',
            }));
          });

          return currentState;
        });
      } catch (error: any) {
        console.error('=== Load Batch Error ===', error);
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error.message || 'Failed to load data',
        }));
      }
    },
    [currentFolderId, searchQuery],
  );

  const loadSharedItems = useCallback(async (isLoadMore: boolean) => {
    try {
      setState((currentState) => {
        const asyncLoad = async () => {
          const { pagination } = currentState;
          const itemsNeeded = pagination.itemsPerPage;
          
          // Determine which API to call based on view mode
          const isIncoming = viewMode === 'shared-incoming';
          
          const sharedResponse = isIncoming
            ? await gDriveApiService.getIncomingShares({
                limit: itemsNeeded + 10, // Get extra to check if more exist
                skip: pagination.folderOffset, // Reuse folderOffset for shared items
                search: searchQuery || undefined,
              })
            : await gDriveApiService.getOutgoingShares({
                limit: itemsNeeded + 10,
                skip: pagination.folderOffset,
                search: searchQuery || undefined,
              });

          const fetchedShares = Array.isArray(sharedResponse.data) ? sharedResponse.data : [];
          const newShares = fetchedShares.slice(0, itemsNeeded);
          const allSharesFetched = fetchedShares.length <= itemsNeeded;

          setState((prev) => ({
            ...prev,
            sharedItems: isLoadMore ? [...prev.sharedItems, ...newShares] : newShares,
            pagination: {
              ...prev.pagination,
              folderOffset: prev.pagination.folderOffset + newShares.length,
              allFoldersFetched: allSharesFetched,
              allFilesFetched: allSharesFetched, // For shared items, both mean the same
              totalDisplayed: isLoadMore
                ? prev.pagination.totalDisplayed + newShares.length
                : newShares.length,
            },
            loading: false,
          }));
        };

        asyncLoad().catch((error: any) => {
          console.error('=== Load Shared Items Error ===', error);
          setState((prev) => ({
            ...prev,
            loading: false,
            error: error.message || 'Failed to load shared items',
          }));
        });

        return currentState;
      });
    } catch (error: any) {
      console.error('=== Load Shared Items Error ===', error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error.message || 'Failed to load shared items',
      }));
    }
  }, [viewMode, searchQuery]);

  const fetchData = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      if (viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
        // Fetch shared items with pagination
        setState((prev) => ({
          ...prev,
          folders: [],
          files: [],
          pagination: {
            itemsPerPage: 20,
            totalDisplayed: 0,
            folderOffset: 0,
            fileOffset: 0,
            hasMoreFolders: true,
            hasMoreFiles: true,
            allFoldersFetched: false,
            allFilesFetched: false,
          },
        }));

        await loadSharedItems(false);
      } else {
        // Personal mode - start with initial load of 20 items
        // Start fresh
        setState((prev) => ({
          ...prev,
          folders: [],
          files: [],
          pagination: {
            itemsPerPage: 20,
            totalDisplayed: 0,
            folderOffset: 0,
            fileOffset: 0,
            hasMoreFolders: true,
            hasMoreFiles: true,
            allFoldersFetched: false,
            allFilesFetched: false,
          },
        }));

        // Load first batch of 20 items (folders first)
        await loadNextBatch(false);
      }
    } catch (error: any) {
      console.error('=== GDrive API Error ===', error);
      setState((prev) => ({
        ...prev,
        folders: [],
        files: [],
        sharedItems: [],
        loading: false,
        error: error.message || 'Failed to fetch GDrive data',
      }));
    }
  }, [currentFolderId, searchQuery, viewMode, refreshTrigger, loadNextBatch, loadSharedItems]);

  // State to track folder names for breadcrumbs
  const [folderNamesCache, setFolderNamesCache] = useState<Record<string, string>>({});

  // Cache folder names when we load folders data
  useEffect(() => {
    if (state.folders.length > 0) {
      const newCacheEntries: Record<string, string> = {};
      state.folders.forEach((folder) => {
        newCacheEntries[folder.id] = folder.folderName;
      });

      setFolderNamesCache((prev) => ({
        ...prev,
        ...newCacheEntries,
      }));
    }
  }, [state.folders]);

  // Build breadcrumb path
  const buildPath = useCallback(() => {
    if (!currentFolderId || viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
      setPathState([]);
      return;
    }

    // Get folder name from cache, fallback to 'Folder' if not found
    const folderName = folderNamesCache[currentFolderId] || 'Folder';

    setPathState([
      {
        id: currentFolderId,
        folderName,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentId: null,
        archive: false,
        employeeId: '',
        employee: {
          id: '',
          firstName: '',
          lastName: '',
          email: '',
          companyId: '',
        },
      } as GDriveFolderResponseDto,
    ]);
  }, [currentFolderId, viewMode, folderNamesCache]);

  // Function to manually cache a folder name (for when navigating)
  const cacheFolderName = useCallback((folderId: string, folderName: string) => {
    setFolderNamesCache((prev) => ({
      ...prev,
      [folderId]: folderName,
    }));
  }, []);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Build path when current folder changes or data is fetched
  useEffect(() => {
    buildPath();
  }, [buildPath, state.folders]);

  const selectedItemId = useAppSelector(selectSelectedItemId);
  const selectedItem = useMemo(() => {
    if (!selectedItemId) return null;

    // Check in regular folders and files first
    const folder = state.folders.find((f) => f.id === selectedItemId);
    if (folder) return folder;

    const file = state.files.find((f) => f.id === selectedItemId);
    if (file) return file;

    // For shared views, also check in shared items
    if (viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
      const sharedItem = state.sharedItems.find((s) => {
        const item = s.folder || s.file;
        return item?.id === selectedItemId;
      });
      if (sharedItem) {
        return sharedItem.folder || sharedItem.file;
      }
    }

    return null;
  }, [state.folders, state.files, state.sharedItems, selectedItemId, viewMode]);

  const refetch = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  const createFolder = useCallback(
    async (folderName: string) => {
      try {
        await gDriveApiService.createFolder({
          folderName,
          parentId: currentFolderId || undefined,
        });
        refetch();
      } catch (error: any) {
        throw error;
      }
    },
    [currentFolderId, refetch],
  );

  const renameFolder = useCallback(
    async (folderId: string, newName: string) => {
      try {
        await gDriveApiService.updateFolder(folderId, {
          folderName: newName,
        });
        refetch();
      } catch (error: any) {
        throw error;
      }
    },
    [refetch],
  );

  const loadMore = useCallback(async () => {
    if (viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
      await loadSharedItems(true);
    } else {
      await loadNextBatch(true);
    }
  }, [loadNextBatch, loadSharedItems, viewMode]);

  // Compute displayed items (folders first, then files)
  const displayedItems = useMemo(() => {
    if (viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
      return []; // Shared items are handled separately in the UI
    }
    return [...state.folders, ...state.files];
  }, [state.folders, state.files, viewMode]);

  // Compute if there are more items to load
  const hasMore = useMemo(() => {
    if (viewMode === 'shared-incoming' || viewMode === 'shared-outgoing') {
      return !state.pagination.allFoldersFetched; // For shared items, we use folderOffset tracking
    }
    return !state.pagination.allFoldersFetched || !state.pagination.allFilesFetched;
  }, [state.pagination.allFoldersFetched, state.pagination.allFilesFetched, viewMode]);

  return {
    ...state,
    selectedItem,
    selectedItemId,
    path: pathState,
    currentFolderId,
    searchQuery,
    viewMode,
    displayedItems,
    hasMore,
    refetch,
    loadMore,
    createFolder,
    renameFolder,
    cacheFolderName,
  };
}

export default useGDriveData;
