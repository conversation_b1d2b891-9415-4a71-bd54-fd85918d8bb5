'use client';

import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { useCircularEdit } from './useCircularEdit';
import { useCircularDetail } from '../../[...circular]/useCircularDetail';
import { useDepartments } from '@/app/(control-panel)/portal/shared/hooks/useDepartments';

import { useParams, useRouter } from 'next/navigation';
import { styled } from '@mui/material/styles';
import FusePageSimple from '@/@fuse/core/FusePageSimple';
import EditCircularHeader from './EditCircularHeader';
import Card from '@mui/material/Card';
import { motion } from 'motion/react';
import {
  Alert,
  AlertTitle,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  FormHelperText,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useRef, useMemo } from 'react';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import Box from '@mui/material/Box';
import { CircularStatusEnums } from '@/services/api/types/circular.types';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.text.primary,
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

interface CircularFormData {
  referenceNo: string;
  from: string;
  subject: string;
  body: string;
}

const circularSchema = z.object({
  referenceNo: z.string(),
  from: z.string(),
  subject: z.string().min(1, 'Subject is required.'),
  body: z.string().min(1, 'Body is required.'),
});

function EditCircular() {
  const editorRef = useRef<RTEEditorRef>(null);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset: resetForm,
    formState: { errors, isSubmitting: isRHFSubmitting },
  } = useForm<CircularFormData>({
    resolver: zodResolver(circularSchema),
    defaultValues: {
      referenceNo: '',
      from: '',
      subject: '',
      body: '',
    },
  });

  const params = useParams();
  const router = useRouter();
  const [circularId] = params.circular as string;

  const { circular, isLoading: loadingCircular, error: circularError } = useCircularDetail(circularId);
  const { employeeDetails } = useAuth();

  const {
    submitCircular: submitCircularToApi,
    isSubmitting: isApiSubmitting,
    submissionError: apiSubmissionError,
  } = useCircularEdit(circularId, circular);

  const {
    departments,
    isLoading: isLoadingDepartments,
    error: departmentsError,
  } = useDepartments();

  // Check if circular can be edited (must be draft status and created by current user)
  const canEdit =
    circular?.status === CircularStatusEnums.DRAFT &&
    circular?.fromEmployee.userId === employeeDetails?.userId;

  // Populate form with existing circular data
  useEffect(() => {
    if (circular && canEdit) {
      resetForm({
        referenceNo: circular.reference || '',
        from: circular.fromEmployee?.userId || '',
        subject: circular.title || '',
        body: circular.body || '',
      });

      // Note: Editor content is now set via value prop, no need for setContent call
    }
  }, [circular, canEdit, resetForm]);

  // Redirect if circular cannot be edited
  useEffect(() => {
    if (circular && !canEdit) {
      router.push(`/portal/e-messaging/circular/${circularId}`);
    }
  }, [circular, canEdit, router, circularId]);

  const handleEditorChange = (content: string) => {
    setValue('body', content, { shouldValidate: true });
  };

  const onSubmit = async (data: CircularFormData) => {
    await submitCircularToApi({
      title: data.subject,
      body: data.body,
    });
  };

  // Helper function to get fromEmployee name
  const getFromEmployeeName = () => {
    if (circular?.fromEmployee) {
      return `${circular.fromEmployee.firstName} ${circular.fromEmployee.lastName}`;
    }
    return 'Employee';
  };

  return (
    <Root
      header={<EditCircularHeader />}
      content={
        <motion.div
          className="md:px-16 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, transition: { delay: 0 } }}
        >
          <Card className="p-4 sm:p-6 max-w-5-xl mb-20">
            {loadingCircular ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                <Typography>Loading circular data...</Typography>
              </Box>
            ) : circularError ? (
              <Box sx={{ padding: 2 }}>
                <Typography color="error">Error loading circular: {circularError}</Typography>
              </Box>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                {apiSubmissionError && (
                  <Alert severity="error" className="mb-4">
                    <AlertTitle>Circular Update Failed</AlertTitle>
                    {apiSubmissionError}
                  </Alert>
                )}

                <Controller
                  name="referenceNo"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Reference No."
                      id="ref-no"
                      variant="outlined"
                      fullWidth
                      InputProps={{
                        readOnly: true,
                      }}
                      sx={{
                        '& .MuiInputBase-input': {
                          backgroundColor: '#f5f5f5',
                        },
                      }}
                      error={!!errors.referenceNo}
                      helperText={errors.referenceNo?.message}
                    />
                  )}
                />

                <FormControl className="mt-2 mb-4" sx={{ width: '100%' }} error={!!errors.from}>
                  <InputLabel htmlFor="from-select">From</InputLabel>
                  <Controller
                    name="from"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        id="from-select"
                        label="From"
                        disabled={true}
                        sx={{
                          backgroundColor: '#f5f5f5',
                        }}
                      >
                        <MenuItem value={field.value}>
                          {getFromEmployeeName()}
                        </MenuItem>
                      </Select>
                    )}
                  />
                  {errors.from && <FormHelperText>{errors.from.message}</FormHelperText>}
                </FormControl>

                <Controller
                  name="subject"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Subject"
                      id="subject"
                      variant="outlined"
                      fullWidth
                      error={!!errors.subject}
                      helperText={errors.subject?.message}
                      disabled={isApiSubmitting}
                    />
                  )}
                />

                <div className="mt-2 mb-4">
                  <InputLabel required error={!!errors.body} sx={{ mb: 1 }}>
                    Circular Body
                  </InputLabel>
                  <Controller
                    name="body"
                    control={control}
                    render={({ field }) => (
                      <RTEEditor
                        ref={editorRef}
                        onChange={handleEditorChange}
                        value={circular?.body || ''}
                      />
                    )}
                  />
                  {errors.body && <FormHelperText error>{errors.body.message}</FormHelperText>}
                </div>

                {/* Existing Attachments Display */}
                {circular?.attachments && circular.attachments.length > 0 && (
                  <div className="mt-2 mb-4">
                    <Typography variant="subtitle1" gutterBottom>
                      Existing Attachments
                    </Typography>
                    <div className="space-y-2">
                      {circular.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border border-gray-300 rounded"
                        >
                          <div className="flex flex-col space-y-1 flex-1">
                            <div className="flex items-center space-x-2">
                              <Typography variant="body2" className="font-medium">
                                {attachment.name}
                              </Typography>
                            </div>
                            <div className="flex items-center space-x-3">
                              {attachment.type && (
                                <Typography variant="caption" color="textSecondary">
                                  Type: {attachment.type}
                                </Typography>
                              )}
                            </div>
                          </div>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => window.open(attachment.url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-end space-x-2 mt-10">
                  <Button
                    variant="outlined"
                    onClick={() => router.push(`/portal/e-messaging/circular/${circularId}`)}
                    disabled={isApiSubmitting || isRHFSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={isRHFSubmitting || isApiSubmitting}
                  >
                    {isApiSubmitting ? (
                      <CircularProgress size={15} color="inherit" />
                    ) : (
                      'Update Circular'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </Card>
        </motion.div>
      }
    />
  );
}

export default EditCircular;