{"mainAssemblyName": "GdPicture.NET.PSPDFKit.Wasm.NET8.dll", "resources": {"hash": "sha256-9UZ0fFqwe2IwvcBnnvoYJyKWy3Dskc90ooKJ3YPqKNI=", "jsModuleNative": {"dotnet.native.js": "sha256-iVMIZ3eMuZdrqMBhu5LuFz3NizCwAOiYVUZSMYstDFM="}, "jsModuleRuntime": {"dotnet.runtime.js": "sha256-FjrgL9dY6dc2yUoXDWowSfachSgM8O29qUMWDQOJKRI="}, "wasmNative": {"dotnet.native.wasm": "sha256-3IDz6MTxFu+EFSe4SIrLupOt7HL2TRHam9rrBaOaSig="}, "icu": {"icudt_CJK.dat": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=", "icudt_EFIGS.dat": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=", "icudt_no_CJK.dat": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}, "assembly": {"BouncyCastle.Cryptography.wasm": "sha256-Zfdh7lBQM0nXhqPskn0+W791X4gfyME0e0nT1owZzFc=", "ChromeHtmlToPdfLib.wasm": "sha256-Qx1f8jjUUWsuIh6RrnNQQMYp0Y1LhGK5LYYWWSHMUlU=", "DocumentFormat.OpenXml.wasm": "sha256-upA93T2lQOiZpMLrMzRzkn8Vc6HnKkSCw35MGmwkIrc=", "DocumentFormat.OpenXml.Framework.wasm": "sha256-6Jbj8OWdFmitrjZR2zJYecVWJ21KxE21MvSDvAfkNyw=", "GdPicture.NET.14.API.wasm": "sha256-i5djZ1o3+lHiODguePiGPz6RPAwJ6lzlMzA45doIk3o=", "GdPicture.NET.14.barcode.1d.writer.wasm": "sha256-sUbYKLJyho3zavuQTp7rGb6Uvjy7dx66+741xYmJwhI=", "GdPicture.NET.14.barcode.2d.writer.wasm": "sha256-EBB8xQ9jTiDQmmfdhpc7v+YIcO701N+fpkN8JmC37ZE=", "GdPicture.NET.14.CAD.wasm": "sha256-nlUzqGqlsRilpRZw2lkU5bmBqQWvLdhh3bs+7xy+YGY=", "GdPicture.NET.14.CAD.DWG.wasm": "sha256-CTwk9wJNWAE2wRegpAYQNOgZnJiQQTXJ+8245Pvy6TY=", "GdPicture.NET.14.Common.wasm": "sha256-1/Kdifg0ZVAdWz3pvjHyohTREwnzku+yru27iXP3rMs=", "GdPicture.NET.14.Document.wasm": "sha256-iXQKK6id+reJLeyPd/K9BhvZDkf8vdDX+kIxTkCW52M=", "GdPicture.NET.14.Imaging.wasm": "sha256-BVX+ddGAIN5B2WaYjur8lLhPqGpwnXz0YlhnZLSy7Aw=", "GdPicture.NET.14.Imaging.Formats.Conversion.wasm": "sha256-ypz2x/ZEHcLTrULwVxHMI0uRsIRlujeEZIPbh90SmDE=", "GdPicture.NET.14.Imaging.Formats.wasm": "sha256-HRjBuxY+wXMAYecwDOqdo4g2dwV7hg9jjrtfiw7xrJ8=", "GdPicture.NET.14.Imaging.Rendering.wasm": "sha256-bc+olPJQ4WNFPvVVcO2SDdkUx2tZ+NmGhYCfvZSA5eY=", "GdPicture.NET.14.MSOfficeBinary.wasm": "sha256-1VZFXeTgkToAuiI7WkDayLYsokd142I0ZVKj/Tm0Yao=", "GdPicture.NET.14.OpenDocument.wasm": "sha256-QhkZn+Ui0mfb+ZVY7MOoTuqgoROeNFor+xNtv0N2MMo=", "GdPicture.NET.14.OpenXML.wasm": "sha256-g7s5IxxL8rCJj3u11Q8m5Ok+VwivhA2MDYsKfIV15V0=", "GdPicture.NET.14.PDF.wasm": "sha256-in3qn+Zb4yA0b7ZOck6YWzaDa+ySUhyzvdGMaDBo2B8=", "GdPicture.NET.14.RTF.wasm": "sha256-yqETUvQPxkVelRbHNYG00vDf4jrYemkAETAUtuGUyEI=", "GdPicture.NET.14.SVG.wasm": "sha256-l3R8EFGSMQ8j3oab//kNIE3sZWWluOflWnMx+/6zk64=", "GdPicture.NET.OpenXML.Templating.Wasm.NET8.wasm": "sha256-nguFNkp+y5oVxa9iHbfm1Qxrm4x5Xsb7N11+hiMFSJw=", "GdPicture.NET.PSPDFKit.Wasm.NET8.wasm": "sha256-HWJudXA9PCCcFdcsNfUZMKyDlgnzeur76Vr7eiSziLA=", "GdPicture.NET.Wasm.NET8.wasm": "sha256-vqA6XWH7lisCJ66smKRsN9h1ixnQDvOk7K1G7WNo89w=", "Microsoft.CSharp.wasm": "sha256-iTpxvn7D6jT4LBm1HY917bxABVkmM3CCJLO7Zjn6bco=", "Microsoft.Win32.Registry.wasm": "sha256-OtiLzmWJ/mphfl9hIeCzsOCaVKuFKWMujEuo/52LTr8=", "MsgReader.wasm": "sha256-l1ZsajOC6PHY4SnvLvChqLzrxIAmvyvxJyVhJWvxbL4=", "Newtonsoft.Json.wasm": "sha256-G97Lg+/AsbDXNl4UtTZ3yGm1qvl/Hb1Qb9AKu32Xe8k=", "OpenMcdf.wasm": "sha256-GbY1SKBNj8uu05INK8eBBENtxiqKH95I8DwwSlRZay0=", "protobuf-net.Core.wasm": "sha256-uGbMq1/fsamkvm0zeYda4/hKINHVJbe+Me2XSUWpE5w=", "protobuf-net.wasm": "sha256-Meo7PL8zpAUe4czMjQRA9Onl5PGLrEhaOX9fL3gwUfY=", "RtfPipe.wasm": "sha256-3FHRVYJ5KNZ7uWogZCE45lZ//jfH5IULrRdfVg4GRLQ=", "System.Collections.Concurrent.wasm": "sha256-a2tQtwMZ7liWnLgedGhHaE/GLLBDk6co8OLDNAYwj24=", "System.Collections.wasm": "sha256-3AX3JORFzEycTJN3TAibKt28aex6V7z/ErGejRCxL/8=", "System.Collections.Immutable.wasm": "sha256-SoxRSTH4WsDgqqP/OCwVQo6ef9vhx8iBUs3HZ2F+bWg=", "System.Collections.NonGeneric.wasm": "sha256-sAhQceBp+d8nKcN8EI0QGI9E7AeeOaZjFE2THl2ppkk=", "System.Collections.Specialized.wasm": "sha256-NnqwAyJhwpYRwzB/1fuue3OjRjEcleXWeAgpQ8LL988=", "System.ComponentModel.wasm": "sha256-Nlg/nIYedk72Brjsfnc42VsWnSq5XKHC2Jpyq7jRYy0=", "System.ComponentModel.EventBasedAsync.wasm": "sha256-dJzfGfUtK7OGadgi5ldNGc7rL4K5hMoYVjOJoZvZ0l4=", "System.ComponentModel.Primitives.wasm": "sha256-Pv2+Z5KqXsVcbO4QHwttt0gEnizut1Ucu3bXhZKNMfg=", "System.ComponentModel.TypeConverter.wasm": "sha256-CCw9T4oAkH+hb46jMnTsf2Esr6M+wIXVx/6kXoonKuA=", "System.Console.wasm": "sha256-TXWjTee91uD/IyRK8xARbmgRFkTqZJLNrNv5fa5GZP0=", "System.Data.Common.wasm": "sha256-gRyslUOx5wps667OaT8MRZiS9hLWq9VBs3/Rf6ppgD4=", "System.Diagnostics.DiagnosticSource.wasm": "sha256-vV5VJmw3/989muq+4b5vA23pGkZKbYlNACMetj6GVlU=", "System.Diagnostics.Process.wasm": "sha256-2DS3+eU6aeU90iwMDCJQN+oREbqBURPDlrkQDELkiF4=", "System.Diagnostics.TraceSource.wasm": "sha256-TvlRt602GwFEFVa3ZkEfdZ3ghcpu0CRs8XH0NQSVDZA=", "System.wasm": "sha256-jsKXa9gY3uvAXdaGqYVsmioMiLRA9lrZoN8/mb5JUv4=", "System.Drawing.wasm": "sha256-wAGRQJC+s7tGQt0TW9OJ02e7nVR9viPSGRnmM16xnlM=", "System.Drawing.Primitives.wasm": "sha256-wH4n2ReQ3mGKz3pOg/5BPnPAYwMW3L8tzRcJYsxYnG8=", "System.Formats.Asn1.wasm": "sha256-k5GiJaMtWhjTMUaZB4gifJWRfer19abIH8Lptc0zA9I=", "System.IO.Compression.wasm": "sha256-hcEwqbCwJgDyZCqxDVVAvV2KvtF4/OaOUt3/Es6VraY=", "System.IO.Packaging.wasm": "sha256-cTFTNn+xCHLQWZe8PlZGkvRYJFGiFyA78evDsn64Gpw=", "System.Linq.wasm": "sha256-8OaOkUKLFido7kfUGmRIFLLkkWdPtG3yo2TPh3IXDXE=", "System.Linq.Expressions.wasm": "sha256-oZvNNYmqTQ+6bmAyy/zqRgpOkkCVlVEjdraOmNzcMaA=", "System.Memory.wasm": "sha256-+CXDzUfeRegLsqXpZEYXvvpMGsGJqk0Qbboph8KKqcQ=", "System.Net.Http.wasm": "sha256-KLW9QL2wKDZ5mRJ0LNtutxs3UjbvaTYs4gWyVKs1/gY=", "System.Net.Http.Formatting.wasm": "sha256-un42fxjDyQmAldV9kdz6UXWHWG07zW1H9BmJaIkXhaM=", "System.Net.Mail.wasm": "sha256-hnQXEimS/nE6hIhVX/02TEFjrkP7qjotHjGRyPuEATE=", "System.Net.NetworkInformation.wasm": "sha256-II7PqCWSjRonRR1Sp0hh4ekaCRDK9Rw9j14VqCuFB64=", "System.Net.Primitives.wasm": "sha256-abc+IhzCP13rnotUpP6/0uVUm421ySBkZOFKXiiI5Ko=", "System.Net.Requests.wasm": "sha256-w7kaegp2TJP4rQ1UXZFv11NwdnA/TG0/MN5qiSSB4rk=", "System.Net.Security.wasm": "sha256-97gKTkKNdUfuWzuu5BLAMTlHoM0BSzc8uW8iiYu3BCQ=", "System.Net.ServicePoint.wasm": "sha256-zGWc2gZbIPDQDsZCtsgGnkwk/aPyZ31KYKM5cX5x8xQ=", "System.Net.Sockets.wasm": "sha256-5MbgOJlaA+4MXZ1abk+wmxvhCEezm1W6G8LE7HmQSbs=", "System.Net.WebHeaderCollection.wasm": "sha256-FWmoWdRfM9A+JaTRKJDFk9VukSWEXlFV6j5WVR8MDCU=", "System.Net.WebSockets.Client.wasm": "sha256-fzzBVGWgk1ii7Ax+M0ubxZ+YlABiQNAMkiMKvAZ4PWI=", "System.Net.WebSockets.wasm": "sha256-YRkNIgzSFb0dLaoY7j3BJds3aFWGXEcfp1A4407n6hM=", "System.ObjectModel.wasm": "sha256-fP9zaax7wUDO2qGQQ4IDIAnRYe5/goDo1Yjn3X+0zEw=", "System.Private.CoreLib.wasm": "sha256-qM1sTW1WUeNeKOL0cAHqRi69QV92AaxjuwXUUFEHZ+w=", "System.Private.Uri.wasm": "sha256-nxSqrx+dTHWd/Y0v2ajVSLOm1iB8VcuTd/ybo0LBUFE=", "System.Private.Xml.wasm": "sha256-M3iLtqXmMYthNiwbPUIG2eVB4X5PLLHdSEhLbtyyHx8=", "System.Private.Xml.Linq.wasm": "sha256-W1YdMIOZoPgSQqLr035mC1QPM5AI2zxCzIR2Wxp1LGU=", "System.Runtime.InteropServices.JavaScript.wasm": "sha256-+SQhY2+x8q9+DC62PLQOU7eE3qGoCp/yzTzL85YJYXA=", "System.Runtime.Numerics.wasm": "sha256-BkQCI0NrY9HYO0usEOZCpG8EzrfJ+9YjXhsvHKoFrs4=", "System.Runtime.Serialization.Formatters.wasm": "sha256-+tHiDrxzAH+vsdjzywsvZ6Dr66o+2CMTVOFOefWipK0=", "System.Runtime.Serialization.Primitives.wasm": "sha256-Ybesv9WVVHCOm58g3QgFCSOlIqx7N2tg9n8P+HQ+sSQ=", "System.Security.Cryptography.wasm": "sha256-SbX4TMHweKLKVQy+6ITgQ1JldzoXfxjtF0nx0kuXZ6g=", "System.Security.Cryptography.Pkcs.wasm": "sha256-t89Eb56oMNngXYqJEZkrKzluo3riy1fcOoQQjaGHOGc=", "System.Text.Encoding.CodePages.wasm": "sha256-tOHyXwz4QpN95oH3StELlaG5OQHye1ZiNeVy5u/ncCo=", "System.Text.Encodings.Web.wasm": "sha256-HPFJ6L6vxmtZ4rMj9fqhXxF3pqm9hhijPWBvliNCTps=", "System.Text.Json.wasm": "sha256-X19P6bnU0EGvxnoYYe4L7JxxzXzOvUahrM8KTsJfARk=", "System.Text.RegularExpressions.wasm": "sha256-SW6n91GDn5vBcO6R1+GBRw5qco/HsTZTJOnGTuMxinw=", "System.Threading.Tasks.Parallel.wasm": "sha256-st4G7D5P8R93kxztaMj+IJVmYg1C71JslXP8sUwLQoE=", "System.Windows.Extensions.wasm": "sha256-G+RkLqqV+p65mqNO3xT3gnya+vP+Phsq4TUk2ZrXTe8=", "System.Xml.Linq.wasm": "sha256-+wSJnzp1rQiHBUV2Jpsq6/fC1ZJB6gW3Io5Lnbi/9VA="}, "vfs": {"runtimeconfig.bin": {"supportFiles/0_runtimeconfig.bin": "sha256-thfax//rPSFO1ZKFH5Mqadj5fakhuqdFx2JvVSVyq0U="}}}, "debugLevel": 0, "globalizationMode": "sharded"}