import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule, RabbitMQModule } from '@igov/common';
import { ChatMessageService, PresenceService } from './services';
import { ChatMessageEntity } from './entities';
import { ChatGateway } from './gateways';
import { AuthCoreModule } from '@igov/auth';
import { ChatMessageRepository } from './repositories';

@Module({
  imports: [DatabaseModule.forFeature([ChatMessageEntity]), RabbitMQModule, AuthCoreModule],
  controllers: [],
  providers: [ChatMessageService, PresenceService, ChatMessageRepository, ChatGateway],
  exports: [ChatMessageService, PresenceService, ChatMessageRepository, ChatGateway],
})
export class ChatCoreModule {}
