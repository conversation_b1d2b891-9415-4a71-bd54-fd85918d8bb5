import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUser1745102790015 implements MigrationInterface {
  name = 'UpdateUser1745102790015';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" RENAME TO "users"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" RENAME TO "user"`);
  }
}
