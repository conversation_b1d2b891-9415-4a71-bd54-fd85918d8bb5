import { Chip, darken, lighten } from '@mui/material';
import { CircularStatusEnums } from '@/services/api/types/circular.types';

type CircularStatusConfig = {
  title: string;
  color: string;
};

const circularStatusConfig: Record<CircularStatusEnums, CircularStatusConfig> = {
  [CircularStatusEnums.DRAFT]: {
    title: 'Draft',
    color: '#9e9e9e', // Gray
  },
  [CircularStatusEnums.PUBLISH]: {
    title: 'Published',
    color: '#4caf50', // Green
  },
};

type CircularStatusProps = {
  status: CircularStatusEnums;
};

function CircularStatus({ status }: CircularStatusProps) {
  const statusConfig = circularStatusConfig[status];

  if (!statusConfig) {
    return null;
  }

  return (
    <Chip
      className="font-semibold text-md"
      label={statusConfig.title}
      sx={(theme) => ({
        color: lighten(statusConfig.color, 0.8),
        backgroundColor: darken(statusConfig.color, 0.1),
        ...theme.applyStyles('light', {
          color: darken(statusConfig.color, 0.4),
          backgroundColor: lighten(statusConfig.color, 0.8),
        }),
      })}
      size="small"
    />
  );
}

export default CircularStatus;