import { apiClient } from './apiClient';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowResponseDto,
  AuthorizeRequestDto,
  WorkflowCommentDto,
  PaginatedWorkflowResponseDto,
  WorkflowFilterDto,
  WorkflowAuthorizationFilterDto,
  WorkflowStatusEnum,
} from './types/workflow.types';

/**
 * Service for workflow-related API calls
 */
class WorkflowApiService {
  private baseUrl = '/workflow-self-service';
  private adminBaseUrl = '/workflows';

  /**
   * Get all workflow requests with filtering and pagination
   *
   * @param options - Query options for pagination and filtering
   * @returns Promise with paginated workflow data
   */
  async getWorkflows(options?: WorkflowFilterDto): Promise<PaginatedWorkflowResponseDto> {
    try {
      // Build query string from options
      const queryParams = new URLSearchParams();

      if (options?.skip !== undefined) {
        queryParams.append('skip', options.skip.toString());
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.search) {
        queryParams.append('search', options.search);
      }
      if (options?.status) {
        queryParams.append('status', options.status);
      }
      if (options?.employeeId) {
        queryParams.append('employeeId', options.employeeId);
      }
      if (options?.initiatedBy) {
        queryParams.append('initiatedBy', options.initiatedBy);
      }
      if (options?.workflowTypeId) {
        queryParams.append('workflowTypeId', options.workflowTypeId);
      }
      if (options?.fromDate) {
        queryParams.append('fromDate', options.fromDate);
      }
      if (options?.toDate) {
        queryParams.append('toDate', options.toDate);
      }

      const queryString = queryParams.toString();
      const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

      return await apiClient.get<PaginatedWorkflowResponseDto>(url);
    } catch (error: any) {
      console.error('Failed to get workflows:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get workflows where the current user is an authorizer
   *
   * @param options - Query options for pagination and filtering
   * @returns Promise with paginated workflow data
   */
  async getAuthorizingWorkflows(options?: WorkflowAuthorizationFilterDto): Promise<PaginatedWorkflowResponseDto> {
    try {
      // Build query string from options
      const queryParams = new URLSearchParams();

      if (options?.skip !== undefined) {
        queryParams.append('skip', options.skip.toString());
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.search) {
        queryParams.append('search', options.search);
      }
      if (options?.status) {
        queryParams.append('status', options.status);
      }
      if (options?.initiatedBy) {
        queryParams.append('initiatedBy', options.initiatedBy);
      }
      if (options?.workflowTypeId) {
        queryParams.append('workflowTypeId', options.workflowTypeId);
      }

      const queryString = queryParams.toString();
      const url = queryString 
        ? `${this.adminBaseUrl}/employee/authorizing?${queryString}` 
        : `${this.adminBaseUrl}/employee/authorizing`;

      return await apiClient.get<PaginatedWorkflowResponseDto>(url);
    } catch (error: any) {
      console.error('Failed to get authorizing workflows:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create a new workflow
   *
   * @param data - The workflow data to create
   * @returns Promise with the created workflow data
   */
  async createWorkflow(data: CreateWorkflowDto): Promise<WorkflowResponseDto> {
    if (!data.title?.trim()) {
      throw new Error('Workflow title is required');
    }
    if (!data.description?.trim()) {
      throw new Error('Workflow description is required');
    }
    if (!data.authorizingUser?.trim()) {
      throw new Error('Authorizing user is required');
    }
    if (!data.workflowTypeId?.trim()) {
      throw new Error('Workflow type is required');
    }
    if (data.amount === undefined || data.amount < 0) {
      throw new Error('Valid amount is required');
    }

    try {
      return await apiClient.post<WorkflowResponseDto>(this.adminBaseUrl, data);
    } catch (error: any) {
      console.error('Failed to create workflow:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get a single workflow by ID
   *
   * @param id - The ID of the workflow to retrieve
   * @returns Promise with the workflow data
   */
  async getWorkflow(id: string): Promise<WorkflowResponseDto> {
    if (!id) {
      throw new Error('Workflow ID is required');
    }

    try {
      return await apiClient.get<WorkflowResponseDto>(`${this.adminBaseUrl}/${id}`);
    } catch (error: any) {
      console.error(`Failed to get workflow ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Update an existing workflow by ID
   *
   * @param id - The ID of the workflow to update
   * @param data - The workflow data to update (fields are optional)
   * @returns Promise with the updated workflow data
   */
  async updateWorkflow(id: string, data: UpdateWorkflowDto): Promise<WorkflowResponseDto> {
    if (!id) {
      throw new Error('Workflow ID is required for update');
    }
    if (Object.keys(data).length === 0) {
      throw new Error('Update data cannot be empty');
    }

    try {
      return await apiClient.patch<WorkflowResponseDto>(`${this.adminBaseUrl}/${id}`, data);
    } catch (error: any) {
      console.error(`Failed to update workflow ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Authorize (approve/decline) a workflow
   *
   * @param data - The authorization data
   * @returns Promise with success response
   */
  async authorizeWorkflow(data: AuthorizeRequestDto): Promise<{ message: string }> {
    if (!data.workflowId?.trim()) {
      throw new Error('Workflow ID is required');
    }
    if (!data.authorizingUserId?.trim()) {
      throw new Error('Authorizing user ID is required');
    }
    if (!data.authorizationStatus) {
      throw new Error('Authorization status is required');
    }
    if (data.markAsFinal === undefined || data.markAsFinal === null) {
      throw new Error('Mark as final status is required');
    }

    try {
      return await apiClient.post<{ message: string }>(`${this.baseUrl}/authorize`, data);
    } catch (error: any) {
      console.error('Failed to authorize workflow:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Add a comment to a workflow
   *
   * @param data - The comment data
   * @returns Promise with success response
   */
  async addComment(data: WorkflowCommentDto): Promise<{ message: string }> {
    if (!data.workflowId?.trim()) {
      throw new Error('Workflow ID is required');
    }
    if (!data.comment?.trim()) {
      throw new Error('Comment text is required');
    }

    try {
      return await apiClient.post<{ message: string }>(`${this.baseUrl}/comment`, data);
    } catch (error: any) {
      console.error('Failed to add comment to workflow:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get workflow counts by status for dashboard
   *
   * @returns Promise with workflow counts
   */
  async getWorkflowCounts(): Promise<Record<WorkflowStatusEnum, number>> {
    try {
      const response = await apiClient.get<Record<WorkflowStatusEnum, number>>(`${this.baseUrl}/counts`);
      return response;
    } catch (error: any) {
      console.error('Failed to get workflow counts:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }
}

// Export a single instance for easy import
export const workflowApiService = new WorkflowApiService();