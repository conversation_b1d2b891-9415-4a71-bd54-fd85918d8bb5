import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { IGDriveFile } from '../interfaces';
import { BaseEntity, IAttachment } from '@igov/common';
import { Employee } from '@igov/employee';

@Entity('g_drive_files')
export class GDriveFileEntity extends BaseEntity implements IGDriveFile {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  fileName!: string;

  @Column({ nullable: true })
  folderId?: string;

  @Column()
  employeeId!: string;

  @Column({ type: 'timestamp', nullable: true })
  readDate?: Date;

  @Column({ default: false })
  archive!: boolean;

  @Column({ type: 'jsonb' })
  attachment!: IAttachment;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'employee_id' })
  employee!: Employee;
}
