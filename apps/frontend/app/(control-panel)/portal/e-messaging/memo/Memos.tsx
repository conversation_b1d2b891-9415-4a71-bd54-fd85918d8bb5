'use client';

import FusePageSimple from '@/@fuse/core/FusePageSimple';
import { styled } from '@mui/material/styles';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import { useState, ChangeEvent, MouseEvent } from 'react';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import useNavigate from '@/@fuse/hooks/useNavigate';
import { useRouter } from 'next/navigation';
import MemoTable from '@/app/(control-panel)/portal/e-messaging/memo/MemoTable';
import MemosHeader from '@/app/(control-panel)/portal/e-messaging/memo/MemosHeader';
import FuseTabs from '@/components/tabs/FuseTabs';
import FuseTab from '@/components/tabs/FuseTab';
import { useMemos, MemoTabType } from './useMemos';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.getContrastText(theme.palette.primary.main),
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

const container = {
  show: {
    transition: {
      staggerChildren: 0.04,
    },
  },
};

const item = {
  hidden: {
    opacity: 0,
    y: 10,
  },
  show: {
    opacity: 1,
    y: 0,
  },
};

function Memos() {
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));
  const router = useRouter();

  // Use the custom hook for memo data management
  const memoState = useMemos();

  function handleSelectedCategory(event: SelectChangeEvent<string>) {
    setSelectedCategory(event.target.value);
  }

  const handleCreateMemoClick = () => {
    router.push('/portal/e-messaging/memo/new');
  };

  // Map tab values to memo tab types
  const tabValueToMemoTab: Record<string, MemoTabType> = {
    inbox: 'inbox',
    sent: 'sent',
    outbox: 'outbox',
  };

  const memoTabToTabValue: Record<MemoTabType, string> = {
    inbox: 'inbox',
    sent: 'sent',
    outbox: 'outbox',
  };

  function handleTabChange(event: React.SyntheticEvent, value: string) {
    const memoTab = tabValueToMemoTab[value];
    if (memoTab) {
      memoState.setActiveTab(memoTab);
    }
  }

  return (
    <Root
      header={<MemosHeader />}
      content={
        <div className="mt-4 md:px-16 px-4 mb-22">
          <MemoTable
            data={memoState.data}
            totalCount={memoState.totalCount}
            loading={memoState.loading}
            error={memoState.error}
            pagination={memoState.pagination}
            globalFilter={memoState.globalFilter}
            activeTab={memoState.activeTab}
            onPaginationChange={memoState.setPagination}
            onGlobalFilterChange={memoState.setGlobalFilter}
            onTabChange={memoState.setActiveTab}
          />
        </div>
      }
      scroll={isMobile ? 'normal' : 'page'}
    />
  );
}

export default Memos;
