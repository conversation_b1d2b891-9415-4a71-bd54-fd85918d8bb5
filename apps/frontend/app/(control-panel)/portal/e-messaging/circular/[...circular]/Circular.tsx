'use client';

import { styled } from '@mui/material/styles';
import FusePageSimple from '@fuse/core/FusePageSimple/FusePageSimple';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import { useEffect, useRef, useState } from 'react';
import Link from '@fuse/core/Link';
import {
  Button,
  Divider,
  IconButton,
  Paper,
  Typography,
  CircularProgress as MUICircularProgress,
  SwipeableDrawer,
  TextField,
  Alert,
} from '@mui/material';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { CircularStatusEnums } from '@/services/api/types/circular.types';
import Error404Page from '@/app/(public)/404/Error404Page';
import { useParams } from 'next/navigation';
import CircularInfo from '@/app/(control-panel)/portal/e-messaging/circular/CircularInfo';
import CircularProgress from '@/app/(control-panel)/portal/e-messaging/circular/CircularProgress';
import Box from '@mui/material/Box';
import CircularContent from '@/app/(control-panel)/portal/e-messaging/circular/[...circular]/CircularContent';
import { useCircularDetail } from './useCircularDetail';
import FuseScrollbars from '@fuse/core/FuseScrollbars';
import { useSnackbar } from 'notistack';
import { circularPDFService } from '@/app/lib/pdf/circularPDFService';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

const StyledSwipeableDrawer = styled(SwipeableDrawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 400,
    maxWidth: '90vw',
  },
}));

function Circular() {
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(!isMobile);
  const [actionDrawerOpen, setActionDrawerOpen] = useState(false);
  const [tokenInput, setTokenInput] = useState('');
  const [passwordInput, setPasswordInput] = useState('');
  const [actionError, setActionError] = useState<string | null>(null);
  const [selectedAction, setSelectedAction] = useState<'sign' | 'reject' | null>(null);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const pageLayout = useRef(null);
  const { employeeDetails } = useAuth();
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const params = useParams();
  const [circularId] = params.circular as string;

  const {
    circular,
    isLoading,
    error,
    fromEmployeeSignature,
    actionError: hookActionError,
    performCircularAction,
    isPerformingAction,
    clearActionError,
  } = useCircularDetail(circularId);

  useEffect(() => {
    setLeftSidebarOpen(!isMobile);
  }, [isMobile]);

  // Handle PDF generation and opening
  const handlePrintPDF = async () => {
    if (!circular) return;

    setIsGeneratingPDF(true);
    try {
      await circularPDFService.openCircularPDF(circular, true, fromEmployeeSignature);
      enqueueSnackbar('PDF opened successfully in new tab', {
        variant: 'success',
        autoHideDuration: 3000,
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      });
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      enqueueSnackbar(error.message || 'Failed to generate PDF. Please try again.', {
        variant: 'error',
        autoHideDuration: 5000,
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleActionClick = (action: 'sign' | 'reject') => {
    setSelectedAction(action);
    setActionDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setActionDrawerOpen(false);
    setSelectedAction(null);
    setTokenInput('');
    setPasswordInput('');
    setActionError(null);
    clearActionError(); // Clear hook's action error as well
  };

  // Check if user has required credentials
  const hasToken = employeeDetails?.eToken && employeeDetails.eToken.trim() !== '';
  const hasSignature = employeeDetails?.eSignature && employeeDetails.eSignature.trim() !== '';

  // Determine if current action requires token or signature
  const requiresToken = selectedAction === 'sign' || selectedAction === 'reject';
  const requiresSignature = selectedAction === 'sign';

  // Check if user has required credentials for the action
  const hasRequiredCredentials = () => {
    if (requiresSignature) return hasToken && hasSignature;
    if (requiresToken) return hasToken;
    return true;
  };

  // Handle action confirmation
  const handleConfirmAction = async () => {
    if (!selectedAction || !hasRequiredCredentials()) return;

    setActionError(null);
    const success = await performCircularAction(selectedAction, tokenInput, passwordInput);

    if (success) {
      // Show success toast
      enqueueSnackbar(
        `Circular ${selectedAction === 'sign' ? 'signed' : 'rejected'} successfully`,
        {
          variant: 'success',
          autoHideDuration: 3000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        },
      );
      handleCloseDrawer();
    } else {
      // Error is already set in the hook, show toast
      const errorMessage =
        hookActionError || `Failed to ${selectedAction} circular. Please try again.`;
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 3000,
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      });
    }
  };

  if (isLoading) {
    return (
      <Root
        content={
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '400px',
            }}
          >
            <MUICircularProgress size={20} />
            <Typography variant="h6" color="text.secondary" sx={{ ml: 2 }}>
              Loading circular...
            </Typography>
          </Box>
        }
      />
    );
  }

  // Handle error or not found
  if (error || !circular) {
    return <Error404Page />;
  }

  return (
    <>
      <Root
        content={
          <div className="flex flex-col min-h-full w-full relative">
            {isMobile && (
              <Paper
                className="flex sticky top-0 z-10 items-center w-full px-4 py-2 border-b-1 shadow-0"
                square
              >
                <IconButton to="/portal/e-messaging/circular" component={Link}>
                  <FuseSvgIcon>heroicons-outline:arrow-left</FuseSvgIcon>
                </IconButton>

                <Typography className="text-md font-medium tracking-tight mx-2.5">
                  {circular.title}
                </Typography>
              </Paper>
            )}

            <div className="flex flex-col flex-auto w-full min-h-full">
              <div className="flex justify-center p-4 pb-16 sm:p-6 sm:pb-16 md:p-12 md:pb-24">
                <div className="w-full max-w-4xl">
                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 mb-4">
                    {/* Print PDF Button - Always visible */}
                    <Button
                      onClick={handlePrintPDF}
                      disabled={isGeneratingPDF}
                      color="primary"
                      variant="outlined"
                      startIcon={
                        isGeneratingPDF ? (
                          <MUICircularProgress size={16} color="inherit" />
                        ) : (
                          <FuseSvgIcon size={20}>heroicons-outline:printer</FuseSvgIcon>
                        )
                      }
                    >
                      {isGeneratingPDF ? 'Generating...' : 'View PDF'}
                    </Button>
                    {/* Edit Button - Only show for draft circulars created by the current user */}
                    {circular?.status === CircularStatusEnums.DRAFT &&
                      circular?.fromEmployee?.userId === employeeDetails?.userId && (
                        <Button
                          onClick={() =>
                            router.push(`/portal/e-messaging/circular/edit/${circularId}`)
                          }
                          color="primary"
                          variant="contained"
                          startIcon={
                            <FuseSvgIcon size={20}>heroicons-outline:pencil-square</FuseSvgIcon>
                          }
                        >
                          Edit Circular
                        </Button>
                      )}

                    {/* Sign Button - Only for fromEmployee when circular is Draft */}
                    {circular?.status === CircularStatusEnums.DRAFT &&
                      circular?.fromEmployee?.userId === employeeDetails?.userId && (
                        <Button
                          onClick={() => handleActionClick('sign')}
                          color="success"
                          variant="contained"
                          startIcon={<FuseSvgIcon size={20}>heroicons-outline:pencil</FuseSvgIcon>}
                        >
                          Sign Circular
                        </Button>
                      )}

                    {/* Reject Button - Only for fromEmployee when circular is Draft */}
                    {circular?.status === CircularStatusEnums.DRAFT &&
                      circular?.fromEmployee?.userId === employeeDetails?.userId && (
                        <Button
                          onClick={() => handleActionClick('reject')}
                          color="error"
                          variant="contained"
                          startIcon={
                            <FuseSvgIcon size={20}>heroicons-outline:x-circle</FuseSvgIcon>
                          }
                        >
                          Reject Circular
                        </Button>
                      )}
                  </div>

                  <CircularContent
                    circular={circular}
                    fromEmployeeSignature={fromEmployeeSignature}
                  />
                </div>
              </div>
            </div>

            {isMobile && (
              <Box
                sx={{ backgroundColor: '#E8E8E8' }}
                className="flex sticky bottom-0 z-10 items-center w-full p-4 border-t-1"
              >
                <IconButton
                  onClick={() => setLeftSidebarOpen(true)}
                  aria-label="open left sidebar"
                  size="large"
                >
                  <FuseSvgIcon>heroicons-outline:bars-3</FuseSvgIcon>
                </IconButton>

                <CircularProgress className="flex flex-1 mx-2" />
              </Box>
            )}
          </div>
        }
        leftSidebarOpen={leftSidebarOpen}
        leftSidebarOnClose={() => {
          setLeftSidebarOpen(false);
        }}
        leftSidebarWidth={400}
        leftSidebarContent={
          <>
            <div className="p-8">
              <Button
                to="/portal/e-messaging/circular"
                component={Link}
                className="mb-6"
                color="secondary"
                variant="text"
                startIcon={<FuseSvgIcon size={20}>heroicons-outline:arrow-small-left</FuseSvgIcon>}
              >
                Back to circulars
              </Button>

              <CircularInfo circular={circular} />

              <div className="mt-5">
                {circular.attachments?.map((attachment, index) => {
                  return (
                    <div
                      className="flex items-center gap-2 mt-2 cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
                      key={index}
                      onClick={() => window.open(attachment?.url, '_blank')}
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          window.open(attachment?.url, '_blank');
                        }
                      }}
                    >
                      <Box
                        sx={{ backgroundColor: '#fff' }}
                        className="flex items-center justify-center w-6 h-6 rounded-md overflow-hidden"
                      >
                        <img src="/assets/images/icons/pdf-icon.png" alt="pdf icon" />
                      </Box>

                      <div className="flex-1">
                        <Typography className="text-md font-medium tracking-tight break-words">
                          {attachment.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Click to open
                        </Typography>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            <Divider />
          </>
        }
        scroll="content"
        ref={pageLayout}
        contentScrollbarsProps={{
          scrollToTopOnChildChange: true,
        }}
      />

      {/* Action Drawer */}
      <StyledSwipeableDrawer
        open={actionDrawerOpen}
        anchor="right"
        onOpen={() => {}}
        onClose={handleCloseDrawer}
        disableSwipeToOpen
      >
        <FuseScrollbars>
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <Typography variant="h6" className="">
                {selectedAction === 'sign' && 'Sign Circular'}
                {selectedAction === 'reject' && 'Reject Circular'}
              </Typography>
              <IconButton onClick={handleCloseDrawer} size="small">
                <FuseSvgIcon>heroicons-outline:x-mark</FuseSvgIcon>
              </IconButton>
            </div>

            <div className="">
              <Typography variant="body1" className="mb-3">
                {selectedAction === 'sign' && 'Are you sure you want to sign this circular?'}
              </Typography>

              {selectedAction === 'reject' && (
                <Typography variant="body1" className="mb-3" color="error">
                  Are you sure you want to reject this circular? This action cannot be undone.
                </Typography>
              )}

              <Paper className="p-4 bg-gray-50 mb-4">
                <Typography variant="body2" color="text.secondary" className="mb-2">
                  Circular Details:
                </Typography>
                <Typography variant="body2" className="font-medium mb-1">
                  {circular?.title}
                </Typography>
                <Typography variant="caption" color="text.secondary" className="block mb-1">
                  Reference: {circular?.reference}
                </Typography>
                <Typography variant="caption" color="text.secondary" className="block mb-1">
                  Created by: {circular?.fromEmployee?.firstName} {circular?.fromEmployee?.lastName}
                </Typography>
                <Typography variant="caption" color="text.secondary" className="block">
                  Created at:{' '}
                  {circular?.createdAt ? new Date(circular.createdAt).toLocaleString() : 'N/A'}
                </Typography>
              </Paper>

              {/* Conditional Credential Check */}
              {!hasRequiredCredentials() ? (
                <Alert severity="warning" className="mt-4">
                  <Typography variant="body2" className="font-medium mb-2">
                    Missing Required Credentials
                  </Typography>
                  <Typography variant="body2">
                    {requiresSignature && !hasToken && 'You need to set up your eToken first.'}
                    {requiresSignature &&
                      hasToken &&
                      !hasSignature &&
                      'You need to set up your eSignature first.'}
                    {requiresSignature &&
                      !hasToken &&
                      !hasSignature &&
                      'You need to set up both your eToken and eSignature first.'}
                    {requiresToken && !hasToken && 'You need to set up your eToken first.'}
                  </Typography>
                  <Typography variant="body2" className="mt-2">
                    Please contact your administrator or visit your profile settings to set up the
                    required credentials.
                  </Typography>
                </Alert>
              ) : (
                <>
                  {/* Token Input - Required for all actions */}
                  <TextField
                    fullWidth
                    label="Enter your eToken"
                    type="password"
                    value={tokenInput}
                    onChange={(e) => setTokenInput(e.target.value)}
                    variant="outlined"
                    size="small"
                    placeholder="Enter your 6-digit eToken"
                    helperText="Enter the 6-digit eToken associated with your account"
                  />

                  {/* Password Input - Required for all actions */}
                  <TextField
                    fullWidth
                    label="Password Confirmation"
                    type="password"
                    value={passwordInput}
                    onChange={(e) => setPasswordInput(e.target.value)}
                    variant="outlined"
                    size="small"
                    placeholder="Enter your password"
                    helperText="Enter your account password to confirm this action"
                    className="my-4"
                  />

                  {/* Additional info for signing */}
                  {requiresSignature && (
                    <Alert severity="info">
                      <Typography variant="body2">
                        This action will apply your digital signature to the circular and mark it as
                        published.
                      </Typography>
                    </Alert>
                  )}
                </>
              )}

              {/* Error Display */}
              {(actionError || hookActionError) && (
                <Alert severity="error" className="mt-4">
                  <Typography variant="body2">{actionError || hookActionError}</Typography>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button onClick={handleCloseDrawer} variant="outlined" fullWidth>
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmAction}
                  variant="contained"
                  fullWidth
                  disabled={
                    !hasRequiredCredentials() ||
                    (hasRequiredCredentials() && (!passwordInput || !tokenInput)) ||
                    isPerformingAction
                  }
                  color={selectedAction === 'reject' ? 'error' : 'success'}
                  startIcon={
                    isPerformingAction ? (
                      <MUICircularProgress size={16} color="inherit" />
                    ) : undefined
                  }
                >
                  {isPerformingAction ? (
                    'Processing...'
                  ) : (
                    <>
                      {selectedAction === 'sign' && 'Sign'}
                      {selectedAction === 'reject' && 'Reject'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </FuseScrollbars>
      </StyledSwipeableDrawer>
    </>
  );
}

export default Circular;
