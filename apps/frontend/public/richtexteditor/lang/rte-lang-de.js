//German , Deutsch
RTE_DefaultConfig.text_language = "Sprache";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "stornieren";	//"Cancel"
RTE_DefaultConfig.text_normal = "normalen";	//"Normal"
RTE_DefaultConfig.text_h1 = "Überschrift 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Überschrift 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Überschrift 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Überschrift 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Überschrift 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Überschrift 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Überschrift 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "Schliessen";	//"Close"
RTE_DefaultConfig.text_bold = "Fett";	//"Bold"
RTE_DefaultConfig.text_italic = "Kursiv";	//"Italic"
RTE_DefaultConfig.text_underline = "Unterstreichen";	//"Underline"
RTE_DefaultConfig.text_strike = "Durchstreichen";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Hochgestellt";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Tiefgestellt";	//"Subcript"
RTE_DefaultConfig.text_ucase = "UGrossbuchstaben";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Kleinbuchstaben";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Format entfernen";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Link einfügen";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Link öffnen";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Link bearbeiten";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Link entfernen";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Zeilenabstand";	//"Line Height"
RTE_DefaultConfig.text_indent = "Einrücken";	//"Indent"
RTE_DefaultConfig.text_outdent = "Ausrücken";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Block-Zitat";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Geordnete Liste";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Ungeordnete Liste";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Horizontale Linie einfügen";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Datum einfügen";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Tabelle einfügen";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Bild einfügen";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Video einfügen";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Code einfügen";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF erstellen";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Emoji einfügen";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Sonderzeichen";	//"Special characters"
RTE_DefaultConfig.text_characters = "Anzahl Zeichen";	//"Characters"
RTE_DefaultConfig.text_fontname = "Font";	//"Font"
RTE_DefaultConfig.text_fontsize = "Grösse";	//"Size"
RTE_DefaultConfig.text_forecolor = "Textfarbe";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Hintergrundsfarbe";	//"Back Color"
RTE_DefaultConfig.text_justify = "Blocksatz";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Linksbündig";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Rechtsbündig";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Zentriert";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Blocksatz";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Nichts";	//"Justify None"
RTE_DefaultConfig.text_delete = "Löschen";	//"Delete"
RTE_DefaultConfig.text_save = "Datei speichern";	//"Save file"
RTE_DefaultConfig.text_selectall = "Alles auswählen";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-Code";	//"HTML Code"
RTE_DefaultConfig.text_preview = "Vorschau";	//"Preview"
RTE_DefaultConfig.text_print = "Drucken";	//"Print"
RTE_DefaultConfig.text_undo = "Rückgängig machen";	//"Undo"
RTE_DefaultConfig.text_redo = "Wiederholen";	//"Redo"
RTE_DefaultConfig.text_more = "Mehr...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Neues Dokument";	//"New Doc"
RTE_DefaultConfig.text_help = "Hilfe";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Ganzes Fenster";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Beenden Sie den Vollbildmodus";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Bild-Editor";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Bildstile";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline-Stile";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Absatzstile";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Linkstile";	//"Link Styles"
RTE_DefaultConfig.text_link = "Link";	//"Link"
RTE_DefaultConfig.text_style = "Stile";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css-Klassen";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Nach Url";	//"By Url"
RTE_DefaultConfig.text_upload = "uploaden";	//"Upload"
RTE_DefaultConfig.text_size = "Grösse";	//"Size"
RTE_DefaultConfig.text_text = "Text";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Öffnen in neuem Tab";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "Einfügen";	//"Insert"
RTE_DefaultConfig.text_update = "Aktualisieren";	//"Update"
RTE_DefaultConfig.text_find = "Suchen & Ersetzen";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "Suchen";	//"Find"
RTE_DefaultConfig.text_replacewith = "Ersetzen";	//"Replace"
RTE_DefaultConfig.text_findnext = "nächstes";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ersetzen";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Alle ersetzen";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Gross / Klein";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Ganzes Word";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Move Down";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Move Up";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Autogröße";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% Breite";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% Breite";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% Breite";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% Breite";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Set-Größe";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt-Text";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "rechtfertigen";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Bildunterschrift";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Zusammenführen von Zellen";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Zelle vertikal teilen";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Zells horizontal teilen";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Zellen-Textfarbe";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Zellen-Hintergrundsfarbe";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Zeile oberhalb einfügen";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Zeile unterhalb einfügen";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Spalte links einfügen";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Spalte rechts einfügen";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Spalte löschen";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Zeile löschen";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Tabelle löschen";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Autogrösse";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Tabellenkopf";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Hinzufügen eines neuen Absatzes";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "Einfügen";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "Einfügen";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Als Text einfügen";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Einfügen als Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Einfügen von Word";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Bitte verwenden Sie STRG+V, um den Inhalt in das Feld unten einzufügen. Der Inhalt wird automatisch gesäubert.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "Absätze";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "Absatz";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Nach oben";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Nach unten";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "Duplizieren";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "Löschen";	//"Delete"
RTE_DefaultConfig.text_pmore = "Mehr..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Mehr..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Umschalten des Rahmens";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "Ausschneiden";	//"Cut"
RTE_DefaultConfig.text_copy = "Kopieren";	//"Copy"
RTE_DefaultConfig.text_copied = "Kopiert";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Einfügen von Galerie";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Dokument einfügen";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Einfügen von Vorlagen";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "Vorschau";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "Normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "Mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "Tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "Tabelle";	//"Table"
RTE_DefaultConfig.text_tablecell = "Tabellenzelle";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Tabellenzeile";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Tabellenspalte";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "Automatisch";	//"Automatic"
RTE_DefaultConfig.text_colormore = "mehr";	//"More"
RTE_DefaultConfig.text_colorpicker = "Farbauswahl";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Webpalette";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Benannte Farben";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "basic";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "zusatz";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Drag &amp; Drop";	//"Drag and drop"
RTE_DefaultConfig.text_or = "oder";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klicken Sie zum Hochladen";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Standard-Bildbeschriftung";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "Suche";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Der hinzuzufügende Text hat die maximale Anzahl Zeichen für dieses Feld erreicht.";	//"The text to be added has reached the character limit for this field."
