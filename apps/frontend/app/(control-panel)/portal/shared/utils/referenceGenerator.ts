import { departmentApiService } from '@/services/api/departmentService';

/**
 * Generates a reference number based on department communication reference
 *
 * @param departmentId - The ID of the department to fetch commRef from
 * @returns Promise with generated reference number
 *
 * @example
 * ```typescript
 * // If department has commRef "DFA/FIN/SEC"
 * const ref = await generateReferenceNumber("dept-123");
 * // Returns: "DFA/FIN/SEC/1735515186000"
 *
 * // If department has no commRef or commRef is null/empty
 * const ref = await generateReferenceNumber("dept-456");
 * // Returns: "REF/1735515186000"
 * ```
 */
export async function generateReferenceNumber(departmentId: string): Promise<string> {
  if (!departmentId) {
    // Fallback if no departmentId provided
    const timestamp = Date.now();
    return `REF/${timestamp}`;
  }

  try {
    // Fetch department data
    const department = await departmentApiService.getDepartmentById(departmentId);

    // Use commRef if available, otherwise fallback to "REF"
    const baseRef = department.commRef?.trim() || 'REF';

    // Ensure baseRef ends with "/"
    const formattedRef = baseRef.endsWith('/') ? baseRef : `${baseRef}/`;

    // Use timestamp for uniqueness
    const timestamp = Date.now();

    return `${formattedRef}${timestamp}`;

  } catch (error) {
    console.error('Failed to fetch department for reference generation:', error);

    // Fallback to generic reference if department fetch fails
    const timestamp = Date.now();
    return `REF/${timestamp}`;
  }
}

/**
 * Validates if a reference number follows the expected format
 *
 * @param reference - The reference number to validate
 * @returns boolean indicating if the reference is valid
 *
 * @example
 * ```typescript
 * isValidReference("DFA/FIN/SEC/1735515186000"); // true
 * isValidReference("REF/1735515186000"); // true
 * isValidReference("invalid-ref"); // false
 * ```
 */
export function isValidReference(reference: string): boolean {
  if (!reference || typeof reference !== 'string') {
    return false;
  }

  // Check if it follows the pattern: PREFIX/TIMESTAMP or PREFIX/SUB/TIMESTAMP etc.
  const parts = reference.split('/');

  // Must have at least 2 parts (prefix and timestamp)
  if (parts.length < 2) {
    return false;
  }

  // Last part should be a valid timestamp (numeric)
  const lastPart = parts[parts.length - 1];
  const timestamp = parseInt(lastPart, 10);

  // Check if it's a valid timestamp (reasonable range)
  const isValidTimestamp = !isNaN(timestamp) && timestamp > 1000000000000; // After year 2001

  return isValidTimestamp;
}
