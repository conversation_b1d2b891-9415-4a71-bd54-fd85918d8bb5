//Russian , Русский
RTE_DefaultConfig.text_language = "язык";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "отменить";	//"Cancel"
RTE_DefaultConfig.text_normal = "нормальной";	//"Normal"
RTE_DefaultConfig.text_h1 = "Заголовок 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Заголовок 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Заголовок 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Заголовок 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Заголовок 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Заголовок 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Заголовок 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "закрыть";	//"Close"
RTE_DefaultConfig.text_bold = "смелые";	//"Bold"
RTE_DefaultConfig.text_italic = "курсив";	//"Italic"
RTE_DefaultConfig.text_underline = "подчеркивание";	//"Underline"
RTE_DefaultConfig.text_strike = "Ударная линия";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "надстрочный";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Подкипп";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Верхний случай";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Нижний случай";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Удалить формат";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Вставить ссылку";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Открытая ссылка";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Отожесть ссылка";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Удалить ссылку";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Высота линии";	//"Line Height"
RTE_DefaultConfig.text_indent = "отступ";	//"Indent"
RTE_DefaultConfig.text_outdent = "Аутдент";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Блок Цитата";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Упорядоченный список";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Список неупорядоченных";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Вставьте горизонтальное правило";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Дата вставки";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Вставить стол";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Вставить изображение";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Вставить видео";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Вставить код";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Создание PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Вставить эмодзи";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Специальные персонажи";	//"Special characters"
RTE_DefaultConfig.text_characters = "символов";	//"Characters"
RTE_DefaultConfig.text_fontname = "шрифта";	//"Font"
RTE_DefaultConfig.text_fontsize = "размер";	//"Size"
RTE_DefaultConfig.text_forecolor = "Цвет текста";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Задний цвет";	//"Back Color"
RTE_DefaultConfig.text_justify = "оправдать";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Оправдывать левые";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Оправдывать право";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Центр оправдания";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Об оправдании полный";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Об оправдании нет";	//"Justify None"
RTE_DefaultConfig.text_delete = "удалить";	//"Delete"
RTE_DefaultConfig.text_save = "Сохранение файла";	//"Save file"
RTE_DefaultConfig.text_selectall = "Выберите все";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-код";	//"HTML Code"
RTE_DefaultConfig.text_preview = "предварительного просмотра";	//"Preview"
RTE_DefaultConfig.text_print = "печати";	//"Print"
RTE_DefaultConfig.text_undo = "отменить";	//"Undo"
RTE_DefaultConfig.text_redo = "повтора";	//"Redo"
RTE_DefaultConfig.text_more = "Больше...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Новый док";	//"New Doc"
RTE_DefaultConfig.text_help = "помочь";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Подходит для окна";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Выход полный экран";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Редактор изображений";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Стили изображения";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Встроенные стили";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Стили абзацев";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Стили ссылок";	//"Link Styles"
RTE_DefaultConfig.text_link = "ссылку";	//"Link"
RTE_DefaultConfig.text_style = "стили";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Классы Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "По Url";	//"By Url"
RTE_DefaultConfig.text_upload = "загрузить";	//"Upload"
RTE_DefaultConfig.text_size = "размер";	//"Size"
RTE_DefaultConfig.text_text = "текст";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Открыть в новой вкладке";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "вставить";	//"Insert"
RTE_DefaultConfig.text_update = "обновление";	//"Update"
RTE_DefaultConfig.text_find = "Найти и заменить";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "найти";	//"Find"
RTE_DefaultConfig.text_replacewith = "заменить";	//"Replace"
RTE_DefaultConfig.text_findnext = "следующий";	//"Next"
RTE_DefaultConfig.text_replaceonce = "заменить";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Заменить все";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Матч Дело";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Слово матча";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Перемещение вниз";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Перемещение вверх";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Авто размер";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% ширина";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "Ширина 75%";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% ширина";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% ширина";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Установить размер";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Альт текст";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "оправдать";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Image Caption";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Слияние ячеек";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Вертикальные сплит-клетки";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Сплит-клетки Горизонтальные";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Цвет текста клетки";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Цвет сотовой связи";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Вставьте строку выше";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Вставьте строку ниже";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Вставить колонку влево";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Вставить колонку вправо";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Удаление столбца";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Удалить строку";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Таблица удаления";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Авто размер";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Таблица Заголовок";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Добавить новый абзац";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "вставить";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "вставить";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Текст пасты";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Паста как Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Вставить Word";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Пожалуйста, используйте CTRL-V, чтобы вставить содержимое в поле ниже. Содержимое будет автоматически очищено.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "пунктах";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "пунктах";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Перемещение вверх";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Перемещение вниз";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "дублировать";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "удалить";	//"Delete"
RTE_DefaultConfig.text_pmore = "Больше..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Больше..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Граница конгла";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "вырезать";	//"Cut"
RTE_DefaultConfig.text_copy = "копировать";	//"Copy"
RTE_DefaultConfig.text_copied = "скопированы";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Галерея вставки";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Вставить документ";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Вставить шаблон";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "предварительного просмотра";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "нормальной";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "мобильных";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "планшет";	//"Tablet"
RTE_DefaultConfig.text_table = "таблице";	//"Table"
RTE_DefaultConfig.text_tablecell = "Ячейка таблицы";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Таблица строки";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Колонка таблицы";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "автоматическое";	//"Automatic"
RTE_DefaultConfig.text_colormore = "больше";	//"More"
RTE_DefaultConfig.text_colorpicker = "Цветной пикер";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Веб-палитра";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Названные цвета";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "основные";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "дополнение";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Перетащите и падение";	//"Drag and drop"
RTE_DefaultConfig.text_or = "или";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Нажмите, чтобы загрузить";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Default Image Caption";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "поиск";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Добавленный текст достиг предела символов для этого поля.";	//"The text to be added has reached the character limit for this field."
