{"info": {"_postman_id": "78887d7d-10d1-42c8-8d38-43d9d64f5904", "name": "Konn3ct API", "description": "Konn3ct API Collection is a collection of all endpoints that organizations and developers can take advantage of to build amazing virtual solutions in Nigeria.\n\n# **ENVIRONMENTS & CREDENTIALS**\n\nTEST - [https://d](https://sandbox.monnify.com/)ev.konn3ct.ng/api\n\nLIVE - [https://](https://sandbox.monnify.com/)konn3ct.com/api\n\n# **AUTHENTICATION**\n\ntoken - Kindly login to your account and generate token to be used with these endpoints\n\n**CONTACT** in case of issues\n\n[<EMAIL>](https://mailto:<EMAIL>)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "Create Room", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Class 255 Facilitationr\",\r\n    \"logout_url\": \"https://hello.com/returnback\",\r\n    \"access_code\": \"\",\r\n    \"welcome_message\": \"Welcome to konn3ct api\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/create-room", "host": ["{{baseurl}}"], "path": ["create-room"]}, "description": "This endpoint is used to create a room.\n\nname: This is the name that will show at the top of the session.\n\nlogout_url: This is the link that the participants will be redirected to when they logout.\n\naccess_code: Enter value here, incase you want the room to be restricted.\n\nwelcome_message: This is the text that will be shown to all participants."}, "response": [{"name": "Create Room [Success]", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Class 255 Facilitationr\",\r\n    \"logout_url\": \"https://hello.com/returnback\",\r\n    \"access_code\": \"\",\r\n    \"welcome_message\": \"Welcome to konn3ct api\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/create-room", "host": ["{{baseurl}}"], "path": ["create-room"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:16:47 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:16:47 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "98"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Room Created Successfully!\",\n    \"data\": {\n        \"name\": \"Class 255 Facilitationr\",\n        \"id\": 117\n    }\n}"}]}, {"name": "List Rooms", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/list-rooms", "host": ["{{baseurl}}"], "path": ["list-rooms"]}, "description": "This endpoint list all the rooms available on your account"}, "response": [{"name": "List Rooms [Success]", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/list-rooms", "host": ["{{baseurl}}"], "path": ["list-rooms"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 17:22:58 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 17:22:58 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "99"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Rooms fetched successfully\",\n    \"data\": [\n        {\n            \"id\": 116,\n            \"name\": \"Testsammy\",\n            \"url\": \"testsammy\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2025-01-20T00:01:48.000000Z\"\n        },\n        {\n            \"id\": 115,\n            \"name\": \"tesss\",\n            \"url\": \"testiti\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2024-12-30T11:41:14.000000Z\"\n        },\n        {\n            \"id\": 96,\n            \"name\": \"sammy baba\",\n            \"url\": \"samsam\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2024-05-27T22:44:40.000000Z\"\n        },\n        {\n            \"id\": 95,\n            \"name\": \"sammy\",\n            \"url\": \"ODE2O2D30\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2024-05-27T11:31:20.000000Z\"\n        },\n        {\n            \"id\": 94,\n            \"name\": \"testi\",\n            \"url\": \"testii\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2024-05-27T11:20:11.000000Z\"\n        },\n        {\n            \"id\": 92,\n            \"name\": \"test free account\",\n            \"url\": \"ODEO40D54\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2023-07-26T16:47:04.000000Z\"\n        },\n        {\n            \"id\": 91,\n            \"name\": \"test free account\",\n            \"url\": \"ODEDO4305\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 60,\n            \"banner\": null,\n            \"created_at\": \"2023-07-26T16:46:33.000000Z\"\n        },\n        {\n            \"id\": 89,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODE 1222OD\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2022-06-01T09:27:58.000000Z\"\n        },\n        {\n            \"id\": 88,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODE027DO4\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2022-06-01T09:27:34.000000Z\"\n        },\n        {\n            \"id\": 87,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODE23DO21\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2022-06-01T09:22:36.000000Z\"\n        },\n        {\n            \"id\": 85,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODED2502O\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2022-04-24T01:16:45.000000Z\"\n        },\n        {\n            \"id\": 84,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODEO0D142\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2022-04-24T01:15:14.000000Z\"\n        },\n        {\n            \"id\": 83,\n            \"name\": \"Class 255 Facilitationr\",\n            \"url\": \"ODEO137D1\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 250,\n            \"duration\": 1440,\n            \"banner\": null,\n            \"created_at\": \"2022-02-11T10:37:17.000000Z\"\n        },\n        {\n            \"id\": 82,\n            \"name\": \"Class 255 Facilitation\",\n            \"url\": \"ODE10DO32\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 250,\n            \"duration\": 1440,\n            \"banner\": null,\n            \"created_at\": \"2022-02-11T10:35:40.000000Z\"\n        },\n        {\n            \"id\": 81,\n            \"name\": \"Teacher Samuel\",\n            \"url\": \"ODE2D20O1\",\n            \"logout_url\": \"https://hello.com/returnback\",\n            \"welcome_message\": \"Welcome to konn3ct api\",\n            \"max_participants\": 250,\n            \"duration\": 1440,\n            \"banner\": null,\n            \"created_at\": \"2022-02-11T10:24:40.000000Z\"\n        },\n        {\n            \"id\": 67,\n            \"name\": \"samjiTestRoom\",\n            \"url\": \"ODE1D3O20\",\n            \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 100,\n            \"duration\": 600,\n            \"banner\": null,\n            \"created_at\": \"2021-08-23T03:36:19.000000Z\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"tesss\",\n            \"url\": \"FintechAcademy\",\n            \"logout_url\": \"\",\n            \"welcome_message\": \"\",\n            \"max_participants\": 90,\n            \"duration\": 0,\n            \"banner\": null,\n            \"created_at\": \"2020-10-15T11:38:00.000000Z\"\n        }\n    ]\n}"}]}, {"name": "Delete Room", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseurl}}/delete-room/92", "host": ["{{baseurl}}"], "path": ["delete-room", "92"]}, "description": "This endpoint list all the rooms available on your account"}, "response": [{"name": "Delete Room [Success]", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseurl}}/delete-room/92", "host": ["{{baseurl}}"], "path": ["delete-room", "92"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:16:27 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:16:27 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "99"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Room Deleted Successfully.\"\n}"}]}, {"name": "Start Room", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 88,\r\n    \"name\": \"Class 255 Facilitation\",\r\n    \"logout_url\": \"https://hello.com/api/end\",\r\n    \"message\": \"Welcome to class 255\",\r\n    \"started_by\": \"samji\",\r\n    \"keyword\": \"samjiclass01\",\r\n    \"access_code\":\"743ue38242\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/start-room", "host": ["{{baseurl}}"], "path": ["start-room"]}}, "response": [{"name": "Start Room [Success]", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 88,\r\n    \"name\": \"Class 255 Facilitation\",\r\n    \"logout_url\": \"https://hello.com/api/end\",\r\n    \"message\": \"Welcome to class 255\",\r\n    \"started_by\": \"samji\",\r\n    \"keyword\": \"samjiclass01\",\r\n    \"access_code\":\"743ue38242\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/start-room", "host": ["{{baseurl}}"], "path": ["start-room"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:17:10 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:17:10 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "97"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Rooms started successfully. You can now join the room\",\n    \"_link\": {\n        \"resource\": \"/join-room\",\n        \"method\": \"POST\"\n    }\n}"}]}, {"name": "Meeting Status", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/room-status/96", "host": ["{{baseurl}}"], "path": ["room-status", "96"]}}, "response": [{"name": "Meeting Status  - RoomInActive  [Success]", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/room-status/91", "host": ["{{baseurl}}"], "path": ["room-status", "91"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:19:00 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:19:00 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "98"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Status Checked successfully.\",\n    \"roomActive\": false,\n    \"data\": []\n}"}, {"name": "Meeting Status - RoomActive [Success]", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/room-status/96", "host": ["{{baseurl}}"], "path": ["room-status", "96"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:31:07 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:31:07 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "98"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Status Checked successfully.\",\n    \"roomActive\": true,\n    \"data\": {\n        \"attendee\": {\n            \"userID\": \"<EMAIL>\",\n            \"fullName\": \"samlast samfirst\",\n            \"role\": \"MODERATOR\",\n            \"isPresenter\": \"true\",\n            \"isListeningOnly\": \"false\",\n            \"hasJoinedVoice\": \"true\",\n            \"hasVideo\": \"false\",\n            \"clientType\": \"HTML5\"\n        }\n    }\n}"}]}, {"name": "Join Room", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 88,\r\n    \"name\": \"karthicMode\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"role\": \"moderator\",\r\n    \"access_code\":\"743ue38242\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/join-room", "host": ["{{baseurl}}"], "path": ["join-room"]}}, "response": [{"name": "Join Room [Success]", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 88,\r\n    \"name\": \"karthicMode\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"role\": \"moderator\",\r\n    \"access_code\":\"743ue38242\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/join-room", "host": ["{{baseurl}}"], "path": ["join-room"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:19:30 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:19:30 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "97"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Rooms fetched successfully\",\n    \"data\": \"http://127.0.0.1:8000/userjoin/eyJpdiI6IkVoZlRybnBuY01HUHB3cHM0bXVjNWc9PSIsInZhbHVlIjoiM3dvc0tNR3dUNHVmbURCelE0MWNuOE5uU04yci9ULzRhMVJiNFE4dWprY0hDaktPOU9XSXA4OUVpQ2wvRWhIaGpWR3RRaEpmVFVMTXptWkw1bW9rRDBVVTNOL2VjeEVTTGJVRVJRKzUzMm9JSnM1KzY2K09IQmQrY3pkbjhJMkJ6Z3dlK2ZHWE11YWNIb2xrTU5qd3VQV0ZBZkNLQndnemxFOHZlTXFaK1lpcGpRQ0MvZCsxTDJrTXJYSHZNUm45bzFOSzg0emJSMlFnd2Q3U3N0ZzczcnltaTRCV0lZWEs0UTR1R3hNRVdvSE5OK1VMZDI1K0ZUaFlUeGE5THpVU3A4NUVuVG1XVm84dmVsVDQwOGIzUFNGNGc2VmoxWHhuL1RZRjI1U0ZMWUU1WFFrYlFYNCtQTFphL1gyRWdrdXlOTm1vMmxWaVNYbGtpWmtjVVZ1bXZ4L1I5MjRiMTlnUVdkcE5NRkFCOXQ1a2V6cWJGSG5CZDFRb1lYOHUveVhqZ2dPM2lRek5xUjJaUWt6L09PVUI3Y1J5KzE0Mi95WDBSNWlQRVNmMnhUVHgzY0FRQlhCMVFBb3A3eFFUY2QxcEdFZWE3M3g2OGpTUmJGakUxajBESmtEcC9USmx1WE9aelFGS1RGMzE5cjE2NjBtaDMvYWkzUW9EZ21VN1R6ekpJZnM3bWx1andMTEdrUEN3bG5Gc3pVYnJLbnd6Nmk1UXE5ekRwYXJHdllyZXozZVRqQnFDMDNVSm5FYTBpTk1lYTNqTTJEYklNQjJSRmtWNEF0blQwQT09IiwibWFjIjoiZjRhMGQ1MDk4YzBjNTJhMDRhYWRkZmY0ZDhjOGUyYjE2YzExODViZjY1YmRiNWIzZmQxN2YyMmZkYjUyODM0ZSIsInRhZyI6IiJ9\"\n}"}]}, {"name": "Validate Meeting Name", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"tesss\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/meeting-info", "host": ["{{baseurl}}"], "path": ["meeting-info"]}}, "response": [{"name": "Validate Meeting Name [Success]", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"tesss\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}/meeting-info", "host": ["{{baseurl}}"], "path": ["meeting-info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:22:30 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:22:30 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "99"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Meeting validated successfully\",\n    \"data\": {\n        \"id\": 15,\n        \"user_id\": 4,\n        \"name\": \"tesss\",\n        \"url\": \"FintechAcademy\",\n        \"default_room\": \"no\",\n        \"dial_number\": \"+1 970-519-2253\",\n        \"welcome_message\": \"\",\n        \"logout_url\": \"\",\n        \"max_participants\": 90,\n        \"duration\": 0,\n        \"muj\": null,\n        \"dpuc\": null,\n        \"dprc\": null,\n        \"ewma\": null,\n        \"dum\": null,\n        \"dsn\": null,\n        \"dwr\": null,\n        \"banner\": null,\n        \"prereg\": \"N9j8Q5ZOnFBvRZHxJPYH\",\n        \"bbb_returncode\": \"SUCCESS\",\n        \"internalMeetingID\": \"b1d5781111d84f7b3fe45a0852e59758cd7a87e5-1602475017235\",\n        \"parentMeetingID\": \"bbb-none\",\n        \"voiceBridge\": \"09857\",\n        \"createDate\": \"Mon Oct 12 03:56:57 UTC 2020\",\n        \"createTime\": \"1602475017235\",\n        \"created_at\": \"2020-10-15T11:38:00.000000Z\",\n        \"updated_at\": \"2023-11-23T08:28:12.000000Z\",\n        \"deleted_at\": null\n    }\n}"}]}, {"name": "Room Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/room-details/96", "host": ["{{baseurl}}"], "path": ["room-details", "96"]}}, "response": [{"name": "Room Details [Success]", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/room-details/96", "host": ["{{baseurl}}"], "path": ["room-details", "96"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:28:53 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:28:53 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "99"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Rooms details\",\n    \"data\": {\n        \"meetingName\": \"sammy baba\",\n        \"startTime\": \"Wed Jul 16 18:27:46 UTC 2025\",\n        \"opened\": \"true\",\n        \"duration\": \"1440\",\n        \"hasParticipantJoined\": \"true\",\n        \"recordingEnabled\": \"true\",\n        \"participants\": \"1\",\n        \"participantsHasVideoOn\": \"0\",\n        \"admins\": \"1\"\n    }\n}"}]}, {"name": "List Room Recording", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/room-recordings/96", "host": ["{{baseurl}}"], "path": ["room-recordings", "96"]}}, "response": []}, {"name": "List Rooms Recording", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/rooms-recordings", "host": ["{{baseurl}}"], "path": ["rooms-recordings"]}}, "response": []}, {"name": "List Attendance", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/list-attendance/83", "host": ["{{baseurl}}"], "path": ["list-attendance", "83"]}}, "response": [{"name": "List Attendance [Success]", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/list-attendance/83", "host": ["{{baseurl}}"], "path": ["list-attendance", "83"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:53 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:53 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "97"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Room attendance fetched successfully\",\n    \"data\": [\n        {\n            \"id\": 205,\n            \"meeting_id\": 83,\n            \"identifier\": \"samjiclass01\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-24T01:44:31.000000Z\"\n        },\n        {\n            \"id\": 203,\n            \"meeting_id\": 83,\n            \"identifier\": \"samjiclass01\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-20T08:28:05.000000Z\"\n        },\n        {\n            \"id\": 199,\n            \"meeting_id\": 83,\n            \"identifier\": \"samjiclass01\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-20T08:20:37.000000Z\"\n        },\n        {\n            \"id\": 197,\n            \"meeting_id\": 83,\n            \"identifier\": \"samjiclass01\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-20T08:19:20.000000Z\"\n        },\n        {\n            \"id\": 182,\n            \"meeting_id\": 83,\n            \"identifier\": \"83196394805\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:52:35.000000Z\"\n        },\n        {\n            \"id\": 181,\n            \"meeting_id\": 83,\n            \"identifier\": \"83322262043\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:52:07.000000Z\"\n        },\n        {\n            \"id\": 180,\n            \"meeting_id\": 83,\n            \"identifier\": \"83103651399\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:48:24.000000Z\"\n        },\n        {\n            \"id\": 179,\n            \"meeting_id\": 83,\n            \"identifier\": \"831703557053\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:44:06.000000Z\"\n        },\n        {\n            \"id\": 178,\n            \"meeting_id\": 83,\n            \"identifier\": \"83893969681\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:18:32.000000Z\"\n        },\n        {\n            \"id\": 177,\n            \"meeting_id\": 83,\n            \"identifier\": \"83399040845\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-18T12:17:40.000000Z\"\n        },\n        {\n            \"id\": 173,\n            \"meeting_id\": 83,\n            \"identifier\": \"831716681338\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T09:56:27.000000Z\"\n        },\n        {\n            \"id\": 172,\n            \"meeting_id\": 83,\n            \"identifier\": \"831870146689\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T09:56:04.000000Z\"\n        },\n        {\n            \"id\": 171,\n            \"meeting_id\": 83,\n            \"identifier\": \"83718702867\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T09:53:20.000000Z\"\n        },\n        {\n            \"id\": 170,\n            \"meeting_id\": 83,\n            \"identifier\": \"831564557782\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T09:50:01.000000Z\"\n        },\n        {\n            \"id\": 167,\n            \"meeting_id\": 83,\n            \"identifier\": \"83963001764\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T09:48:23.000000Z\"\n        },\n        {\n            \"id\": 163,\n            \"meeting_id\": 83,\n            \"identifier\": \"831344710088\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:12:42.000000Z\"\n        },\n        {\n            \"id\": 162,\n            \"meeting_id\": 83,\n            \"identifier\": \"831999419510\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:09:22.000000Z\"\n        },\n        {\n            \"id\": 161,\n            \"meeting_id\": 83,\n            \"identifier\": \"83880067651\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:04:37.000000Z\"\n        },\n        {\n            \"id\": 160,\n            \"meeting_id\": 83,\n            \"identifier\": \"83337727277\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:04:34.000000Z\"\n        },\n        {\n            \"id\": 159,\n            \"meeting_id\": 83,\n            \"identifier\": \"831288880704\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:03:06.000000Z\"\n        },\n        {\n            \"id\": 158,\n            \"meeting_id\": 83,\n            \"identifier\": \"831502187529\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-04-08T07:01:32.000000Z\"\n        },\n        {\n            \"id\": 127,\n            \"meeting_id\": 83,\n            \"identifier\": \"83159586730\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-02-11T10:37:33.000000Z\"\n        }\n    ]\n}"}]}, {"name": "Attendance Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/attendance-details/83/83159586730", "host": ["{{baseurl}}"], "path": ["attendance-details", "83", "83159586730"]}}, "response": [{"name": "Attendance Details [Success]", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/attendance-details/83/83159586730", "host": ["{{baseurl}}"], "path": ["attendance-details", "83", "83159586730"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:36 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:36 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "98"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Attendance fetched successfully\",\n    \"data\": [\n        {\n            \"id\": 128,\n            \"meeting_id\": 83,\n            \"identifier\": \"83159586730\",\n            \"name\": \"<PERSON> <PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"attempt_to_join\",\n            \"created_at\": \"2022-02-11T10:37:46.000000Z\"\n        },\n        {\n            \"id\": 127,\n            \"meeting_id\": 83,\n            \"identifier\": \"83159586730\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2022-02-11T10:37:33.000000Z\"\n        }\n    ]\n}"}]}, {"name": "Meeting History", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseurl}}/meeting-history", "host": ["{{baseurl}}"], "path": ["meeting-history"]}}, "response": [{"name": "Meeting History [Success]", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/meeting-history", "host": ["{{baseurl}}"], "path": ["meeting-history"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:15 GMT"}, {"key": "Date", "value": "Wed, 16 Jul 2025 18:24:15 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.0.13"}, {"key": "X-Powered-By", "value": "Konn3ct Developer"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-RateLimit-Limit", "value": "100"}, {"key": "X-RateLimit-Remaining", "value": "99"}, {"key": "Permissions-Policy", "value": "accelerometer=(self), ambient-light-sensor=(self), autoplay=(self), battery=(self), camera=(self), cross-origin-isolated=(self), display-capture=(self), document-domain=*, encrypted-media=(self), execution-while-not-rendered=*, execution-while-out-of-viewport=*, fullscreen=(self), geolocation=(self), gyroscope=(self), magnetometer=(self), microphone=(self), midi=(self), navigation-override=(self), payment=(self), picture-in-picture=*, publickey-credentials-get=(self), screen-wake-lock=(self), sync-xhr=*, usb=(self), web-share=(self), xr-spatial-tracking=(self)"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Expect-CT", "value": "max-age=2147483648, enforce"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Download-Options", "value": "noopen"}, {"key": "X-Frame-Options", "value": "deny"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "no-referrer"}, {"key": "Server", "value": "<PERSON><PERSON>"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Meeting history fetched successfully\",\n    \"data\": [\n        {\n            \"id\": 357,\n            \"meeting_id\": 88,\n            \"identifier\": \"88176916593\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"743ue38242\",\n            \"keyword\": \"samjiclass01\",\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-07-16T18:17:08.000000Z\",\n            \"updated_at\": \"2025-07-16T18:17:08.000000Z\",\n            \"room\": {\n                \"id\": 88,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitationr\",\n                \"url\": \"ODE027DO4\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 100,\n                \"duration\": 600,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2022-06-01T09:27:34.000000Z\",\n                \"updated_at\": \"2022-06-01T09:28:11.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 356,\n            \"meeting_id\": 96,\n            \"identifier\": \"962112883357\",\n            \"name\": \"samlast samfirst\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"523r3r\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-07-16T17:24:18.000000Z\",\n            \"updated_at\": \"2025-07-16T17:24:18.000000Z\",\n            \"room\": {\n                \"id\": 96,\n                \"user_id\": 4,\n                \"name\": \"sammy baba\",\n                \"url\": \"samsam\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"\",\n                \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n                \"max_participants\": 100,\n                \"duration\": 60,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2024-05-27T22:44:40.000000Z\",\n                \"updated_at\": \"2024-05-27T22:44:40.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 326,\n            \"meeting_id\": 82,\n            \"identifier\": \"821817520046\",\n            \"name\": \" ODEJINMI & SAMUEL\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:46:42.000000Z\",\n            \"updated_at\": \"2025-01-02T12:46:42.000000Z\",\n            \"room\": {\n                \"id\": 82,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitation\",\n                \"url\": \"ODE10DO32\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 250,\n                \"duration\": 1440,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": \"SUCCESS\",\n                \"internalMeetingID\": \"5d002552b3c54c9a005d9891f4fb1644da43739d-1644575741152\",\n                \"parentMeetingID\": \"bbb-none\",\n                \"voiceBridge\": \"05685\",\n                \"createDate\": \"Fri Feb 11 11:35:41 WAT 2022\",\n                \"createTime\": \"1644575741152\",\n                \"created_at\": \"2022-02-11T10:35:40.000000Z\",\n                \"updated_at\": \"2022-02-11T10:35:42.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 324,\n            \"meeting_id\": 88,\n            \"identifier\": \"88353611225\",\n            \"name\": \"samji\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"743ue38242\",\n            \"keyword\": \"samjiclass01\",\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:29:47.000000Z\",\n            \"updated_at\": \"2025-01-02T12:29:47.000000Z\",\n            \"room\": {\n                \"id\": 88,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitationr\",\n                \"url\": \"ODE027DO4\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 100,\n                \"duration\": 600,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2022-06-01T09:27:34.000000Z\",\n                \"updated_at\": \"2022-06-01T09:28:11.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 323,\n            \"meeting_id\": 82,\n            \"identifier\": \"82554568858\",\n            \"name\": \"ODEJINMI & SAMUEL \",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:06:41.000000Z\",\n            \"updated_at\": \"2025-01-02T12:06:41.000000Z\",\n            \"room\": {\n                \"id\": 82,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitation\",\n                \"url\": \"ODE10DO32\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 250,\n                \"duration\": 1440,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": \"SUCCESS\",\n                \"internalMeetingID\": \"5d002552b3c54c9a005d9891f4fb1644da43739d-1644575741152\",\n                \"parentMeetingID\": \"bbb-none\",\n                \"voiceBridge\": \"05685\",\n                \"createDate\": \"Fri Feb 11 11:35:41 WAT 2022\",\n                \"createTime\": \"1644575741152\",\n                \"created_at\": \"2022-02-11T10:35:40.000000Z\",\n                \"updated_at\": \"2022-02-11T10:35:42.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 322,\n            \"meeting_id\": 82,\n            \"identifier\": \"821954434618\",\n            \"name\": \"ODEJINMI & SAMUEL \",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:05:54.000000Z\",\n            \"updated_at\": \"2025-01-02T12:05:54.000000Z\",\n            \"room\": {\n                \"id\": 82,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitation\",\n                \"url\": \"ODE10DO32\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 250,\n                \"duration\": 1440,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": \"SUCCESS\",\n                \"internalMeetingID\": \"5d002552b3c54c9a005d9891f4fb1644da43739d-1644575741152\",\n                \"parentMeetingID\": \"bbb-none\",\n                \"voiceBridge\": \"05685\",\n                \"createDate\": \"Fri Feb 11 11:35:41 WAT 2022\",\n                \"createTime\": \"1644575741152\",\n                \"created_at\": \"2022-02-11T10:35:40.000000Z\",\n                \"updated_at\": \"2022-02-11T10:35:42.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 321,\n            \"meeting_id\": 115,\n            \"identifier\": \"115902231756\",\n            \"name\": \" ODEJINMI & SAMUEL\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:03:23.000000Z\",\n            \"updated_at\": \"2025-01-02T12:03:23.000000Z\",\n            \"room\": {\n                \"id\": 115,\n                \"user_id\": 4,\n                \"name\": \"tesss\",\n                \"url\": \"testiti\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"\",\n                \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n                \"max_participants\": 100,\n                \"duration\": 60,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"updated_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 320,\n            \"meeting_id\": 115,\n            \"identifier\": \"115810507153\",\n            \"name\": \" ODEJINMI & SAMUEL\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T12:00:02.000000Z\",\n            \"updated_at\": \"2025-01-02T12:00:02.000000Z\",\n            \"room\": {\n                \"id\": 115,\n                \"user_id\": 4,\n                \"name\": \"tesss\",\n                \"url\": \"testiti\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"\",\n                \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n                \"max_participants\": 100,\n                \"duration\": 60,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"updated_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 319,\n            \"meeting_id\": 115,\n            \"identifier\": \"115487030357\",\n            \"name\": \" ODEJINMI & SAMUEL\",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T11:59:22.000000Z\",\n            \"updated_at\": \"2025-01-02T11:59:22.000000Z\",\n            \"room\": {\n                \"id\": 115,\n                \"user_id\": 4,\n                \"name\": \"tesss\",\n                \"url\": \"testiti\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"\",\n                \"logout_url\": \"http://127.0.0.1:8000/leftsession\",\n                \"max_participants\": 100,\n                \"duration\": 60,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": null,\n                \"internalMeetingID\": null,\n                \"parentMeetingID\": null,\n                \"voiceBridge\": null,\n                \"createDate\": null,\n                \"createTime\": null,\n                \"created_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"updated_at\": \"2024-12-30T11:41:14.000000Z\",\n                \"deleted_at\": null\n            }\n        },\n        {\n            \"id\": 318,\n            \"meeting_id\": 82,\n            \"identifier\": \"8298537646\",\n            \"name\": \"ODEJINMI & SAMUEL \",\n            \"email\": \"<EMAIL>\",\n            \"password_attendee\": \"attendee\",\n            \"keyword\": null,\n            \"status\": \"start meeting\",\n            \"created_at\": \"2025-01-02T11:09:52.000000Z\",\n            \"updated_at\": \"2025-01-02T11:09:52.000000Z\",\n            \"room\": {\n                \"id\": 82,\n                \"user_id\": 4,\n                \"name\": \"Class 255 Facilitation\",\n                \"url\": \"ODE10DO32\",\n                \"default_room\": \"no\",\n                \"dial_number\": null,\n                \"welcome_message\": \"Welcome to konn3ct api\",\n                \"logout_url\": \"https://hello.com/returnback\",\n                \"max_participants\": 250,\n                \"duration\": 1440,\n                \"muj\": null,\n                \"dpuc\": null,\n                \"dprc\": null,\n                \"ewma\": null,\n                \"dum\": null,\n                \"dsn\": null,\n                \"dwr\": null,\n                \"banner\": null,\n                \"prereg\": null,\n                \"bbb_returncode\": \"SUCCESS\",\n                \"internalMeetingID\": \"5d002552b3c54c9a005d9891f4fb1644da43739d-1644575741152\",\n                \"parentMeetingID\": \"bbb-none\",\n                \"voiceBridge\": \"05685\",\n                \"createDate\": \"Fri Feb 11 11:35:41 WAT 2022\",\n                \"createTime\": \"1644575741152\",\n                \"created_at\": \"2022-02-11T10:35:40.000000Z\",\n                \"updated_at\": \"2022-02-11T10:35:42.000000Z\",\n                \"deleted_at\": null\n            }\n        }\n    ]\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseurl", "value": "https://konn3ct.com/api", "type": "string"}, {"key": "token", "value": "Oyk1XOvUmshc0OXwYfMKnb41GEyPuYUVyAcVuQFT88", "type": "string"}]}