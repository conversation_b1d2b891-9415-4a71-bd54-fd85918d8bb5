import { Controller, Get, Param, ParseUUIDPipe, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MemoResponseDto, PaginatedMemoResponseDto, QueryMemoDto } from '../dtos';
import { JwtAuthGuard } from '@igov/auth';
import { MemoService } from '../services';

@ApiTags('Memo')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('memos')
export class MemoController {
  constructor(private readonly memoService: MemoService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get one memo record' })
  @ApiResponse({ status: 200, type: MemoResponseDto })
  async show(@Param('id', ParseUUIDPipe) id: string): Promise<MemoResponseDto> {
    return this.memoService.getOne({ id });
  }

  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: MemoResponseDto })
  @Get()
  getAllMemos(@Query() query: QueryMemoDto): Promise<PaginatedMemoResponseDto> {
    return this.memoService.getAll(query);
  }
}
