import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { AnnouncementEntity } from '../entity';
import { AnnouncementFilterDto } from '../dtos';

@Injectable()
export class AnnouncementRepository extends BaseRepository<AnnouncementEntity> {
  override entityClassName = AnnouncementEntity;
  private readonly logger = new Logger(AnnouncementRepository.name);

  async findOne(params: {
    id: string;
    employeeId?: string;
    relations?: string[];
  }): Promise<AnnouncementEntity | null> {
    const { id, employeeId } = params;

    const queryBuilder = this.repository.createQueryBuilder('announcement');

    queryBuilder
      .leftJoinAndSelect('announcement.draftedBy', 'draftedBy')
      .addSelect(['draftedBy.userId', 'draftedBy.firstName', 'draftedBy.lastName'])
      .leftJoinAndSelect('announcement.postedBy', 'postedBy')
      .addSelect(['postedBy.userId', 'postedBy.firstName', 'postedBy.lastName'])
      .leftJoinAndSelect('announcement.history', 'history')
      .leftJoinAndSelect('history.performedByEmployee', 'historyEmployee')
      .addSelect([
        'historyEmployee.userId',
        'historyEmployee.firstName',
        'historyEmployee.lastName',
      ])
      .leftJoinAndSelect('announcement.views', 'views')
      .leftJoinAndSelect('views.readBy', 'readBy')
      .addSelect(['readBy.userId', 'readBy.firstName', 'readBy.lastName']);

    queryBuilder.where('announcement.id = :id', { id });

    if (employeeId) {
      queryBuilder.andWhere(
        `(
        announcement.draftedById = :employeeId OR
        announcement.postedById = :employeeId
      )`,
        { employeeId },
      );
    }

    return queryBuilder.getOne();
  }

  async findOneById(id: string): Promise<AnnouncementEntity | null> {
    return await this.repository.findOne({ where: { id } });
  }

  async findAll(
    query: Partial<AnnouncementFilterDto>,
  ): Promise<{ data: AnnouncementEntity[]; total: number }> {
    const {
      skip,
      limit,
      search,
      status,
      fromDate,
      toDate,
      draftedById,
      postedById,
      showReadStatus,
      employeeId,
    } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('announcement')
      .leftJoin('announcement.draftedBy', 'draftedBy')
      .addSelect(['draftedBy.userId', 'draftedBy.firstName', 'draftedBy.lastName'])
      .leftJoin('announcement.postedBy', 'postedBy')
      .addSelect(['postedBy.userId', 'postedBy.firstName', 'postedBy.lastName']);

    if (showReadStatus && employeeId) {
      queryBuilder.leftJoinAndSelect(
        'announcement.views',
        'views',
        'views.readById = :employeeId AND views.announcementId = announcement.id',
        { employeeId },
      );
    }

    if (search) {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere(
        '(announcement.title ILIKE :search OR announcement.message ILIKE :search)',
        { search: searchTerm },
      );
    }

    if (status?.length) {
      queryBuilder.andWhere('announcement.status IN (:...status)', { status });
    }

    if (draftedById) {
      queryBuilder.orWhere('announcement.draftedById = :draftedById', { draftedById });
    }

    if (postedById) {
      queryBuilder.orWhere('announcement.postedById = :postedById', { postedById });
    }

    if (fromDate) {
      queryBuilder.andWhere('announcement.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      queryBuilder.andWhere('announcement.createdAt <= :toDate', { toDate });
    }

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total announcements found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const announcements = await queryBuilder.getMany();

    return { data: announcements, total: totalCount };
  }
}
