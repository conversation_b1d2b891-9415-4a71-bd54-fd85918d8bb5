# S3 Module Variables

variable "environment" {
  description = "Environment (staging/production)"
  type        = string
}

variable "db_backup_bucket" {
  description = "Name of the S3 bucket for database backups"
  type        = string
}

variable "file_storage_bucket" {
  description = "Name of the S3 bucket for file storage"
  type        = string
}

variable "force_destroy" {
  description = "A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error"
  type        = bool
  default     = false
}

variable "db_backup_retention_days" {
  description = "Number of days to retain database backups"
  type        = number
  default     = 30
}
