import Button from '@mui/lab/LoadingButton';
import { Accordion, AccordionDetails, AccordionSummary, Box, Grid } from '@mui/material';
import SelectInput from '../formInputs/SelectInput';
import TextInput from '../formInputs/TextInput';
import { Form, Formik } from 'formik';
import { FilterListSharp } from '@mui/icons-material';

interface FilterField {
  field?: string;
  label: string;
  filterFieldName: string; // The name of the field used for filtering
  fieldType: string; // The type of the field (e.g., 'text', 'number', etc.)
  fieldDisplay?: string; // The property to display in the dropdown
  options?: Array<Record<string, any>>; // Flexible structure for options
}

const CustomFilterPanel = ({
  onApplyFilters,
  filterFields = [],
}: {
  onApplyFilters: (filters: any[]) => void;
  filterFields: FilterField[];
}) => {
  const handleApplyFilters = (values, actions) => {
    onApplyFilters(values);
  };

  return (
    <Accordion style={{ marginBottom: '2rem' }}>
      <AccordionSummary>
        <FilterListSharp fontSize="large" />
      </AccordionSummary>
      <AccordionDetails style={{ padding: '1rem' }}>
        <Box sx={{ padding: 2, width: '100%' }}>
          <Formik initialValues={{}} onSubmit={handleApplyFilters}>
            {({ values, setFieldValue, isSubmitting }) => {
              return (
                <Form>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item container xs={12} spacing={2}>
                      {filterFields.map((filter, index) => (
                        <Grid
                          item
                          xs={6}
                          md={3}
                          key={index}
                          justifyContent={'center'}
                          alignItems="center"
                        >
                          {filter.fieldType === 'select' ? (
                            <SelectInput
                              label={filter.label}
                              id={filter.filterFieldName}
                              size="small"
                              name={filter.filterFieldName}
                              value={values[filter.filterFieldName]}
                              field={filter.field}
                              fieldDisplay={filter.fieldDisplay}
                              options={filter.options}
                              fullWidth
                            />
                          ) : (
                            <TextInput
                              label={filter.label}
                              id={`value-${index}`}
                              size="small"
                              name={filter.filterFieldName}
                              type={filter.fieldType}
                              value={values[filter.filterFieldName]}
                              fullWidth
                            />
                          )}
                        </Grid>
                      ))}
                    </Grid>
                    <Grid item container xs={12} justifyContent={'center'}>
                      <Button
                        variant="contained"
                        color="primary"
                        type="submit"
                        disabled={isSubmitting}
                      >
                        Apply Filters
                      </Button>
                    </Grid>
                  </Grid>
                </Form>
              );
            }}
          </Formik>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default CustomFilterPanel;
