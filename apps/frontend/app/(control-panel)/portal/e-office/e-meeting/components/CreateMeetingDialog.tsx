import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { konn3ctApiService } from '@/services/api/konn3ctService';
import { CreateRoomDto } from '@/services/api/types/konn3ct.types';

interface CreateMeetingDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateRoomDto) => void;
  loading: boolean;
}

interface FormData {
  name: string;
  welcome_message: string;
  access_code: string;
}

function CreateMeetingDialog({ open, onClose, onSubmit, loading }: CreateMeetingDialogProps) {
  const [validatingName, setValidatingName] = useState(false);
  const [nameError, setNameError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      welcome_message: '',
      access_code: '',
    },
  });

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      reset({
        name: '',
        welcome_message: '',
        access_code: '',
      });
      setNameError(null);
    }
  }, [open, reset]);

  const validateMeetingName = async (name: string) => {
    if (!name) {
      setNameError(null);
      return;
    }

    setValidatingName(true);
    setNameError(null);

    try {
      const response = await konn3ctApiService.validateMeetingName({ name });
      if (response.success && response.data) {
        // If data exists, it means the room name already exists
        setNameError('Meeting name already exists. Please choose a different name.');
      }
    } catch (error: any) {
      setNameError('Failed to validate meeting name');
    } finally {
      setValidatingName(false);
    }
  };

  const handleFormSubmit = async (data: FormData) => {
    try {
      const createRoomData: CreateRoomDto = {
        name: data.name,
        logout_url: konn3ctApiService.generateLogoutUrl(),
        welcome_message: data.welcome_message || undefined,
        ...(data.access_code && { access_code: data.access_code }),
      };

      await onSubmit(createRoomData);
      // Success handling (like dialog close) is done in the parent component
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Create room failed:', error);
    }
  };

  const handleClose = () => {
    if (!loading && !validatingName) {
      onClose();
    }
  };

  const isLoading = loading || validatingName;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={isLoading}
    >
      <DialogTitle>
        <Typography variant="h6" component="h2">
          Create New Meeting Room
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Create a new meeting room for your organization
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box component="form" onSubmit={handleSubmit(handleFormSubmit)} sx={{ mt: 2 }}>
          {/* Meeting Name */}
          <Controller
            name="name"
            control={control}
            rules={{
              required: 'Meeting name is required',
              minLength: {
                value: 3,
                message: 'Meeting name must be at least 3 characters',
              },
              maxLength: {
                value: 100,
                message: 'Meeting name must not exceed 100 characters',
              },
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Meeting Name"
                margin="normal"
                variant="outlined"
                required
                error={!!errors.name || !!nameError}
                helperText={
                  errors.name?.message || nameError || 'Enter a unique name for your meeting room'
                }
                disabled={isLoading}
                autoFocus
                onBlur={() => validateMeetingName(field.value)}
                InputProps={{
                  endAdornment: validatingName && <CircularProgress size={15} />,
                }}
              />
            )}
          />

          {/* Welcome Message */}
          <Controller
            name="welcome_message"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Welcome Message"
                margin="normal"
                variant="outlined"
                multiline
                rows={3}
                helperText="Optional: A welcome message for participants when they join the meeting"
                disabled={isLoading}
              />
            )}
          />

          {/* Access Code */}
          <Controller
            name="access_code"
            control={control}
            rules={{
              minLength: {
                value: 6,
                message: 'Access code must be at least 6 characters',
              },
              maxLength: {
                value: 8,
                message: 'Access code must be less than 8 characters',
              },
              pattern: {
                value: /^[a-zA-Z0-9]*$/,
                message: 'Access code can only contain letters and numbers',
              },
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Access Code"
                margin="normal"
                variant="outlined"
                error={!!errors.access_code}
                helperText={
                  errors.access_code?.message ||
                  'Optional: A unique code to restrict room access (6-8 characters, letters and numbers only)'
                }
                disabled={isLoading}
              />
            )}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={handleClose} disabled={isLoading} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit(handleFormSubmit)}
          variant="contained"
          disabled={isLoading || !isValid || !!nameError}
          startIcon={isLoading ? <CircularProgress size={15} /> : null}
        >
          {loading ? 'Creating...' : validatingName ? 'Validating...' : 'Create Room'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default CreateMeetingDialog;
