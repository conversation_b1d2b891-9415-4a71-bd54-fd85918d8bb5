import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
  Font,
} from '@react-pdf/renderer';
import { WorkflowResponseDto } from '@/services/api/types/workflow.types';
import { OrganizationProfileResponseDto } from '@/services/api/types/organizationProfile.types';
import OrgProfilePDF from './components/OrgProfilePDF';

// Register a font (optional - will fall back to default if not available)
// Font.register({
//   family: 'Open Sans',
//   src: 'https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVc.ttf',
// });

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontSize: 12,
    fontFamily: 'Helvetica',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333333',
  },
  detailsSection: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    width: 120,
    color: '#333333',
  },
  detailValue: {
    fontSize: 12,
    flex: 1,
    color: '#666666',
  },
  descriptionSection: {
    marginTop: 20,
  },
  descriptionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  },
  descriptionText: {
    fontSize: 11,
    lineHeight: 1.6,
    color: '#444444',
    textAlign: 'justify',
    minPresenceAhead: 10, // Ensure at least 10 units on next page if wrapping
  },
  longContentContainer: {
    minPresenceAhead: 50, // Ensure minimum content ahead before breaking
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 10,
    color: '#888888',
    borderTopWidth: 1,
    borderTopColor: '#eeeeee',
    paddingTop: 10,
  },
});

interface WorkflowPDFDocumentProps {
  workflow: WorkflowResponseDto;
  orgProfile: OrganizationProfileResponseDto;
}

// Function to strip HTML and convert to plain text
const stripHtml = (html: string): string => {
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .trim();
};

// Function to format date
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

// Function to format currency
const formatCurrency = (amount: number): string => {
  return `₦${amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

const WorkflowPDFDocument: React.FC<WorkflowPDFDocumentProps> = ({
  workflow,
  orgProfile,
}) => {
  const cleanDescription = stripHtml(workflow.description || '');
  
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header - Organization Profile */}
        <OrgProfilePDF profile={orgProfile} />


        {/* Workflow Title */}
        <Text style={styles.title}>{workflow.title}</Text>

        {/* Workflow Details */}
        <View style={[styles.detailsSection, styles.longContentContainer]}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Primary Authorizer:</Text>
            <Text style={styles.detailValue}>
              {workflow.actors?.[0]?.actor
                ? `${workflow.actors[0].actor.firstName} ${workflow.actors[0].actor.lastName}`
                : 'Not assigned'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Initiator:</Text>
            <Text style={styles.detailValue}>
              {workflow.initiatedBy
                ? `${workflow.initiatedBy.firstName} ${workflow.initiatedBy.lastName}`
                : 'Unknown'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Workflow Type:</Text>
            <Text style={styles.detailValue}>
              {workflow.workflowType?.workflowType || 'Process Approval'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Amount:</Text>
            <Text style={styles.detailValue}>{formatCurrency(workflow.amount)}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date:</Text>
            <Text style={styles.detailValue}>{formatDate(workflow.createdAt)}</Text>
          </View>
        </View>

        {/* Workflow Description */}
        {cleanDescription && (
          <View style={[styles.descriptionSection, styles.longContentContainer]}>
            <Text style={styles.descriptionTitle}>Description</Text>
            <Text style={styles.descriptionText}>{cleanDescription}</Text>
          </View>
        )}

        {/* Footer */}
        <Text style={styles.footer}>
          Generated on {new Date().toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          })} • Workflow ID: {workflow.id}
        </Text>
      </Page>
    </Document>
  );
};

export default WorkflowPDFDocument;