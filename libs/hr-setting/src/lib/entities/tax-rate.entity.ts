import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { BaseEntity } from '@igov/common';
import { ITaxRate } from '../interfaces';

@Entity('tax_rates')
export class TaxRate extends BaseEntity implements ITaxRate {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 255 })
  band!: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  rate!: number;
}
