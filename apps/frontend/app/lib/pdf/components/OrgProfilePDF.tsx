import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { organizationProfileApiService } from '@/services/api/organizationProfileService';
import { OrganizationProfileResponseDto } from '@/services/api/types/organizationProfile.types';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    width: 64,
    height: 64,
    marginBottom: 10,
  },
  orgName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    color: '#333333',
  },
  orgDetails: {
    alignItems: 'center',
  },
  orgDetail: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 2,
    lineHeight: 1.4,
  },
});

interface OrgProfilePDFProps {
  profile?: OrganizationProfileResponseDto | null;
}

// Logo component with error handling and fallback
const PDFLogo: React.FC<{ src: string }> = ({ src }) => {
  return (
    <Image 
      style={styles.logo} 
      src={src}
    />
  );
};

const OrgProfilePDF: React.FC<OrgProfilePDFProps> = ({ profile }) => {
  const [orgProfile, setOrgProfile] = useState<OrganizationProfileResponseDto | null>(profile || null);
  const [loading, setLoading] = useState(!profile);

  useEffect(() => {
    if (!profile) {
      const fetchProfile = async () => {
        try {
          setLoading(true);
          const profileData = await organizationProfileApiService.getOrganizationProfile();
          setOrgProfile(profileData);
        } catch (error) {
          console.error('Error fetching organization profile for PDF:', error);
          // Use fallback data if fetch fails
          setOrgProfile({
            id: 'fallback',
            name: 'Organization Name',
            address: 'Organization Address',
            phoneNumber: 'Phone Number',
            email: 'Email Address',
            website: 'Website',
          });
        } finally {
          setLoading(false);
        }
      };

      fetchProfile();
    }
  }, [profile]);

  if (loading || !orgProfile) {
    return (
      <View style={styles.container}>
        <Text style={styles.orgName}>Loading Organization Profile...</Text>
      </View>
    );
  }

  // Function to get the appropriate logo URL for PDF generation
  const getAbsoluteImageUrl = (logoUrl: string | undefined): string | null => {
    if (!logoUrl) {
      return `${window.location.origin}/assets/images/logo/logo-sm.png`;
    }
    
    // If already absolute URL (S3), use proxy to avoid CORS issues with @react-pdf/renderer
    if (logoUrl.startsWith('http://') || logoUrl.startsWith('https://')) {
      return `${window.location.origin}/api/proxy-image?url=${encodeURIComponent(logoUrl)}`;
    }
    
    // If relative URL, convert to absolute using current origin
    if (logoUrl.startsWith('/')) {
      return `${window.location.origin}${logoUrl}`;
    }
    
    // Default fallback
    return `${window.location.origin}/assets/images/logo/logo-sm.png`;
  };

  const logoSrc = getAbsoluteImageUrl(orgProfile.logoUrl);

  return (
    <View style={styles.container}>
      {/* Organization Logo */}
      {logoSrc ? (
        <PDFLogo src={logoSrc} />
      ) : (
        <View style={[styles.logo, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#e0e0e0', borderRadius: 32 }]}>
          <Text style={{ fontSize: 12, color: '#666', fontWeight: 'bold' }}>LOGO</Text>
        </View>
      )}
      
      <Text style={styles.orgName}>{orgProfile.name}</Text>
      
      <View style={styles.orgDetails}>
        {orgProfile.address && (
          <Text style={styles.orgDetail}>{orgProfile.address}</Text>
        )}
        {orgProfile.phoneNumber && (
          <Text style={styles.orgDetail}>{orgProfile.phoneNumber}</Text>
        )}
        {orgProfile.email && (
          <Text style={styles.orgDetail}>{orgProfile.email}</Text>
        )}
        {orgProfile.website && (
          <Text style={styles.orgDetail}>{orgProfile.website}</Text>
        )}
      </View>
    </View>
  );
};

export default OrgProfilePDF;