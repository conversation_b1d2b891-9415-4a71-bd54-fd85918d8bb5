/**
 * Represents the configuration for a specific upload folder
 */
export interface UploadFolderConfig {
  /** Maximum file size in bytes */
  maxFileSize: number;
  /** Allowed MIME types */
  allowedMimeTypes: string[];
  /** Whether to allow multiple files (for future use) */
  multiple?: boolean;
}

/**
 * Type representing the available upload folders
 */
export type UploadFolderType =
  | 'profile-pictures'
  | 'signatures'
  | 'documents'
  | 'temporary'
  | 'attachments'
  | string; // Allow string literals for future extensibility

/**
 * Configuration for all upload folders
 */
export const UPLOAD_CONFIG: Record<UploadFolderType, UploadFolderConfig> = {
  'profile-pictures': {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  signatures: {
    maxFileSize: 2 * 1024 * 1024, // 2MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
  },
  documents: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['*/*'], // All file types
  },
  temporary: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['*/*'], // All file types
  },
  attachments: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
      'text/plain',
      'application/rtf',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ],
  },
};

/**
 * Represents the metadata for an uploaded file
 */
export interface UploadedFile {
  /** Full public URL to access the file */
  url: string;
  /** S3 object key (path in the bucket) */
  key: string;
  /** Original file name */
  fileName: string;
  /** File size in bytes */
  size: number;
  /** MIME type of the file */
  type: string;
  /** ISO timestamp of when the file was uploaded */
  uploadedAt: string;
  /** The folder where the file was uploaded */
  folder: UploadFolderType;
}
