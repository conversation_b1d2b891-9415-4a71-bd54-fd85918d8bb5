'use client';

import Typography from '@mui/material/Typography';
import { motion } from 'framer-motion';
import Button from '@mui/material/Button';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@fuse/core/Link';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import PageBreadcrumb from '@/components/PageBreadcrumb';
import useGDriveData from './hooks/useGDriveData';
import React, { useState, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
} from '@mui/material';
import { useAppDispatch } from '@/store/hooks';
import FuseUtils from '@fuse/utils';
import { showMessage } from '@fuse/core/FuseMessage/fuseMessageSlice';
import { useDropzone } from 'react-dropzone';
import { useGDriveUpload } from './hooks/useGDriveUpload';
import {
  Alert,
  AlertTitle,
} from '@mui/material';
import {
  acceptStyle,
  baseStyle,
  focusedStyle,
  img,
  rejectStyle,
  thumb,
  thumbInner,
  thumbsContainer,
} from './upload-styles';

/**
 * The GDrive header, aligned with TasksHeader structure.
 */
function GDriveHeader() {
  const { path, createFolder, refetch, currentFolderId } = useGDriveData();
  const dispatch = useAppDispatch();
  const baseGDrivePath = '/portal/e-office/g-drive';

  // State for create folder dialog
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [folderName, setFolderName] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // State for file upload dialog
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  
  // Use the new upload hook
  const {
    uploadedFiles,
    validationErrors,
    isUploading,
    handleFileChange,
    removeFile,
    uploadFiles,
    reset: resetUpload,
    formatFileSize,
  } = useGDriveUpload();

  // Handler for upload button
  const handleUploadClick = () => {
    setIsUploadOpen(true);
  };

  // Handler for new folder button
  const handleNewFolderClick = () => {
    setIsCreateFolderOpen(true);
  };

  // Handler for creating folder
  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      dispatch(
        showMessage({
          message: 'Please enter a folder name',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
      return;
    }

    setIsCreating(true);
    try {
      await createFolder(folderName.trim());
      await refetch(); // Add explicit refetch call
      setIsCreateFolderOpen(false);
      setFolderName('');
      dispatch(
        showMessage({
          message: 'Folder created successfully',
          variant: 'success',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    } catch (error: any) {
      console.error('Failed to create folder:', error);
      dispatch(
        showMessage({
          message: error.message || 'Failed to create folder',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    } finally {
      setIsCreating(false);
    }
  };

  // Handler for canceling folder creation
  const handleCancelCreateFolder = () => {
    setIsCreateFolderOpen(false);
    setFolderName('');
    setIsCreating(false);
  };

  // Dropzone configuration
  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.png', '.jpg', '.svg'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
    },
    maxFiles: 10,
    maxSize: 10485760, // 10MB
    onDrop: (acceptedFiles, fileRejections) => {
      handleFileChange(acceptedFiles, fileRejections);
    },
  });

  const dropzoneStyle = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject],
  );

  // Handler for uploading files
  const handleUploadFiles = async () => {
    if (uploadedFiles.length === 0) {
      dispatch(
        showMessage({
          message: 'Please select files to upload',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
      return;
    }

    try {
      const success = await uploadFiles(currentFolderId || undefined);
      
      if (success) {
        setIsUploadOpen(false);
        resetUpload();
        // Refresh the data to show newly uploaded files
        await refetch();
        dispatch(
          showMessage({
            message: `Files uploaded successfully`,
            variant: 'success',
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'right',
            },
          }),
        );
      }
    } catch (error: any) {
      console.error('Failed to upload files:', error);
      dispatch(
        showMessage({
          message: error.message || 'Failed to upload files',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    }
  };

  // Handler for canceling upload
  const handleCancelUpload = () => {
    setIsUploadOpen(false);
    resetUpload();
  };

  return (
    <div className="flex grow-0 flex-1 w-full items-center justify-between space-y-2 sm:space-y-0 py-6 sm:py-8 md:px-16 px-4">
      {/* Left Section: Breadcrumbs, Title, Subtitle, GDrive Path */}
      <motion.span initial={{ x: -20 }} animate={{ x: 0, transition: { delay: 0.2 } }}>
        <div>
          <PageBreadcrumb className="mb-2" />
          <Typography className="text-4xl font-extrabold leading-none tracking-tight text-gray-900">
            GDrive
          </Typography>
          <Typography className="font-medium tracking-tight" color="text.secondary">
            Browse and manage your files and folders
          </Typography>

          {/* GDrive dynamic path breadcrumbs */}
          <div className="mt-2">
            <Breadcrumbs aria-label="breadcrumb" separator={<NavigateNextIcon fontSize="small" />}>
              {/* Link to GDrive root, shown unless path itself is already effectively root */}
              {path && path.length > 0 && (
                <Link href={baseGDrivePath} className="text-sm hover:underline">
                  Main Drive
                </Link>
              )}
              {/* If at root (path is empty), show "My Drive" as current location */}
              {(!path || path.length === 0) && (
                <Typography color="text.secondary" className="text-sm">
                  Main Drive
                </Typography>
              )}

              {path?.map((item, index) =>
                index + 1 === path.length ? (
                  <Typography key={index} color="text.primary" className="text-sm">
                    {item?.folderName}
                  </Typography>
                ) : (
                  <Link
                    key={index}
                    href={`${baseGDrivePath}/${item?.id}`}
                    className="text-sm hover:underline"
                  >
                    {item?.folderName}
                  </Link>
                ),
              )}
            </Breadcrumbs>
          </div>
        </div>
      </motion.span>

      {/* Right Section: Action Buttons */}
      <div className="flex flex-1 items-center justify-end space-x-2">
        <motion.div
          className="flex grow-0"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0, transition: { delay: 0.2 } }}
        >
          <Button
            className="whitespace-nowrap mx-1"
            variant="outlined"
            color="primary"
            startIcon={<FuseSvgIcon size={20}>heroicons-outline:arrow-up-tray</FuseSvgIcon>}
            onClick={handleUploadClick}
          >
            <span className="mx-1 sm:mx-2">Upload</span>
          </Button>
          <Button
            className="whitespace-nowrap mx-1"
            variant="contained"
            color="secondary"
            startIcon={<FuseSvgIcon size={20}>heroicons-outline:plus-circle</FuseSvgIcon>}
            onClick={handleNewFolderClick}
          >
            <span className="mx-1 sm:mx-2">New Folder</span>
          </Button>
        </motion.div>
      </div>

      {/* Create Folder Dialog */}
      <Dialog 
        open={isCreateFolderOpen} 
        onClose={handleCancelCreateFolder} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: { minWidth: 400 }
        }}
      >
        <DialogTitle className="pb-2">
          <Typography variant="h6" component="div" className="font-semibold">
            Create New Folder
          </Typography>
        </DialogTitle>
        <DialogContent className="pb-4">
          <TextField
            autoFocus
            margin="normal"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isCreating) {
                handleCreateFolder();
              }
            }}
            disabled={isCreating}
            placeholder="Enter folder name..."
          />
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button onClick={handleCancelCreateFolder} disabled={isCreating} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleCreateFolder}
            variant="contained"
            disabled={isCreating || !folderName.trim()}
            startIcon={isCreating ? <CircularProgress size={16} /> : null}
            className="ml-2"
          >
            {isCreating ? 'Creating...' : 'Create Folder'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Files Dialog */}
      <Dialog 
        open={isUploadOpen} 
        onClose={handleCancelUpload} 
        maxWidth="sm" 
        fullWidth
        PaperProps={{
          sx: { minWidth: 400 }
        }}
      >
        <DialogTitle className="pb-2">
          <Typography variant="h6" component="div" className="font-semibold">
            Upload Files
          </Typography>
        </DialogTitle>
        <DialogContent>
          <div className="mt-2 mb-4">
            <div {...getRootProps({ style: dropzoneStyle })}>
              <input {...getInputProps()} />
              <p>Drag 'n' drop files here, or click to select files</p>
              <small>Max 10 files, 10MB each. Allowed: images, PDF, documents, spreadsheets, presentations.</small>
            </div>
            {validationErrors && validationErrors.length > 0 && (
              <Alert severity="warning" className="mt-2">
                <AlertTitle>File Issues</AlertTitle>
                <ul>
                  {validationErrors.map((err, index) => (
                    <li key={index}>
                      {err.fileName}: {err.message}
                    </li>
                  ))}
                </ul>
              </Alert>
            )}
            <aside style={{ ...thumbsContainer, marginTop: '16px' }}>
              {uploadedFiles.map((upFile) => (
                <div
                  className="flex items-center gap-3 mb-2 p-2 border rounded"
                  key={upFile.id}
                  style={{ width: '100%' }}
                >
                  <div style={thumb}>
                    <div style={thumbInner}>
                      {upFile.file.type === 'application/pdf' ? (
                        <img
                          src="/assets/images/icons/pdf-icon.png"
                          alt="PDF file"
                          style={{ ...img, objectFit: 'contain' as const }}
                        />
                      ) : upFile.preview ? (
                        <img src={upFile.preview} style={img} alt={upFile.file.name} />
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-gray-100 text-gray-500 text-xs">
                          {upFile.file.name.split('.').pop()?.toUpperCase() || 'FILE'}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="file-info flex flex-col flex-grow">
                    <Typography variant="subtitle1" className="">
                      {upFile.file.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {upFile.file.type || 'Unknown type'} • {formatFileSize(upFile.file.size)}
                    </Typography>
                    {upFile.error && (
                      <Typography variant="caption" color="error">
                        Error: {upFile.error}
                      </Typography>
                    )}
                    {upFile.status === 'uploading' && (
                      <Typography variant="caption" color="textSecondary">
                        Uploading: {upFile.progress}%
                      </Typography>
                    )}
                    {upFile.status === 'completed' && (
                      <Typography variant="caption" color="success.main">
                        Uploaded
                      </Typography>
                    )}
                  </div>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => removeFile(upFile.id)}
                    disabled={isUploading || upFile.status === 'uploading'}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </aside>
          </div>
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button onClick={handleCancelUpload} disabled={isUploading} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleUploadFiles}
            variant="contained"
            disabled={isUploading || uploadedFiles.length === 0}
            startIcon={isUploading ? <CircularProgress size={16} /> : null}
            className="ml-2"
          >
            {isUploading ? 'Uploading...' : `Upload ${uploadedFiles.length} file(s)`}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default GDriveHeader;
