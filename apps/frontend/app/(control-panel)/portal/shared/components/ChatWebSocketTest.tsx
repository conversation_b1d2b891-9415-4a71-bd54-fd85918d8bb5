'use client';

import React, { useEffect, useState } from 'react';
import { Button, Paper, Typography, Box, Chip } from '@mui/material';
import { useChatWebSocket } from '../hooks/useChatWebSocket';

/**
 * Test component for WebSocket chat functionality
 * This is a temporary component to verify our WebSocket connection works
 */
export function ChatWebSocketTest() {
  const chat = useChatWebSocket();
  const [messages, setMessages] = useState<any[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[ChatTest] ${message}`);
  };

  // Set up event listeners
  useEffect(() => {
    const unsubscribeMessage = chat.onMessage((message) => {
      addLog(`Received message: ${message.content} from ${message.senderId}`);
      setMessages(prev => [...prev, message]);
    });

    const unsubscribeSent = chat.onMessageSent((data) => {
      addLog(`Message sent with ID: ${data.messageId}`);
    });

    const unsubscribeDelivered = chat.onMessageDelivered((data) => {
      addLog(`Message delivered: ${data.messageId}`);
    });

    const unsubscribeRead = chat.onMessageRead((data) => {
      addLog(`Message read: ${data.messageId}`);
    });

    const unsubscribeTypingStart = chat.onUserTypingStart((data) => {
      addLog(`User ${data.from} started typing`);
    });

    const unsubscribeTypingStop = chat.onUserTypingStop((data) => {
      addLog(`User ${data.from} stopped typing`);
    });

    return () => {
      unsubscribeMessage();
      unsubscribeSent();
      unsubscribeDelivered();
      unsubscribeRead();
      unsubscribeTypingStart();
      unsubscribeTypingStop();
    };
  }, [chat]);

  const handleConnect = async () => {
    try {
      addLog('Attempting to connect...');
      await chat.connect();
      addLog('Connection initiated');
    } catch (error: any) {
      addLog(`Connection failed: ${error.message}`);
    }
  };

  const handleDisconnect = () => {
    addLog('Disconnecting...');
    chat.disconnect();
  };

  const handleSendTestMessage = () => {
    try {
      // Send a test message to a dummy receiver
      chat.sendMessage({
        receiverId: 'test-receiver-123',
        content: 'Hello from WebSocket test!'
      });
      addLog('Test message sent');
    } catch (error: any) {
      addLog(`Failed to send message: ${error.message}`);
    }
  };

  const getStatusColor = () => {
    switch (chat.connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, m: 2, maxWidth: 800 }}>
      <Typography variant="h5" gutterBottom>
        WebSocket Chat Test
      </Typography>
      
      {/* Connection Status */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Connection Status
        </Typography>
        <Chip 
          label={chat.connectionStatus.toUpperCase()}
          color={getStatusColor()}
          variant="filled"
          sx={{ mr: 2 }}
        />
        <Chip 
          label={`Connected: ${chat.isConnected}`}
          color={chat.isConnected ? 'success' : 'default'}
          variant="outlined"
          sx={{ mr: 2 }}
        />
        <Chip 
          label={`Connecting: ${chat.isConnecting}`}
          color={chat.isConnecting ? 'warning' : 'default'}
          variant="outlined"
          sx={{ mr: 2 }}
        />
        <Chip 
          label={`Has Error: ${chat.hasError}`}
          color={chat.hasError ? 'error' : 'default'}
          variant="outlined"
        />
      </Box>

      {/* Action Buttons */}
      <Box sx={{ mb: 3 }}>
        <Button 
          variant="contained" 
          onClick={handleConnect}
          disabled={chat.isConnected || chat.isConnecting}
          sx={{ mr: 2 }}
        >
          Connect
        </Button>
        <Button 
          variant="outlined" 
          onClick={handleDisconnect}
          disabled={!chat.isConnected}
          sx={{ mr: 2 }}
        >
          Disconnect
        </Button>
        <Button 
          variant="contained" 
          color="secondary"
          onClick={handleSendTestMessage}
          disabled={!chat.isConnected}
        >
          Send Test Message
        </Button>
      </Box>

      {/* Typing Users */}
      {chat.typingUsers.size > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Users Typing
          </Typography>
          {Array.from(chat.typingUsers.entries()).map(([userId, userName]) => (
            <Chip key={userId} label={`${userName} is typing...`} sx={{ mr: 1 }} />
          ))}
        </Box>
      )}

      {/* Messages */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Received Messages ({messages.length})
        </Typography>
        <Paper variant="outlined" sx={{ p: 2, maxHeight: 200, overflowY: 'auto' }}>
          {messages.length === 0 ? (
            <Typography color="text.secondary">No messages received yet</Typography>
          ) : (
            messages.map((msg, index) => (
              <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                <strong>{msg.senderId}:</strong> {msg.content}
                <small style={{ marginLeft: 8, color: 'gray' }}>
                  {new Date(msg.createdAt).toLocaleTimeString()}
                </small>
              </Typography>
            ))
          )}
        </Paper>
      </Box>

      {/* Logs */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Event Logs
        </Typography>
        <Paper 
          variant="outlined" 
          sx={{ 
            p: 2, 
            maxHeight: 300, 
            overflowY: 'auto',
            backgroundColor: '#f5f5f5',
            fontFamily: 'monospace'
          }}
        >
          {logs.length === 0 ? (
            <Typography color="text.secondary">No logs yet</Typography>
          ) : (
            logs.map((log, index) => (
              <Typography 
                key={index} 
                variant="body2" 
                sx={{ 
                  fontFamily: 'monospace',
                  fontSize: '0.8rem',
                  mb: 0.5
                }}
              >
                {log}
              </Typography>
            ))
          )}
        </Paper>
      </Box>
    </Paper>
  );
}