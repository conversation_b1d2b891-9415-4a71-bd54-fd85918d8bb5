import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { QueryUserDto, PaginatedUserResponseDto } from '../dtos';
import { UserRepository } from '../repositories';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);
  constructor(private readonly userRepository: UserRepository) {}

  async getUsers(filters: QueryUserDto): Promise<PaginatedUserResponseDto> {
    try {
      const { data, total } = await this.userRepository.getUsers(filters);
      return { data, total };
    } catch (error) {
      this.logger.error('Error while fetching users', error);
      throw new InternalServerErrorException('Error while fetching users');
    }
  }
}
