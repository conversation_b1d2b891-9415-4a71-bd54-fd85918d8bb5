import { Task } from '../task-data';
import clsx from 'clsx';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import moment from 'moment';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import Timeline from '@mui/lab/Timeline';
import TaskLogTimelineItem from './TaskLogTimelineItem';

export type TaskInfoProps = {
  task: Task;
  className?: string;
};

function TaskInfo({ task, className }: TaskInfoProps) {
  if (!task) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Cancelled':
        return 'text-red-500';
      case 'Ongoing':
        return 'text-yellow-500';
      case 'Completed':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className={clsx('w-full', className)}>
      <div className="flex items-center justify-between mb-4">
        <Typography className={clsx('text-lg font-medium', getStatusColor(task.status))}>
          {task.status}
        </Typography>
      </div>

      <Typography className="text-lg font-medium">{task.subject}</Typography>

      <Divider className="w-12 my-6 border-1" />

      <Typography className="flex items-center space-x-1.5 text-md" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Primary Executor: {task.primaryExecutor.name}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user-circle
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">Created By: {task.taskCreator.name}</span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:clock
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Due: {moment(task.dueDate).format('MMMM Do YYYY, h:mm a')}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:flag
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none capitalize">Priority: {task.priority}</span>
      </Typography>

      {task.taskExecutors.length > 0 && (
        <div className="mt-5">
          <Typography className="flex items-center space-x-1.5 text-md" color="text.secondary">
            <FuseSvgIcon color="disabled" size={20}>
              heroicons-outline:users
            </FuseSvgIcon>
            <span className="whitespace-nowrap leading-none">Executors:</span>
          </Typography>
          <AvatarGroup max={3} className="justify-end mt-2">
            {task.taskExecutors.map((executor) => (
              <Tooltip
                key={executor.id}
                title={`${executor.name} (${executor.departmentName} - ${executor.unitName})`}
                placement="top"
              >
                <Avatar alt={executor.name}>
                  {executor.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </Avatar>
              </Tooltip>
            ))}
          </AvatarGroup>
        </div>
      )}
    </div>
  );
}

export default TaskInfo;
