type Department = {
  id: string;
  name: string;
  code: string;
};

type Emp = {
  id: string;
  name: string;
  unitName: string;
  departmentName: string;
};

type CircularCategory = {
  id: string;
  title: string;
  slug: string;
  color: string;
};

type Circular = {
  id: string;
  refNo: string;
  subject: string;
  body: string;
  from: string;
  fromEmp: Emp;
  recipients: Department[];
  attachments?: CircularAttachment[];
  status: number;
  category: string;
  read: boolean;
  postedDate: string;
  postedBy: string;
};

type CircularAttachment = {
  id: string;
  circularId: string;
  name: string;
  url: string;
};

const circulars: Circular[] = [
  // Inbox circulars
  {
    id: 'c5a114f1-e9b1-4f15-9bd0-c7eea2f5be4f',
    refNo: 'CIRC-2024-001',
    subject: 'Annual Department Meeting',
    body: 'Please be informed that the annual department meeting will be held on Friday...',
    from: '520e8a72-6519-456a-82fc-8d6332d948c4',
    fromEmp: {
      id: '520e8a72-6519-456a-82fc-8d6332d948c4',
      name: '<PERSON>',
      unitName: 'Finance',
      departmentName: 'Accounting',
    },
    recipients: [
      {
        id: 'dept-001',
        name: 'IT Department',
        code: 'IT',
      },
      {
        id: 'dept-002',
        name: 'HR Department',
        code: 'HR',
      },
    ],
    attachments: [
      {
        id: 'a38f9ca0-84d6-4b59-a4b7-51d7c5c81c01',
        circularId: 'c5a114f1-e9b1-4f15-9bd0-c7eea2f5be4f',
        name: 'Meeting_Agenda.pdf',
        url: '/assets/documents/Meeting_Agenda.pdf',
      },
    ],
    status: 1,
    category: 'inbox',
    postedDate: '2024-03-20T09:30:00Z',
    postedBy: '520e8a72-6519-456a-82fc-8d6332d948c4',
    read: true,
  },
  // Add more mock data as needed
];

const circularCategories: CircularCategory[] = [
  {
    id: 'circular.inbox',
    title: 'Inbox',
    slug: 'inbox',
    color: '#2196f3',
  },
  {
    id: 'circular.outbox',
    title: 'Outbox',
    slug: 'outbox',
    color: '#ffc107',
  },
  {
    id: 'circular.sent',
    title: 'Sent',
    slug: 'sent',
    color: '#4caf50',
  },
];

export type { Circular, Department, Emp, CircularAttachment, CircularCategory };
export { circulars, circularCategories };
