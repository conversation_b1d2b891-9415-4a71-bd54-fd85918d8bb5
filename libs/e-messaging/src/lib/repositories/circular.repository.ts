import { BaseRepository } from '@igov/common';
import { Injectable, Logger } from '@nestjs/common';
import { ICircular } from '../interfaces';
import { CircularEntity } from '../entity';
import { QueryCircularDto } from '../dtos';

const parseCommaSeparatedIds = (ids?: string | string[]): string[] | undefined =>
  typeof ids === 'string' ? ids.split(',').map((id) => id.trim()) : ids;

@Injectable()
export class CircularRepository extends BaseRepository<CircularEntity> {
  private readonly logger = new Logger(CircularRepository.name);
  override entityClassName = CircularEntity;

  async findOne(params: {
    id: string;
    employeeId?: string;
    relations?: string[];
  }): Promise<CircularEntity | null> {
    const { id, employeeId, relations = [] } = params;

    const queryBuilder = this.repository.createQueryBuilder('circulars');

    queryBuilder
      .addSelect(['circulars.status', 'circulars.createdAt', 'circulars.updatedAt'])
      .leftJoin('circulars.createdByEmployee', 'createdByEmployee')
      .addSelect([
        'createdByEmployee.userId',
        'createdByEmployee.firstName',
        'createdByEmployee.lastName',
      ])
      .leftJoin('circulars.fromEmployee', 'fromEmployee')
      .leftJoin('circulars.circularDepartment', 'department')
      .addSelect(['fromEmployee.userId', 'fromEmployee.firstName', 'fromEmployee.lastName']);

    const needsDepartments = relations.includes('circularDepartment');

    if (needsDepartments) {
      queryBuilder.leftJoinAndSelect('circulars.circularDepartment', 'circularDepartment');
    }

    queryBuilder.where('circulars.id = :id', { id });

    if (employeeId) {
      queryBuilder.andWhere(
        `(
        fromEmployee.userId = :employeeId OR
        createdByEmployee.userId = :employeeId
      )`,
        { employeeId },
      );
    }

    return queryBuilder.getOne();
  }
  async findAll(
    query: Partial<QueryCircularDto>,
  ): Promise<{ data: CircularEntity[]; total: number }> {
    const {
      skip,
      limit,
      search,
      from,
      fromDate,
      toDate,
      createdBy,
      departmentId,
      useOrConditionForEmployees,
      status,
    } = query;
    const parsedDepartmentIds = parseCommaSeparatedIds(departmentId);
    const queryBuilder = this.repository
      .createQueryBuilder('circulars')
      .leftJoin('circulars.createdByEmployee', 'createdByEmployee')
      .addSelect([
        'createdByEmployee.userId',
        'createdByEmployee.firstName',
        'createdByEmployee.lastName',
        'createdByEmployee.departmentId',
      ])
      .leftJoin('circulars.fromEmployee', 'fromEmployee')
      .addSelect([
        'fromEmployee.userId',
        'fromEmployee.firstName',
        'fromEmployee.lastName',
        'fromEmployee.departmentId',
      ])
      .leftJoin('circulars.circularDepartment', 'department')
      .addSelect(['department.code', 'department.name', 'department.commRef', 'department.id']);

    if (search) {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere('(circulars.title ILIKE :search OR circulars.body ILIKE :search)', {
        search: searchTerm,
      });
    }

    if (useOrConditionForEmployees) {
      // Group OR conditions
      const orConditions: string[] = [];
      const orParams: Record<string, string> = {};

      if (createdBy) {
        orConditions.push('circulars.createdBy = :createdBy');
        orParams['createdBy'] = createdBy;
      }
      if (from) {
        orConditions.push('circulars.from = :from');
        orParams['from'] = from;
      }

      if (orConditions.length > 0) {
        queryBuilder.andWhere(`(${orConditions.join(' OR ')})`, orParams);
      }
    } else {
      // Use AND conditions individually
      if (createdBy) {
        queryBuilder.andWhere('circulars.createdBy = :createdBy', { createdBy });
      }
      if (from) {
        queryBuilder.andWhere('circulars.from = :from', { from });
      }
    }
    if (fromDate) {
      queryBuilder.andWhere('circulars.createdAt >= :fromDate', { fromDate });
    }
    if (parsedDepartmentIds?.length) {
      queryBuilder.andWhere('department.id  IN (:...departmentIds)', {
        departmentIds: parsedDepartmentIds,
      });
    }

    if (toDate) {
      queryBuilder.andWhere('circulars.createdAt <= :toDate', { toDate });
    }
    if (status) {
      queryBuilder.andWhere('circulars.status = :status', { status });
    }

    // Order by creation date (most recent first)
    queryBuilder.orderBy('circulars.createdAt', 'DESC');

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total circulars found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const circulars = await queryBuilder.getMany();

    return { data: circulars, total: totalCount };
  }

  async save(circular: Partial<ICircular>): Promise<CircularEntity> {
    return this.repository.save(circular);
  }

  async findOneById(id: string): Promise<CircularEntity | null> {
    return await this.repository.findOne({ where: { id } });
  }
  async findOneCircularByAuthorizedEmployee(params: {
    id: string;
    employeeId: string;
  }): Promise<CircularEntity | null> {
    const { id, employeeId } = params;

    const queryBuilder = this.repository.createQueryBuilder('circulars');

    queryBuilder
      .leftJoin('circulars.fromEmployee', 'fromEmployee')
      .leftJoin('circulars.createdByEmployee', 'createdByEmployee');

    queryBuilder.where('circulars.id = :id', { id });

    queryBuilder.andWhere(
      `(
      circulars.from = :employeeId OR
      circulars.createdBy = :employeeId
    )`,
      { employeeId },
    );

    return queryBuilder.getOne();
  }
}
