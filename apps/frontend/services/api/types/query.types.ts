// Query status enum matching backend
export enum QueryStatusEnums {
  Draft = 'draft',
  Issued = 'issued',
  Open = 'open',
  Expired = 'expired',
  Closed = 'closed',
}

// Attachment DTO matching backend structure (reusing from memo.types.ts pattern)
export interface AttachmentDto {
  name: string;
  url: string;
  type?: string;
}

// Employee interface (simplified from the full API response)
export interface QueryEmployeeDto {
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  photoUrl?: string;
  presentPosting?: string;
}

// History entry interface
export interface QueryHistoryDto {
  id: string;
  queryId: string;
  performedBy: string;
  action: string;
  snapshot: any;
  createdAt: string;
}

// Query announcement/reply interface for responses
export interface QueryAnnouncementDto {
  id: string;
  queryId: string;
  comment: string;
  attachment?: AttachmentDto | AttachmentDto[]; // Support both single and array
  employeeId: string;
  employee: QueryEmployeeDto;
  createdAt: string;
  updatedAt: string;
}

// Main query response DTO matching backend
export interface QueryResponseDto {
  id: string;
  reference: string;
  title: string;
  message: string;
  attachments?: AttachmentDto[];
  employeeId: string;
  draftedBy: string;
  issuedBy: string;
  status: QueryStatusEnums;
  dueDate?: Date;
  employee: QueryEmployeeDto;
  draftedByEmployee: QueryEmployeeDto;
  issuedByEmployee: QueryEmployeeDto;
  history?: QueryHistoryDto[];
  announcements?: QueryAnnouncementDto[];
  createdAt: Date;
  updatedAt: Date;
}

// Paginated response DTO
export interface PaginatedQueryResponseDto {
  total: number;
  data: QueryResponseDto[];
}

// Create query DTO for new queries
export interface CreateQueryDto {
  reference: string;
  title: string;
  message: string;
  attachments?: AttachmentDto[];
  employeeId: string;
  issuedBy: string;
  dueDate?: string;
}

// Update query DTO
export interface UpdateQueryDto {
  title?: string;
  message?: string;
  status?: QueryStatusEnums;
}

// Create query announcement/reply DTO
export interface CreateQueryAnnouncementDto {
  comment: string;
  attachment?: AttachmentDto; // Currently supporting single attachment
  queryId: string;
}

// Query announcement response DTO
export interface QueryAnnouncementResponseDto {
  comment: string;
  attachment?: AttachmentDto[];
  employeeId: string;
}

// Query options for filtering and pagination
export interface QueryQueryOptions {
  skip?: number;
  limit?: number;
  search?: string;
  employeeId?: string;
  draftedBy?: string;
  issuedBy?: string;
  status?: QueryStatusEnums;
  startDate?: Date;
  endDate?: Date;
  useOrConditionForEmployees?: boolean;
}

// Filter DTO matching backend
export interface QueryFilterDto {
  skip?: number;
  limit?: number;
  search?: string;
  employeeId?: string;
  draftedBy?: string;
  issuedBy?: string;
  status?: QueryStatusEnums;
  fromDate?: string;
  toDate?: string;
  useOrConditionForEmployees?: boolean;
}