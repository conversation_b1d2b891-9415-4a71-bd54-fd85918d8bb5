/**
 * @file authService.ts
 * @description Service for authentication-related API operations
 *
 * This service handles all authentication operations like login, registration,
 * and user role verification. It uses the apiClient to communicate with the backend
 * and manages auth tokens and user data via the client's storage functionality.
 *
 * How to use:
 * ```typescript
 * import { authApiService } from './authService';
 *
 * // Login
 * await authApiService.login({ email: '<EMAIL>', password: 'password' });
 *
 * // Check authentication and roles
 * const isLoggedIn = authApiService.isAuthenticated();
 * const isAdmin = authApiService.hasRole(UserRole.ADMIN);
 *
 * // Logout
 * authApiService.logout();
 * ```
 */

import { apiClient } from './apiClient';
import { ChangePasswordDto, LoginDto, LoginResponseDto, User, UserRole } from './types/auth.types';

/**
 * Service for authentication related API calls
 *
 * @remarks
 * This service provides methods for user authentication operations and
 * role-based access control checks. It's a wrapper around the apiClient
 * that adds authentication-specific functionality.
 */
class AuthApiService {
  /** Base API path for auth endpoints */
  private baseUrl = '/auth';

  /**
   * Authenticate a user with email and password
   *
   * @param loginDto - The login credentials (email and password)
   * @returns Promise with login response containing token and user info
   *
   * @throws Will throw an error if login fails, with potential validation errors
   * in the error.response.data.message field (which may be an array of strings)
   *
   * @example
   * ```typescript
   * try {
   *   const result = await authApiService.login({
   *     email: '<EMAIL>',
   *     password: 'password123'
   *   });
   *   console.log('Logged in successfully:', result);
   * } catch (error) {
   *   console.error('Login failed:', error.response?.data?.message);
   * }
   * ```
   */
  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    try {
      // Validate login data before sending to avoid unnecessary API calls
      if (!loginDto.email || !loginDto.password) {
        throw new Error('Email and password are required');
      }

      const response = await apiClient.post<LoginResponseDto>(`${this.baseUrl}/login`, loginDto);
      console.log({ response });

      // Store auth data for future requests
      if (response.token) {
        apiClient.setAuthData(response.token, response as unknown as User);
      } else {
        console.warn('No token received in login response');
      }

      return response;
    } catch (error: any) {
      // Log only essential error information
      console.error('Login failed', {
        message: error.message,
        status: error.response?.status,
      });
      throw error;
    }
  }

  /**
   * Log out the current user by clearing auth data
   *
   * @remarks
   * This method only clears client-side auth data (token and user).
   * It does not make an API call to invalidate the token on the server.
   */
  logout(): void {
    apiClient.clearAuthData();
  }

  /**
   * Change the current user's password
   * @param changePasswordDto - Object containing current password, new password, and confirmation
   * @returns Promise that resolves when password is successfully changed
   * 
   * @throws Will throw an error if password change fails, with potential validation errors
   * in the error.response.data.message field
   * 
   * @example
   * ```typescript
   * try {
   *   await authApiService.changePassword({
   *     currentPassword: 'oldPassword123',
   *     newPassword: 'newSecurePassword123!',
   *     confirmPassword: 'newSecurePassword123!'
   *   });
   *   // Handle success (e.g., show success message)
   * } catch (error) {
   *   console.error('Password change failed:', error.response?.data?.message);
   * }
   * ```
   */
  async changePassword(changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // Basic validation before API call
      if (!changePasswordDto.currentPassword || !changePasswordDto.newPassword || !changePasswordDto.confirmPassword) {
        throw new Error('All password fields are required');
      }

      if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
        throw new Error('New password and confirmation do not match');
      }

      await apiClient.post<void>(`${this.baseUrl}/change-password`, changePasswordDto);
    } catch (error: any) {
      console.error('Password change failed', {
        message: error.message,
        status: error.response?.status,
      });
      throw error;
    }
  }

  /**
   * Server-side logout (placeholder for future implementation)
   *
   * @remarks
   * This method will make an API call to invalidate the token on the server.
   * For now, it's just a placeholder that calls the client-side logout.
   */
  async logoutApi(): Promise<void> {
    // This would be implemented when the API supports it
    // await apiClient.post(`${this.baseUrl}/logout`);

    // For now, just do client-side logout
    this.logout();
  }

  /**
   * Check if the user is currently authenticated
   *
   * @returns Boolean indicating if the user is authenticated
   *
   * @remarks
   * This checks if a valid auth token exists in storage.
   * It does not verify if the token is still valid on the server.
   */
  isAuthenticated(): boolean {
    return apiClient.isAuthenticated();
  }

  /**
   * Get the current authenticated user
   *
   * @returns User object or null if not authenticated
   */
  getCurrentUser(): User | null {
    return apiClient.getCurrentUser();
  }

  /**
   * Check if the current user has a specific role
   *
   * @param role - The role to check
   * @returns Boolean indicating if the user has the specified role
   */
  hasRole(role: UserRole): boolean {
    const user = this.getCurrentUser();
    return !!user && user.role === role;
  }

  /**
   * Check if the current user has any of the specified roles
   *
   * @param roles - Array of roles to check
   * @returns Boolean indicating if the user has any of the specified roles
   */
  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.getCurrentUser();
    return !!user && roles.includes(user.role);
  }
}

// Export a single instance for easy import
export const authApiService = new AuthApiService();
