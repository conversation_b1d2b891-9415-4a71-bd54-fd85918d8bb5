import { MigrationInterface, QueryRunner } from 'typeorm';

export class WorflowCommentActorsUpdate1750161261654 implements MigrationInterface {
  name = 'WorflowCommentActorsUpdate1750161261654';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_actors_auth_status_enum" AS ENUM('Pending', 'Approve', 'Decline')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."workflow_actors_marked_as_final_enum" AS ENUM('Yes', 'No')`,
    );
    await queryRunner.query(
      `CREATE TABLE "workflow_actors" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "employee_id" uuid NOT NULL, "action_date" TIMESTAMP NOT NULL DEFAULT NOW(), "auth_status" "public"."workflow_actors_auth_status_enum" NOT NULL DEFAULT 'Pending', "marked_as_final" "public"."workflow_actors_marked_as_final_enum" NOT NULL DEFAULT 'No', "workflow_id" uuid NOT NULL, CONSTRAINT "PK_53a8030df465f3fad1a9060dbc3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "workflow_comments" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "employee_id" uuid NOT NULL, "workflow_id" uuid NOT NULL, "comment" text NOT NULL, "date" TIMESTAMP NOT NULL DEFAULT NOW(), CONSTRAINT "PK_a844947bcc91e663addf12ae2c6" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_actors" ADD CONSTRAINT "FK_ef3cfb7e0b8e5755b28816de568" FOREIGN KEY ("employee_id") REFERENCES "employees"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_comments" ADD CONSTRAINT "FK_ffdf6d6dcb933611d6d8d914cae" FOREIGN KEY ("workflow_id") REFERENCES "workflows"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflows" ADD CONSTRAINT "FK_b10f29250c6cee791f8188517f3" FOREIGN KEY ("workflow_type_id") REFERENCES "workflow_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workflows" DROP CONSTRAINT "FK_b10f29250c6cee791f8188517f3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_comments" DROP CONSTRAINT "FK_ffdf6d6dcb933611d6d8d914cae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workflow_actors" DROP CONSTRAINT "FK_ef3cfb7e0b8e5755b28816de568"`,
    );
    await queryRunner.query(`DROP TABLE "workflow_comments"`);
    await queryRunner.query(`DROP TABLE "workflow_actors"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_actors_marked_as_final_enum"`);
    await queryRunner.query(`DROP TYPE "public"."workflow_actors_auth_status_enum"`);
  }
}
