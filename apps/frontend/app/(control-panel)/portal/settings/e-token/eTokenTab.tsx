'use client';

import { useState, useEffect } from 'react';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { Alert, Button, Divider, InputAdornment, CircularProgress, Box } from '@mui/material';
import { useSnackbar } from 'notistack';

import { useAccountDetails } from './useAccountDetails';
import { useEToken } from './useEToken';

function ETokentab() {
  const { enqueueSnackbar } = useSnackbar();

  // Get account details and eToken state
  const { employee, isLoading: isAccountLoading, error: accountError } = useAccountDetails();
  const {
    eToken,
    isLoading: isETokenLoading,
    error: eTokenError,
    setToken,
    clearError,
  } = useEToken();

  // Form state
  const [token, setTokenValue] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTokenSet, setIsTokenSet] = useState(false);

  // Update isTokenSet when eToken changes
  useEffect(() => {
    setIsTokenSet(!!eToken);
  }, [eToken]);

  // Show loading state while data is being fetched
  useEffect(() => {
    if (employee?.eToken) {
      setIsTokenSet(true);
    }
  }, [employee]);

  // Handle form submission for setting/updating eToken
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token || !password) {
      enqueueSnackbar('Please fill in all fields', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await setToken(token, password);
      if (success) {
        setTokenValue('');
        setPassword('');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel button click
  const handleCancel = () => {
    setTokenValue('');
    setPassword('');
    clearError();
  };

  // Show error if account details couldn't be loaded
  if (accountError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load account details: {accountError.message}
      </Alert>
    );
  }

  // Show loading state for initial data load
  // if (isAccountLoading || isETokenLoading) {
  //   return null; // Or a minimal loading indicator that doesn't take full page
  // }

  return (
    <div className="w-full max-w-5xl">
      <form onSubmit={handleSubmit}>
        <div className="w-full">
          <Typography className="text-xl">Manage your e-Token</Typography>
          <Typography color="text.secondary">
            Use your e-Token to authorize sensitive actions on your account. Confirm updates with
            your password below.
          </Typography>
        </div>

        <div className="mt-8 grid w-full gap-1.5 sm:grid-cols-4 space-y-8">
          {/* Alert when no eToken is set */}
          {!isTokenSet && (
            <div className="sm:col-span-4">
              <Alert
                severity="warning"
                sx={{
                  '& .MuiAlert-message': {
                    width: '100%',
                  },
                }}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                  <Typography variant="body2">
                    You don't have an eToken set up yet. You will need it to authorize actions on
                    your account.
                  </Typography>
                </Box>
              </Alert>
            </div>
          )}
          {/* Alert when eToken is set */}
          {isTokenSet && (
            <div className="sm:col-span-4">
              <Alert
                severity="success"
                sx={{
                  '& .MuiAlert-message': {
                    width: '100%',
                  },
                }}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                  <Typography variant="body2">Your eToken is active.</Typography>
                </Box>
              </Alert>
            </div>
          )}

          {/* Error message if any */}

          <div className="sm:col-span-4">
            <TextField
              label={isTokenSet ? 'New e-Token' : 'e-Token'}
              type="password"
              variant="outlined"
              fullWidth
              value={token}
              onChange={(e) => {
                // Only allow up to 6 characters
                const value = e.target.value.slice(0, 6);
                setTokenValue(value);
              }}
              inputProps={{
                maxLength: 6,
                inputMode: 'numeric',
                pattern: '\\d*',
              }}
              helperText={`${token.length}/6 characters`}
              FormHelperTextProps={{
                sx: {
                  textAlign: 'right',
                  mx: 0,
                },
              }}
              disabled={isSubmitting}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon size={20}>heroicons-solid:ellipsis-horizontal</FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          <div className="sm:col-span-4">
            <TextField
              label="Password Confirmation"
              type="password"
              variant="outlined"
              fullWidth
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isSubmitting}
              placeholder="Enter your password to confirm"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon size={20}>heroicons-outline:key</FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>
        </div>

        <Divider className="mb-10 mt-11 border-t border-[#DBDBDB]" />

        <div className="flex flex-col space-y-3">
          <div className="flex justify-end">
            {eTokenError && <Typography className="text-red-500 text-sm">{eTokenError}</Typography>}
          </div>
          <div className="flex items-center justify-end space-x-2">
            <Button variant="outlined" onClick={handleCancel} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting || !token || !password}
            >
              {isSubmitting ? (
                <CircularProgress size={15} color="inherit" />
              ) : isTokenSet ? (
                'Update eToken'
              ) : (
                'Set eToken'
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

export default ETokentab;
