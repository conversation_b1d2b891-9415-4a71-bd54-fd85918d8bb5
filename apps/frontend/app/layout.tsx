import clsx from 'clsx';
import '@/styles/splash-screen.css';
import '@/styles/index.css';

import generateMetadata from '../utils/generateMetadata';
import App from './App';
import { AuthProvider } from '@/contexts/AuthContext';

// eslint-disable-next-line react-refresh/only-export-components
export const metadata = await generateMetadata({
  title: 'CNX IGOV',
  description: 'CNX IGov',
  cardImage: '/card.png',
  robots: 'follow, index',
  favicon: '/favicon.ico',
  url: 'https://react-material.fusetheme.com',
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#000000" />
        <base href="/" />
        {/*
					manifest.json provides metadata used when your web app is added to the
					homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
				*/}
        <link rel="manifest" href="/manifest.json" />
        <link rel="shortcut icon" href="/favicon.ico" />

        <link
          href="/assets/fonts/material-design-icons/MaterialIconsOutlined.css"
          rel="stylesheet"
        />
        <link href="/assets/fonts/inter/inter.css" rel="stylesheet" />
        <link href="/assets/fonts/meteocons/style.css" rel="stylesheet" />
        <link href="/assets/styles/prism.css" rel="stylesheet" />
        
        {/* RichTextEditor CSS and JS */}
        <link rel="stylesheet" href="/richtexteditor/rte_theme_default.css" />
        <script src="/richtexteditor/rte.js"></script>
        <script src="/richtexteditor/plugins/all_plugins.js"></script>
        
        <noscript id="emotion-insertion-point" />
      </head>
      <body id="root" className={clsx('loading')}>
        <AuthProvider>
          <App>{children}</App>
        </AuthProvider>
      </body>
    </html>
  );
}
