import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { WorkflowService } from '../services';
import {
  AuthorizeRequestDto,
  PaginatedWorkflowResponseDto,
  WorkflowCommentDto,
  WorkflowFilterDto,
  WorkflowResponseDto,
} from '../dtos';

@ApiTags('Workflow - Self-service')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workflow-self-service')
export class WorkflowSelfServiceController {
  constructor(private readonly workflowService: WorkflowService) {}
  @Post('/authorize')
  @ApiOperation({ summary: 'Authorize request' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: WorkflowResponseDto })
  async authorizeRequest(
    @Body() dto: AuthorizeRequestDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<WorkflowResponseDto> {
    return this.workflowService.authorizeRequest({ employeeId: jwtPayload.id, data: dto });
  }
  @Post('/comment')
  @ApiOperation({ summary: 'Comment on workflow' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 201, type: WorkflowResponseDto })
  async leaveComment(
    @Body() dto: WorkflowCommentDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<WorkflowResponseDto> {
    return this.workflowService.comment(dto, jwtPayload.id);
  }
  @Get()
  @ApiOperation({ summary: 'Get all workflow requests' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: WorkflowResponseDto })
  async getAllWorkflowRequests(
    @Query() query: WorkflowFilterDto,
  ): Promise<PaginatedWorkflowResponseDto> {
    return this.workflowService.getAll(query);
  }
}
