import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { RegistryEntity } from '../entities';
import { IRegistry } from '../interfaces';
import { In } from 'typeorm';

@Injectable()
export class RegistryRepository extends BaseRepository<RegistryEntity> {
  override entityClassName = RegistryEntity;
  private readonly logger = new Logger(RegistryRepository.name);

  async findByOwnerId(ownerId: string): Promise<RegistryEntity[]> {
    return this.repository.find({ where: { ownerId } });
  }

  async findByIds(ids: string[]): Promise<RegistryEntity[]> {
    return this.repository.find({
      where: {
        id: In(ids),
      },
    });
  }

  async findAll(query: {
    ownerId?: string;
    skip?: number;
    limit?: number;
    search?: string;
  }): Promise<{ data: RegistryEntity[]; total: number }> {
    const { ownerId, skip = 0, limit = 10, search } = query;
    const queryBuilder = this.repository.createQueryBuilder('registry');

    if (search)
      queryBuilder.andWhere('registry.registryName LIKE :search', {
        search: `%${search}%`,
      });

    if (ownerId) queryBuilder.andWhere('registry.ownerId = :ownerId', { ownerId });

    queryBuilder.skip(skip).take(limit).orderBy('registry.createdAt', 'DESC');

    const [data, total] = await queryBuilder.getManyAndCount();

    this.logger.log(`Found ${data.length} registries out of ${total} total`);
    return {
      data,
      total,
    };
  }

  async findById(id: string, employeeId?: string): Promise<RegistryEntity | null> {
    return this.repository.findOne({
      where: { id, ...(employeeId && { ownerId: employeeId }) },
      relations: ['owner'],
    });
  }

  async findByName(name: string): Promise<RegistryEntity | null> {
    return this.repository.findOne({ where: { registryName: name } });
  }

  async saveRegistry(data: Partial<IRegistry>): Promise<RegistryEntity> {
    return this.repository.save(data);
  }
}
