'use client';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import PageBreadcrumb from '@/components/PageBreadcrumb';
import Typography from '@mui/material/Typography';
import { motion } from 'motion/react';

function TrainingsHeader() {
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <div className="flex grow-0 flex-1 w-full items-center justify-between space-y-2 sm:space-y-0 py-6 sm:py-8 md:px-16 px-4">
      <motion.span initial={{ x: -20 }} animate={{ x: 0, transition: { delay: 0.2 } }}>
        <div>
          <PageBreadcrumb className="mb-2" />
          <Typography className="text-4xl font-extrabold leading-none tracking-tight text-gray-900">
            Trainings
          </Typography>
          <Typography
            className="font-medium tracking-tight"
            color="text.secondary"
          >
            Explore available trainings and track your progress
          </Typography>
        </div>
      </motion.span>
    </div>
  );
}

export default TrainingsHeader;
