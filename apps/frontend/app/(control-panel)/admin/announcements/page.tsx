'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Button,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  Paper,
  ListItem,
  Typography,
  ListItemText,
  Breadcrumbs,
  Link,
  List,
} from '@mui/material';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import FusePageCarded from '@fuse/core/FusePageCarded';
import {
  Edit,
  //  Delete,
  Send,
} from 'lucide-react';
import { apiClient } from '@/services/api/apiClient';
import { enqueueSnackbar } from 'notistack';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';
import TextInput from '../../../../components/formInputs/TextInput';
import FileUpload from '../../../../components/formInputs/FileUploadInput';
import FuseLoading from '@fuse/core/FuseLoading';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Download, Visibility } from '@mui/icons-material';
import { Employee } from '../manage-employee/page';
import SelectInput from '@/components/formInputs/SelectInput';
import FilePreview from '@/components/FilePreview';
import WYSIWYGEditor from '@/components/WYSIWYGEditor';
import { handleFileUpload } from '../../misc';

interface Announcement {
  id?: string;
  title: string;
  message: string;
  status?: string;
  attachments?: Array<{
    id: string;
    name: string;
    url: string;
    size: number;
    type: string;
  }>;
  postedById?: string;
  postedBy?: object;
  draftedBy?: object;
  draftedById?: string;
  createdAt?: string;
  updatedAt?: string;
}

export default function AnnouncementPage() {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [addMode, setAddMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [announcementPagination, setAnnouncementPagination] = useState({
    page: 0,
    pageSize: 10,
    total: 0,
  });

  const fetchAnnouncements = async (
    draftedById?: string,
    postedById?: string,
    status?: string,
    searchText?: string,
    fromDate?: string,
    toDate?: string,
  ) => {
    console.log('User: ', user);
    setLoading(true);
    try {
      let url = `/announcement?skip=${announcementPagination.page * announcementPagination.pageSize}&limit=${announcementPagination.pageSize}`;
      if (draftedById) {
        url += `&draftedById=${draftedById}`;
      }
      if (postedById) {
        url += `&postedById=${postedById}`;
      }
      if (status) {
        url += `&status=${status}`;
      }
      if (searchText) {
        url += `&searchText=${encodeURIComponent(searchText)}`;
      }
      if (fromDate) {
        url += `&fromDate=${encodeURIComponent(fromDate)}`;
      }
      if (toDate) {
        url += `&toDate=${encodeURIComponent(toDate)}`;
      }
      const { data, total } = await apiClient.get(url);
      setAnnouncements(data);
      setAnnouncementPagination((prev) => ({
        ...prev,
        total: total || 0,
      }));
    } catch (error) {
      enqueueSnackbar('Failed to fetch announcements', { variant: 'error' });
      console.error('Failed to fetch announcements:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchEmployees = async () => {
    try {
      const { employees }: any = await apiClient.get('/employees');
      setEmployees(employees ?? []);
    } catch (error) {
      enqueueSnackbar('Failed to fetch employees', { variant: 'error' });
      console.error('Failed to fetch employees:', error);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
    fetchEmployees();
  }, []);

  const handleOpenAdd = () => {
    setEditMode(false);
    setSelectedAnnouncement(null);
    setAddMode(true);
  };

  const handleOpenEdit = (announcement: Announcement) => {
    setEditMode(true);
    setSelectedAnnouncement(announcement);
    setAddMode(true);
  };

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
    setSelectedAnnouncement(null);
    setFiles([]);
  };

  const handleFormSubmit = async (values: Announcement, actions: FormikHelpers<Announcement>) => {
    try {
      let fileUrls = [];
      if (files.length > 0) {
        fileUrls = await handleFileUpload(files);
      }

      const payload = {
        ...values,
        attachments: [
          ...(selectedAnnouncement?.attachments || []),
          ...fileUrls.map((url, index) => ({
            name: files[index].name,
            url: url,
            type: files[index].type,
          })),
        ],
      };

      if (editMode && selectedAnnouncement?.id) {
        await apiClient.patch(`/announcement/${selectedAnnouncement.id}`, payload);
        enqueueSnackbar('Announcement updated successfully', { variant: 'success' });
      } else {
        await apiClient.post('/announcement', payload);
        enqueueSnackbar('Announcement created successfully', { variant: 'success' });
      }

      fetchAnnouncements();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(error?.message || 'An error occurred while saving the announcement', {
        variant: 'error',
      });
    } finally {
      actions.setSubmitting(false);
    }
  };

  const handleViewAnnouncement = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setViewMode(true);
  };

  // const handleDelete = async (id: string) => {
  //   setLoading(true);
  //   try {
  //     await apiClient.delete(`/announcements/${id}`);
  //     enqueueSnackbar('Announcement deleted successfully', { variant: 'success' });
  //     fetchAnnouncements();
  //   } catch (error) {
  //     enqueueSnackbar('Failed to delete announcement', { variant: 'error' });
  //     console.error('Failed to delete announcement:', error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleTogglePublish = async (announcement: Announcement) => {
    if (!announcement.id) return;
    setLoading(true);
    try {
      await apiClient.patch(`/announcement/${announcement.id}`, {
        title: announcement.title,
        message: announcement.message,
        status: announcement.status === 'draft' ? 'published' : 'draft',
      });
      enqueueSnackbar(
        `Announcement ${announcement.status !== 'draft' ? 'unpublished' : 'published'} successfully`,
        { variant: 'success' },
      );
      fetchAnnouncements();
    } catch (error) {
      enqueueSnackbar('Failed to update publish status', { variant: 'error' });
      console.error('Failed to update publish status:', error);
    } finally {
      setLoading(false);
    }
  };

  const announcementColumns = useMemo<GridColDef<Announcement>[]>(
    () => [
      {
        field: 'id',
        headerName: 'S/N',
        flex: 0.5,
        renderCell: (params) => params.api.getAllRowIds().indexOf(params.id) + 1,
      },
      {
        field: 'title',
        headerName: 'Title',
        flex: 1,
      },
      {
        field: 'message',
        headerName: 'Message',
        flex: 2,
      },
      {
        field: 'status',
        headerName: 'Status',
        flex: 1,
      },
      {
        field: 'createdAt',
        headerName: 'Date Created',
        flex: 1,
        valueFormatter: (params) => (params ? new Date(params as string).toLocaleDateString() : ''),
      },
      {
        field: 'updatedAt',
        headerName: 'Date Modified',
        flex: 1,
        valueFormatter: (params) => (params ? new Date(params as string).toLocaleDateString() : ''),
      },
      {
        field: 'actions',
        headerName: 'Actions',
        flex: 1.5,
        renderCell: (params) => (
          <Box>
            <Tooltip title="View">
              <IconButton
                aria-label="View Announcement"
                onClick={() => handleViewAnnouncement(params.row)}
                sx={{ mr: 1 }}
              >
                <Visibility />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit">
              <IconButton
                aria-label="Edit"
                onClick={() => handleOpenEdit(params.row)}
                sx={{ mr: 1 }}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title={params.row.status === 'draft' ? 'Publish' : 'Unpublish'}>
              <IconButton
                aria-label="Publish"
                onClick={() => handleTogglePublish(params.row)}
                sx={{ mr: 1 }}
                color={params.row.status ? 'success' : 'default'}
              >
                {params.row.status === 'draft' ? (
                  <Send size={18} />
                ) : (
                  <Send size={18} style={{ transform: 'rotate(180deg)' }} />
                )}
              </IconButton>
            </Tooltip>
            {/* <Tooltip title="Delete">
              <IconButton
                aria-label="Delete"
                onClick={() => handleDelete(params.row.id)}
                sx={{ color: 'error.main' }}
              >
                <Delete size={18} />
              </IconButton>
            </Tooltip> */}
          </Box>
        ),
      },
    ],
    [],
  );

  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ px: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'white' }}>
                    Announcements
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/announcements" color="inherit">
                      Announcements
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': { backgroundColor: 'primary.dark' },
              }}
              onClick={handleOpenAdd}
            >
              Create Announcement
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Grid container spacing={2} p={2}>
          <Grid item xs={12}>
            {loading ? (
              <FuseLoading />
            ) : (
              <Paper>
                <DataGrid
                  rows={announcements}
                  columns={announcementColumns}
                  getRowId={(row) => row.id}
                  initialState={{
                    pagination: {
                      paginationModel: announcementPagination,
                    },
                  }}
                  onRowClick={(params) => setSelectedAnnouncement(params.row)}
                  sx={{
                    backgroundColor: 'transparent',
                    borderColor: 'primary.main',
                    '& .MuiDataGrid-columnHeaderTitle': {
                      fontWeight: 'bold',
                      color: 'white',
                    },
                    '& .MuiDataGrid-row.Mui-hovered': {
                      backgroundColor: 'transparent',
                    },
                    '& .MuiDataGrid-row:hover': {
                      backgroundColor: 'transparent',
                    },
                    '& .MuiDataGrid-cell': {
                      backgroundColor: 'transparent',
                    },
                  }}
                  showToolbar
                />
              </Paper>
            )}
          </Grid>
          {viewMode && selectedAnnouncement && (
            <Grid container item spacing={2} xs={12}>
              <Grid item xs={10}>
                <Paper
                  sx={{
                    p: 2,
                    height: '100%',
                    backgroundColor: '#f9f9f9',
                    borderRadius: 1,
                    boxShadow: 1,
                    overflowY: 'auto',
                    maxHeight: 600,
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Title:
                      </Typography>
                      <Typography variant="body1" color="primary" gutterBottom>
                        {selectedAnnouncement.title}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Posted by:{' '}
                        {`${selectedAnnouncement?.postedBy?.firstName} ${selectedAnnouncement?.postedBy?.lastName}`}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Message:
                      </Typography>
                      <div
                        className="prose prose-sm dark:prose-invert w-full max-w-full"
                        dangerouslySetInnerHTML={{ __html: selectedAnnouncement.message || '' }}
                        dir="ltr"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      {selectedAnnouncement.attachments &&
                        selectedAnnouncement.attachments.length > 0 && (
                          <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #ccc' }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Attachments:
                            </Typography>
                            <List>
                              {selectedAnnouncement.attachments.map((file) => (
                                <ListItem
                                  key={file.id}
                                  secondaryAction={
                                    <IconButton
                                      href={file.url}
                                      edge="end"
                                      aria-label="download"
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      download
                                    >
                                      <Download />
                                    </IconButton>
                                  }
                                >
                                  <FilePreview file={file} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              <Grid item xs={2} container justifyContent="center" alignItems="center">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setViewMode(false);
                    setSelectedAnnouncement(null);
                  }}
                >
                  Close
                </Button>
                <Button
                  variant="outlined"
                  style={{ marginLeft: 2 }}
                  onClick={() => handleOpenEdit(selectedAnnouncement)}
                >
                  Edit
                </Button>
              </Grid>
            </Grid>
          )}

          <Dialog open={addMode || editMode} onClose={handleDialogClose} maxWidth="md" fullWidth>
            <DialogTitle>{editMode ? 'Edit Announcement' : 'Create Announcement'}</DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  title: selectedAnnouncement?.title || '',
                  message: selectedAnnouncement?.message || '',
                  postedById: selectedAnnouncement?.postedById || '',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting, setFieldValue }: FormikProps<Announcement>) => (
                  <Form>
                    <Grid container spacing={2}>
                      <Grid item xs={12} p={2}>
                        <TextInput
                          label="Title"
                          id="title"
                          value={values.title}
                          name="title"
                          fullWidth
                          required
                        />
                      </Grid>
                      <Grid item xs={12} p={2}>
                        <WYSIWYGEditor
                          onChange={(content) => setFieldValue('message', content)}
                          value={values.message}
                        />
                        {/* <TextInput
                          label="Announcement Message"
                          id="message"
                          value={values.message}
                          name="message"
                          type="textarea"
                          fullWidth
                          multiline
                          inputProps={{ minRows: 4 }}
                          required
                        /> */}
                      </Grid>
                      <Grid item xs={12} p={2}>
                        <SelectInput
                          label="Posted By"
                          name="postedById"
                          id="postedById"
                          field="userId"
                          fieldDisplay="name"
                          options={employees.map((emp) => ({
                            ...emp,
                            name: `${emp.lastName} ${emp.firstName} ${emp.otherNames || ''}`,
                          }))}
                          fullWidth
                          required
                        />
                      </Grid>
                      <Grid item xs={12} p={2}>
                        <FileUpload
                          files={files}
                          onFileSelect={(fileList) =>
                            setFiles((prev) => [...prev, ...Array.from(fileList)])
                          }
                          onFileRemove={(index) =>
                            setFiles((prev) => prev.filter((_, i) => i !== index))
                          }
                          existingFiles={selectedAnnouncement?.attachments}
                          onExistingFileRemove={async (fileId) => {
                            try {
                              await apiClient.delete(
                                `/announcements/${selectedAnnouncement?.id}/attachments/${fileId}`,
                              );
                              fetchAnnouncements();
                            } catch (error) {
                              enqueueSnackbar('Failed to remove attachment', { variant: 'error' });
                              console.error('Failed to remove attachment:', error);
                            }
                          }}
                        />
                      </Grid>
                      <Grid
                        item
                        container
                        justifyContent="space-between"
                        alignItems="center"
                        xs={12}
                        p={2}
                      >
                        <Button variant="outlined" onClick={handleDialogClose} type="button">
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          type="submit"
                          disabled={isSubmitting}
                          color="primary"
                        >
                          {isSubmitting ? 'Submitting...' : 'Submit'}
                        </Button>
                      </Grid>
                    </Grid>
                  </Form>
                )}
              </Formik>
            </DialogContent>
          </Dialog>
        </Grid>
      }
    />
  );
}
