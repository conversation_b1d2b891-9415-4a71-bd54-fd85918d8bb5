'use client';

import { styled } from '@mui/material/styles';
import FusePageSimple from '@fuse/core/FusePageSimple/FusePageSimple';
import AdminDashboardHeader from '@/app/(control-panel)/admin/AdminDashboardHeader';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: theme.palette.background.paper,
    boxShadow: `inset 0 -1px 0 0px  ${theme.palette.divider}`,
  },
  '& .FusePageSimple-content': {
    backgroundColor: theme.palette.background.paper,
  },
}));

function AdminDashboard() {
  return (
    <Root
      header={<AdminDashboardHeader />}
      content={
        <div className="w-full pt-4 sm:pt-6">
          <div className="w-full px-6 md:px-8"></div>
        </div>
      }
    />
  );
}

export default AdminDashboard;
