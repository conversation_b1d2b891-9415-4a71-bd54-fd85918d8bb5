'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/services/api/types/auth.types';
import FuseSplashScreen from '@/@fuse/core/FuseSplashScreen';

// --- RouteGuardProps Interface ---
/**
 * Defines the props accepted by the RouteGuard component.
 */
interface RouteGuardProps {
  children: ReactNode; // The content to be rendered if access is granted.
  requiredRoles?: UserRole[]; // Optional array of roles. If provided, user must have at least one of these roles.
  redirectTo?: string; // Optional path to redirect to if authentication fails. Defaults to '/sign-in'.
}

// --- RouteGuard Component ---
/**
 * Route Guard component.
 * This component wraps routes or layouts to protect them based on user authentication status
 * and, optionally, user roles. It redirects users who do not meet the criteria.
 */
export function RouteGuard({
  children, // The actual page/component content to protect.
  requiredRoles = [], // Default to an empty array, meaning no specific roles are required by default (only authentication).
  redirectTo = '/sign-in', // Default redirection target for unauthenticated users.
}: RouteGuardProps) {
  // --- Hooks and State ---
  const { isAuthenticated, hasAnyRole, loading } = useAuth(); // Get auth state and functions from AuthContext.
  const router = useRouter(); // Next.js hook for programmatic navigation.

  // --- useEffect for Access Control Logic ---
  /**
   * This effect runs when the component mounts or when its dependencies change.
   * It's responsible for checking authentication and role requirements and redirecting
   * the user if they don't meet the criteria.
   */
  useEffect(() => {
    // 1. Skip checks if authentication state is still loading.
    // This prevents premature redirection before auth status is confirmed.
    if (loading) return;

    // 2. Check if the user is authenticated.
    // If not, redirect to the specified `redirectTo` path (e.g., '/sign-in').
    if (!isAuthenticated) {
      router.push(redirectTo);
      return; // Stop further checks as user is being redirected.
    }

    // 3. Check role-based access if `requiredRoles` are specified.
    // If roles are required and the user doesn't have any of them, redirect to '/forbidden'.
    if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
      router.push('/forbidden'); // Or any other appropriate 'access denied' page.
      return; // Stop further checks.
    }
    // If all checks pass, the user is allowed to see the content.
  }, [isAuthenticated, hasAnyRole, loading, requiredRoles, redirectTo, router]); // Dependencies for the effect.

  // --- Conditional Rendering Logic ---
  /**
   * Determines what to render:
   * - If auth is loading, or if the user is not authenticated (and about to be redirected),
   *   or if the user lacks required roles (and about to be redirected),
   *   show a loading screen (`FuseSplashScreen`) to prevent flashing of content.
   * - Otherwise (user is authenticated and has necessary roles), render the `children`.
   */
  if (loading || !isAuthenticated || (requiredRoles.length > 0 && !hasAnyRole(requiredRoles))) {
    // Display a loading screen while auth state is being verified or during the redirection process.
    // This provides a better user experience than showing a blank screen or the protected content momentarily.
    return <FuseSplashScreen />;
  }

  // If all checks passed and not loading, render the protected content.
  return <>{children}</>;
}
