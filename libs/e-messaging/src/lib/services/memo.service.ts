import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateMemoDto,
  MemoCommentCreateDto,
  MemoCommentDto,
  MemoResponseDto,
  PaginatedMemoResponseDto,
  QueryMemoDto,
  UpdateMemoDto,
} from '../dtos';
import { MemoCommentEntity, MemoEntity, MemoHistoryEntity } from '../entity';
import { plainToInstance } from 'class-transformer';
import { MemoRepository } from '../repositories';
import { MemoActionEnum, MemoStatusEnums } from '../enums';
import { EmployeeRepository } from '@igov/employee';
import { DataSource } from 'typeorm';

@Injectable()
export class MemoService {
  private readonly logger = new Logger(MemoEntity.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly memoRepository: MemoRepository,
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  async create(createMemoDto: CreateMemoDto, createdBy: string): Promise<MemoResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const { title, body, attachments, fromEmployeeId, toEmployees, throughEmployees, reference } =
        createMemoDto;
      const recipientEmployeesPromise = this.employeeRepository.findByIdsList(toEmployees);

      const associatedEmployeesPromise = throughEmployees?.length 
        ? this.employeeRepository.findByIdsList(throughEmployees)
        : Promise.resolve([]);

      const [recipientEmployees, associatedEmployees] = await Promise.all([
        recipientEmployeesPromise,
        associatedEmployeesPromise,
      ]);

      await queryRunner.connect();

      await queryRunner.startTransaction();

      // Set initial status based on throughEmployees business rule
      // If throughEmployees provided: Start with Draft (needs approval first)
      // If no throughEmployees: Start with Pending (skip approval, go directly to signature)
      const initialStatus = associatedEmployees.length > 0 
        ? MemoStatusEnums.Draft 
        : MemoStatusEnums.Pending;

      const memo = await queryRunner.manager.save(MemoEntity, {
        createdBy: createdBy,
        attachments: attachments,
        fromEmployeeId: fromEmployeeId,
        title,
        body,
        status: initialStatus,
        throughEmployees: associatedEmployees,
        toEmployees: recipientEmployees,
        reference: reference,
      });

      await queryRunner.manager.save(MemoHistoryEntity, {
        performedBy: createdBy,
        memoId: memo.id,
        action: MemoActionEnum.Created,
        snapshot: {
          before: {
            title: memo.title,
            body: memo.body,
          },
        },
      });

      await queryRunner.commitTransaction();

      return plainToInstance(MemoResponseDto, memo);
    } catch (e) {
      await queryRunner.rollbackTransaction();
      this.handleError(e);
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async getOne(params: { id: string; employeeId?: string }): Promise<MemoResponseDto> {
    const { id, employeeId } = params;
    const memo = await this.memoRepository.findOne({
      id,
      ...(employeeId && {
        employeeId,
      }),
      relations: ['toEmployees', 'throughEmployees', 'comments', 'history'],
    });

    if (!memo) {
      throw new NotFoundException(`Memo with ID ${id} not found`);
    }

    return plainToInstance(MemoResponseDto, memo);
  }

  async getAll(query: Partial<QueryMemoDto>): Promise<PaginatedMemoResponseDto> {
    const { data, total } = await this.memoRepository.findAll(query);
    const memos = plainToInstance(MemoResponseDto, data);
    return {
      total,
      data: memos,
    };
  }

  async updateRecord(params: {
    id: string;
    data: UpdateMemoDto;
    employeeId: string;
  }): Promise<MemoResponseDto> {
    const { id, data, employeeId } = params;
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const existingMemo = await this.ensureMemoExists({ id, employeeId });
      if (
        existingMemo.status === MemoStatusEnums.Approved ||
        existingMemo.status === MemoStatusEnums.Rejected
      ) {
        this.logger.warn(`Cannot edit memo with ID ${id} as it is already ${existingMemo.status}`);
        throw new ConflictException(
          `Cannot edit memo with ID ${id} as it is already ${existingMemo.status}`,
        );
      }

      let action = MemoActionEnum.Edited;

      if (data?.status == MemoStatusEnums.Approved) {
        action = MemoActionEnum.Approved;
      } else if (data?.status == MemoStatusEnums.Rejected) {
        action = MemoActionEnum.Rejected;
      }

      await queryRunner.connect();

      await queryRunner.startTransaction();

      const memo = await queryRunner.manager.save(MemoEntity, { id, ...data });

      await queryRunner.manager.save(MemoHistoryEntity, {
        performedBy: employeeId,
        memoId: id,
        action,
        snapshot: {
          before: {
            title: existingMemo.title,
            body: existingMemo.body,
          },
          after: {
            title: memo.title,
            body: memo.body,
          },
        },
      });

      await queryRunner.commitTransaction();
      return plainToInstance(MemoResponseDto, memo);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async ensureMemoExists(data: { id: string; employeeId: string }): Promise<MemoEntity> {
    const { id, employeeId } = data;
    const memo = await this.memoRepository.findOneMemoByAuthorizedEmployee(data);
    if (!memo) {
      const message = `Memo with ID ${id} not found or Employee with id ${employeeId} not Authrized to act on this Memo`;
      this.logger.warn(message);
      throw new ConflictException(message);
    }
    return memo;
  }

  private handleError(error: unknown): void {
    this.logger.error('An error occurred:', error);
    if (error instanceof ConflictException || error instanceof NotFoundException) {
      throw error;
    }
    throw new InternalServerErrorException('Something went wrong...');
  }

  async addComment(data: {
    dto: MemoCommentCreateDto;
    memoId: string;
    commenterId: string;
  }): Promise<MemoCommentDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const { dto, memoId, commenterId } = data;
      const existingMemo = await this.ensureMemoExists({ id: memoId, employeeId: commenterId });
      if (
        existingMemo.status === MemoStatusEnums.Approved ||
        existingMemo.status === MemoStatusEnums.Rejected
      ) {
        this.logger.warn(
          `Cannot edit memo with ID ${memoId} as it is already ${existingMemo.status}`,
        );
        throw new ConflictException(
          `Cannot edit memo with ID ${memoId} as it is already ${existingMemo.status}`,
        );
      }
      await queryRunner.connect();

      await queryRunner.startTransaction();

      const newComment = await queryRunner.manager.save(MemoCommentEntity, {
        commentedBy: commenterId,
        memoId,
        ...dto,
      });
      await queryRunner.manager.save(MemoHistoryEntity, {
        performedBy: commenterId,
        memoId: memoId,
        action: MemoActionEnum.Commented,
        snapshot: {
          comment: dto?.comment,
        },
      });
      await queryRunner.commitTransaction();

      return plainToInstance(MemoCommentDto, newComment);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
