'use client';

import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { employeeApiService } from '@/services/api/employeeService';
import { useSnackbar } from 'notistack';

interface UseETokenReturn {
  /** Current eToken value */
  eToken: string | undefined;
  /** Whether the eToken is being set or validated */
  isLoading: boolean;
  /** Error message if any operation fails */
  error: string | null;
  /** Function to set/update the eToken */
  setToken: (token: string, passwordConfirmation: string) => Promise<boolean>;
  /** Function to validate an eToken */
  validateToken: (token: string, passwordConfirmation: string) => Promise<boolean>;
  /** Function to clear any error */
  clearError: () => void;
}

/**
 * Custom hook to manage eToken operations
 */
export function useEToken(): UseETokenReturn {
  const { employeeDetails, refresh: refreshAuth } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { enqueueSnackbar } = useSnackbar();

  const eToken = employeeDetails?.eToken;

  const handleApiError = useCallback(
    (error: any, defaultMessage: string) => {
      console.error(`${defaultMessage}:`, error);
      const errorMessage = error.response?.data?.message || error.message || defaultMessage;
      setError(errorMessage);
      enqueueSnackbar(errorMessage, {
        variant: 'error',
        autoHideDuration: 5000,
        anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      });
      return errorMessage;
    },
    [enqueueSnackbar],
  );

  const setToken = useCallback(
    async (token: string, passwordConfirmation: string) => {
      if (!employeeDetails?.userId) {
        setError('User not authenticated');
        return false;
      }

      setIsLoading(true);
      setError(null);

      try {
        const tokenData = {
          token,
          passwordConfirmation,
        };

        await employeeApiService.setToken(tokenData);

        // Refresh auth context to get updated employee details
        await refreshAuth();

        enqueueSnackbar('eToken updated successfully', {
          variant: 'success',
          autoHideDuration: 3000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        });

        return true;
      } catch (error: any) {
        handleApiError(error, 'Failed to set eToken');
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [employeeDetails?.userId, refreshAuth, handleApiError],
  );

  const validateToken = useCallback(
    async (token: string, passwordConfirmation: string) => {
      if (!employeeDetails?.userId) {
        setError('User not authenticated');
        return false;
      }

      setIsLoading(true);
      setError(null);

      try {
        const tokenData = {
          userId: employeeDetails.userId,
          token,
          passwordConfirmation,
        };

        const isValid = await employeeApiService.validateToken(tokenData);

        if (isValid) {
          enqueueSnackbar('eToken is valid', {
            variant: 'success',
            autoHideDuration: 3000,
            anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
          });
        }

        return isValid;
      } catch (error: any) {
        handleApiError(error, 'Failed to validate eToken');
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [employeeDetails?.userId, handleApiError],
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    eToken,
    isLoading,
    error,
    setToken,
    validateToken,
    clearError,
  };
}
