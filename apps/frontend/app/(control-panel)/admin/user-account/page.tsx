'use client';

import { useEffect, useMemo, useState } from 'react';
import {
  Button,
  Typography,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  CircularProgress,
  Paper,
  ListItem,
  Link,
  Breadcrumbs,
  ListItemText,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import FusePageCarded from '@fuse/core/FusePageCarded/FusePageCarded';
import { apiClient } from '../../../../services/api/apiClient';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';

import FuseLoading from '@fuse/core/FuseLoading';
import { enqueueSnackbar } from 'notistack';
import SelectInput from '../../../../components/formInputs/SelectInput';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { firstLetterUpperCase, generatePassword } from '../../misc';

export interface UserAccount {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  role?: string;
  status?: string;
  password?: string;
}

export default function UserAccountsPage() {
  const [userAccounts, setUserAccounts] = useState([]);
  const [fEmployees, setEmployees] = useState([]);
  const [editMode, setEditMode] = useState(false);
  const [addMode, setAddMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<UserAccount | null>();
  const [usersPagination, setUsersPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });

  const handleOpenAdd = () => {
    setEditMode(false);
    setAddMode(true);
  };

  const handleOpenEdit = (dept) => {
    setEditMode(true);
    setAddMode(false);
    setCurrentAccount(dept);
  };

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const { employees }: [] = await apiClient.get('/employees');
      const formattedEmployees = employees.map((emp) => ({
        ...emp,
        fullname: `${emp.lastName} ${emp.firstName} ${emp.otherNames || ''}`.trim(),
      }));
      setEmployees(formattedEmployees);
    } catch (_err) {
      console.error('Failed to fetch employees:', _err);
    } finally {
      setLoading(false);
    }
  };
  const fetchUserAccounts = async () => {
    setLoading(true);
    try {
      const { data, total } = await apiClient.get(
        `/users?skip=${(usersPagination.page - 1) * usersPagination.pageSize}&limit=${usersPagination.pageSize}`,
      );
      setUserAccounts(data);
      setUsersPagination((prev) => ({
        ...prev,
        total: total,
      }));
    } catch (_err) {
      console.error('Failed to fetch user accounts:', _err);
    } finally {
      setLoading(false);
    }
  };

  const handleResetClick = async (id: string) => {
    const confirmDelete = window.confirm(
      'Are you sure you want to reset the password for this account? This action cannot be undone.',
    );
    if (!confirmDelete) {
      return;
    }
    setLoading(true);
    await apiClient.delete(`/organization-structure/departments/${id}`);
    enqueueSnackbar('Reset Successfully', {
      variant: 'success',
    });
    setLoading(false);
    fetchUserAccounts();
  };

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
  };

  const userAccountsColumns = useMemo<GridColDef<UserAccount>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Full Name',
        flex: 2,
        renderCell: (params) => {
          return `${params.row?.lastName || 'N/A'} ${params.row?.firstName || 'N/A'}`.trim();
        },
      },
      {
        field: 'email',
        headerName: 'Email',
        flex: 2,
      },
      {
        field: 'role',
        headerName: 'Account Type',
        flex: 1,
        valueGetter: (params) => {
          return firstLetterUpperCase(params || '');
        },
      },
      {
        field: 'createdAt',
        headerName: 'Created At',
        flex: 1,
        valueGetter: (params) => {
          return new Date(params || '');
        },
      },
      {
        field: 'updatedAt',
        headerName: 'Updated At',
        flex: 1,
        valueGetter: (params) => {
          return new Date(params || '');
        },
      },
      // {
      //   field: 'actions',
      //   headerName: 'Actions',
      //   flex: 2,
      //   type: 'actions',
      //   width: 100,
      //   getActions: (params) => [
      //     <Tooltip title="View account" key="view">
      //       <IconButton
      //         aria-label="View Account"
      //         onClick={() => {
      //           setCurrentAccount(params.row);
      //           setViewMode(true);
      //         }}
      //         sx={{ color: 'error.main' }}
      //       >
      //         <Visibility />
      //       </IconButton>
      //     </Tooltip>,
      //     <Tooltip title="Reset account password" key="reset-password">
      //       <IconButton
      //         aria-label="Reset Account Password"
      //         onClick={() => {
      //           handleResetClick(params.row.id);
      //         }}
      //         sx={{ color: 'error.main' }}
      //       >
      //         <ResetTv />
      //       </IconButton>
      //     </Tooltip>,
      //     <Tooltip title="Edit" key="edit">
      //       <IconButton aria-label="Edit" onClick={() => handleOpenEdit(params.row)} sx={{ mr: 1 }}>
      //         <Edit size={18} />
      //       </IconButton>
      //     </Tooltip>,
      //   ],
      // },
    ],
    [],
  );
  const availableEmployees = useMemo(() => {
    return fEmployees
      .filter((employee) => !userAccounts.some((account) => account.employeeId === employee.id))
      .map((employee) => ({
        value: employee.id,
        label: `${employee.lastName} ${employee.firstName} ${employee.otherNames || ''}`.trim(),
      }));
  }, [fEmployees, userAccounts]);
  const handleFormSubmit = async (values: UserAccount, actions: FormikHelpers<UserAccount>) => {
    try {
      if (editMode) {
        await apiClient.patch(`/organization-structure/user-account/${currentAccount.id}`, values);
      } else {
        const generatedPassword = generatePassword(12);
        await apiClient.post('/organization-structure/user-account', {
          ...values,
          password: generatedPassword,
        });
      }
      enqueueSnackbar('User account data saved successfully', {
        variant: 'success',
      });
      fetchUserAccounts();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };
  useEffect(() => {
    fetchUserAccounts();
    fetchEmployees();
  }, []);
  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography
                    variant="h5"
                    sx={{
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  >
                    User Accounts
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/user-account" color="inherit">
                      User accounts
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
              style={{ color: 'white' }}
              onClick={handleOpenAdd}
            >
              Add Account
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Box p={2}>
          {loading ? (
            <FuseLoading />
          ) : (
            <Grid container spacing={2} mb={2}>
              <Grid item xs={12}>
                <Paper>
                  <DataGrid
                    rows={userAccounts}
                    columns={userAccountsColumns}
                    showToolbar
                    sx={(theme) => ({
                      backgroundColor: 'transparent',
                      borderColor: theme.palette.primary.main,
                      '& .MuiDataGrid-columnHeaders': {
                        backgroundColor: 'transparent',
                        color: theme.palette.common.white,
                      },
                      '& .MuiDataGrid-row.Mui-hovered': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row': {
                        border: '1px solid lightgray',
                      },
                      '& .MuiDataGrid-overlay': {
                        backgroundColor: 'transparent',
                      },
                      // Take out the hover colour
                      '& .MuiDataGrid-row:hover': {
                        backgroundColor: 'transparent',
                      },
                    })}
                    getRowId={(row) => row.id}
                  />
                </Paper>
              </Grid>
            </Grid>
          )}

          <Dialog open={addMode || editMode} onClose={handleDialogClose} maxWidth="sm">
            <DialogTitle>{editMode ? 'Edit User Account' : 'Add User Account'}</DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  email: currentAccount?.email || '',
                  firstName: currentAccount?.firstName || '',
                  lastName: currentAccount?.lastName || '',
                  password: currentAccount?.status || '',
                  role: currentAccount?.role || '',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting, setFieldValue }: FormikProps<UserAccount>) => {
                  return (
                    <Form>
                      <Grid container spacing={2}>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            id="employeeId"
                            options={fEmployees}
                            field="userId"
                            fieldDisplay="label"
                            label="Employee"
                            customChange={(v) => {
                              const emp = fEmployees.find((ep) => ep.Id === v);
                              setFieldValue('firstName', emp?.firstName);
                              setFieldValue('lastName', emp?.lastName);
                              setFieldValue('email', emp?.email);
                            }}
                            name="employeeId"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            label="User Type"
                            id="userType"
                            field="value"
                            fieldDisplay="label"
                            options={[
                              { value: 'Employee', label: 'Employee' },
                              { value: 'Moderator', label: 'Moderator' },
                            ]}
                            value={values.role}
                            // floatingLabel
                            name="userType"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            label="Status"
                            id="status"
                            field="value"
                            fieldDisplay="label"
                            options={[
                              { value: 'active', label: 'Active' },
                              { value: 'inactive', label: 'Inactive' },
                            ]}
                            value={values.status}
                            // floatingLabel
                            name="status"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid
                          item
                          container
                          justifyContent={'space-between'}
                          alignItems={'center'}
                          xs={12}
                        >
                          <LoadingButton
                            variant="outlined"
                            onClick={handleDialogClose}
                            type="button"
                          >
                            Cancel
                          </LoadingButton>
                          <Button variant="contained" type="submit" color="primary">
                            {isSubmitting ? <CircularProgress /> : 'Submit'}
                          </Button>
                        </Grid>
                      </Grid>
                    </Form>
                  );
                }}
              </Formik>
            </DialogContent>
          </Dialog>
        </Box>
      }
    />
  );
}
