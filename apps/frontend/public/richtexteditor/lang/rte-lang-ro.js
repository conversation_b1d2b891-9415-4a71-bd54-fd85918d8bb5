//Romanian , Română
RTE_DefaultConfig.text_language = "limba";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "anula";	//"Cancel"
RTE_DefaultConfig.text_normal = "normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Titlul 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Titlu 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Titlu 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Titlu 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Titlu 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Titlu 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Titlu 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "închide";	//"Close"
RTE_DefaultConfig.text_bold = "îndrăzneţ";	//"Bold"
RTE_DefaultConfig.text_italic = "cursiv";	//"Italic"
RTE_DefaultConfig.text_underline = "subliniere";	//"Underline"
RTE_DefaultConfig.text_strike = "Linie de lovire";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "exponent";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript (în engleză)";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Majuscule";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Litere mici";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Eliminare format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Inserare legătură";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Deschidere legătură";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Editare link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Eliminare legătură";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Înălțime linie";	//"Line Height"
RTE_DefaultConfig.text_indent = "liniuță";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent (Outdent)";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Bloc are ofertă";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Listă ordonată";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Listă neordonată";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Inserare regulă orizontală";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Inserare dată";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Inserare tabel";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Inserare imagine";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Inserare video";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Inserare cod";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Creare PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Inserare emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Caractere speciale";	//"Special characters"
RTE_DefaultConfig.text_characters = "caractere";	//"Characters"
RTE_DefaultConfig.text_fontname = "font";	//"Font"
RTE_DefaultConfig.text_fontsize = "dimensiunea";	//"Size"
RTE_DefaultConfig.text_forecolor = "Culoare text";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Culoare spate";	//"Back Color"
RTE_DefaultConfig.text_justify = "justifica";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justificare stânga";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justificare dreapta";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centru de justificare";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justificare completă";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Justificare fără";	//"Justify None"
RTE_DefaultConfig.text_delete = "şterge";	//"Delete"
RTE_DefaultConfig.text_save = "Salvare fișier";	//"Save file"
RTE_DefaultConfig.text_selectall = "Selectați Toate";	//"Select All"
RTE_DefaultConfig.text_code = "Cod HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "previzualizare";	//"Preview"
RTE_DefaultConfig.text_print = "imprima";	//"Print"
RTE_DefaultConfig.text_undo = "anulare";	//"Undo"
RTE_DefaultConfig.text_redo = "refaceţi";	//"Redo"
RTE_DefaultConfig.text_more = "Mai multe...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Doc nou";	//"New Doc"
RTE_DefaultConfig.text_help = "ajuta";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Potrivire la fereastră";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Ieșire ecran complet";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor de imagini";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Stiluri imagine";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Stiluri în linie";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Stiluri paragraf";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Stiluri legătură";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "stiluri";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Clase Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "După Url";	//"By Url"
RTE_DefaultConfig.text_upload = "încărcaţi";	//"Upload"
RTE_DefaultConfig.text_size = "dimensiunea";	//"Size"
RTE_DefaultConfig.text_text = "text";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Deschidere în fila nouă";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "insera";	//"Insert"
RTE_DefaultConfig.text_update = "actualizare";	//"Update"
RTE_DefaultConfig.text_find = "Găsire&înlocuire";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "găsi";	//"Find"
RTE_DefaultConfig.text_replacewith = "înlocui";	//"Replace"
RTE_DefaultConfig.text_findnext = "următorul";	//"Next"
RTE_DefaultConfig.text_replaceonce = "înlocui";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Înlocuire toate";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Caz meci";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Potrivire cuvânt";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Mutare în jos";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Mutare în sus";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Dimensiune automată";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "Lățime 100%";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "Lățime 75%";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "Lățime 50%";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "Lățime 25%";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Setare dimensiune";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt text";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "justifica";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Legendă imagine";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Îmbinare celule";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Scindare a celulelor verticale";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Scindare a celulelor orizontale";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Culoare text celulă";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Culoare spate celulă";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Inserare rând deasupra";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Inserare rând dedesubt";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Inserare coloană la stânga";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Inserare coloană la dreapta";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Ștergere coloană";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Ștergere rând";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Ștergere tabel";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Dimensiune automată";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Antet tabel";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Adăugarea unui paragraf nou";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "lipiţi";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "lipiţi";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Lipire text";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Lipire ca Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Lipire cuvânt";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Utilizați CTRL+V pentru a lipi conținutul în caseta de mai jos. \r\nConținutul va fi curățat automat.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "alineatele (";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "alineatele (";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Mutare în sus";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Mutare în jos";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplicat";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "şterge";	//"Delete"
RTE_DefaultConfig.text_pmore = "Mai multe..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Mai multe..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Comutare bordură";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "tăiat";	//"Cut"
RTE_DefaultConfig.text_copy = "copie";	//"Copy"
RTE_DefaultConfig.text_copied = "copiat";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Inserare galerie";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Inserare document";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Inserare șablon";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "previzualizare";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tabletă";	//"Tablet"
RTE_DefaultConfig.text_table = "tabelul";	//"Table"
RTE_DefaultConfig.text_tablecell = "Celulă tabel";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Rând tabel";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Coloană tabel";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automată";	//"Automatic"
RTE_DefaultConfig.text_colormore = "mai multe";	//"More"
RTE_DefaultConfig.text_colorpicker = "Alegere culoare";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Paletă Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Culori denumite";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "bază";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "plus";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Glisare și fixare";	//"Drag and drop"
RTE_DefaultConfig.text_or = "sau";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Faceți clic pentru a încărca";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Legendă imagine implicită";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "căutaţi";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Textul de adăugat a atins limita de caractere pentru acest câmp.";	//"The text to be added has reached the character limit for this field."
