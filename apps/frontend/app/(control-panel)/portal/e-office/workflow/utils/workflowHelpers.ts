/**
 * Utility functions for workflow components
 */

/**
 * Strip HTML tags from text and return plain text
 */
export function stripHtml(html: string): string {
  if (!html) return '';
  
  // Create a temporary div element to parse HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  
  // Get text content and clean up whitespace
  return tempDiv.textContent?.trim() || tempDiv.innerText?.trim() || '';
}

/**
 * Truncate text to specified length and add ellipsis
 */
export function truncateText(text: string, maxLength: number = 100): string {
  if (!text) return '';
  
  const plainText = stripHtml(text);
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  // Find the last space before the max length to avoid cutting words
  const truncated = plainText.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > maxLength * 0.8) {
    // If there's a space near the end, cut there
    return truncated.substring(0, lastSpace) + '...';
  } else {
    // Otherwise, cut at max length
    return truncated + '...';
  }
}

/**
 * Format date safely with fallback for invalid dates
 */
export function formatWorkflowDate(date: string | Date | null | undefined, fallback: string = 'Not set'): string {
  if (!date) return fallback;
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return fallback;
    }
    
    return dateObj.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.warn('Invalid date format:', date);
    return fallback;
  }
}

/**
 * Get display text for workflow description in tables
 */
export function getWorkflowDisplayDescription(description: string | undefined, maxLength: number = 120): string {
  if (!description) return 'No description';
  
  return truncateText(description, maxLength);
}