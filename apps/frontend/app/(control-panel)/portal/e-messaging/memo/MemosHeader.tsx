import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import PageBreadcrumb from '@/components/PageBreadcrumb';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { motion } from 'motion/react';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import NavLinkAdapter from '@fuse/core/NavLinkAdapter';
import { useRouter } from 'next/navigation';

function MemosHeader() {
  const router = useRouter();
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));
  const handleCreateNew = () => {
    router.push('/portal/e-messaging/memo/new');
  };

  return (
    <div className="flex grow-0 flex-1 w-full items-center justify-between space-y-2 sm:space-y-0 py-6 sm:py-8 md:px-16 px-4">
      <motion.span initial={{ x: -20 }} animate={{ x: 0, transition: { delay: 0.2 } }}>
        <div>
          <PageBreadcrumb className="mb-2" />
          <Typography className="text-4xl font-extrabold leading-none tracking-tight text-gray-900">
            Memos
          </Typography>
          <Typography
            className="font-medium tracking-tight"
            color="text.secondary"
          >
            View and manage all memos
          </Typography>
        </div>
      </motion.span>

      <div className="flex flex-1 items-center justify-end space-x-2">
        <motion.div
          className="flex grow-0"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0, transition: { delay: 0.2 } }}
        >
          <Button
            className="whitespace-nowrap mx-1"
            variant="contained"
            color="secondary"
            startIcon={<FuseSvgIcon size={20}>heroicons-outline:plus-circle</FuseSvgIcon>}
            onClick={handleCreateNew}
          >
            <span className="mx-1 sm:mx-2">Create Memo</span>
          </Button>
        </motion.div>
      </div>
    </div>
  );
}

export default MemosHeader;
