'use client';

import React, { useState } from 'react';
import {
  <PERSON>ton,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  Breadcrumbs,
  ListItem,
  ListItemText,
  Link,
  Typography,
} from '@mui/material';
import FusePageCarded from '@fuse/core/FusePageCarded/FusePageCarded';
import { Edit, Trash } from 'lucide-react';
import { apiClient } from '@/services/api/apiClient';
import { enqueueSnackbar } from 'notistack';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';
import TextInput from '../../../../../components/formInputs/TextInput';
import FuseLoading from '@fuse/core/FuseLoading';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import SelectInput from '@/components/formInputs/SelectInput';

interface PaymentDefinition {
  id?: string;
  payCode: string;
  payName: string;
  payMode: string;
  payType: string;
  value: string;
  percentageValue: number;
}
export default function PaymentDefinitionsPage() {
  const [paymentDefinitions, setPaymentDefinitions] = useState([]);
  const [addMode, setAddMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedPaymentDefinition, setSelectedPaymentDefinition] =
    useState<PaymentDefinition | null>(null);

  const handleOpenAdd = () => {
    setEditMode(false);
    setAddMode(true);
  };

  const handleOpenEdit = (pos) => {
    setEditMode(true);
    setSelectedPaymentDefinition(pos);
    setAddMode(false);
  };

  const fetchPaymentDefinitions = async () => {
    setLoading(true);
    try {
      const responseData: [] = await apiClient.get('/hr-settings/payment-definitions');
      setPaymentDefinitions(responseData);
    } catch (_err) {
      console.error('Failed to fetch requested data:', _err);
    } finally {
      setLoading(false);
    }
  };
  const handleDelete = async (id) => {
    setLoading(true);
    try {
      await apiClient.delete(`/hr-settings/payment-definitions/${id}`);
      enqueueSnackbar('Deleted Successfully', {
        variant: 'success',
      });
    } catch (error) {
      enqueueSnackbar('Attempt to delete tax-rate failed', {
        variant: 'error',
      }); 
    } finally {
      fetchPaymentDefinitions();
    }
  };

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
  };

  const handleFormSubmit = async (
    values: PaymentDefinition,
    actions: FormikHelpers<PaymentDefinition>,
  ) => {
    try {
      values.percentageValue = Number(values.percentageValue);
      if (editMode) {
        await apiClient.patch(
          `/hr-settings/payment-definitions/${selectedPaymentDefinition.id}`,
          values,
        );
      } else {
        await apiClient.post('/hr-settings/payment-definitions', values);
      }
      enqueueSnackbar('Payment definition saved successfully', {
        variant: 'success',
      });
      fetchPaymentDefinitions();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };

  const paymentDefinitionColumns = React.useMemo<GridColDef<PaymentDefinition>[]>(
    () => [
      { field: 'payName', headerName: 'Name', flex: 1 },
      { field: 'payMode', headerName: 'Mode', flex: 1 },
      { field: 'payCode', headerName: 'Code', flex: 1 },
      { field: 'payType', headerName: 'Type', flex: 1 },
      { field: 'percentageValue', headerName: 'Percentage Value (%)', flex: 1 },
      { field: 'value', headerName: 'Value', flex: 1 },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        width: 100,
        renderCell: (params) => (
          <Box>
            <Tooltip title="Edit">
              <IconButton
                aria-label="Edit"
                onClick={() => handleOpenEdit(params.row)}
                sx={{ mr: 1 }}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                aria-label="Delete"
                onClick={() => handleDelete(params.row.id)}
                sx={{ color: 'error.main' }}
              >
                <Trash size={18} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [],
  );
  React.useEffect(() => {
    fetchPaymentDefinitions();
  }, []);

  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 'bold',
                      color: 'white',
                    }}
                  >
                    Payments Setup
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/hr-settings/payment-definitions" color="inherit">
                      Payments Setup
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
              style={{ color: 'white' }}
              onClick={handleOpenAdd}
            >
              Add Payment Definition
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Box p={2}>
          {loading ? (
            <FuseLoading />
          ) : (
            <DataGrid
              rows={paymentDefinitions}
              columns={paymentDefinitionColumns}
              getRowId={(row) => row.id}
              showToolbar
              sx={(theme) => ({
                backgroundColor: 'transparent',
                borderColor: theme.palette.primary.main,
                '& .MuiDataGrid-columnHeaderRow': {
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-columnHeader': {
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-row': {
                  border: '1px solid lightgray',
                },
                '& .MuiDataGrid-columnHeaderTitle': {
                  fontWeight: 'bold',
                  color: theme.palette.common.white,
                },
                '& .MuiDataGrid-overlay': {
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-row:first-of-type': {
                  // Changed from first-child
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-row.Mui-hovered': {
                  backgroundColor: 'transparent',
                },
                // Take out the hover colour
                '& .MuiDataGrid-row:hover': {
                  backgroundColor: 'transparent',
                },
                '& .MuiDataGrid-cell': {
                  backgroundColor: 'transparent',
                },
              })}
            />
            // <Table>
            //   <TableHead>
            //     <TableRow>
            //       <TableCell>S/N</TableCell>
            //       <TableCell>Name</TableCell>
            //       <TableCell>Mode</TableCell>
            //       <TableCell>Code</TableCell>
            //       <TableCell>Type</TableCell>
            //       <TableCell>Percentage Value</TableCell>
            //       <TableCell>Value</TableCell>
            //       <TableCell align="right">Actions</TableCell>
            //     </TableRow>
            //   </TableHead>
            //   <TableBody>
            //     {paymentDefinitions.map((pDef, index) => (
            //       <TableRow key={pDef.id} hover>
            //         <TableCell>{index + 1}</TableCell>
            //         <TableCell>{pDef.payName}</TableCell>
            //         <TableCell>{pDef.payMode}</TableCell>
            //         <TableCell>{pDef.payCode}</TableCell>
            //         <TableCell>{pDef.payType}</TableCell>
            //         <TableCell>{pDef.percentageValue}</TableCell>
            //         <TableCell>{pDef.value}</TableCell>
            //         <TableCell align="right">
            //           <Tooltip title="Edit">
            //             <IconButton
            //               aria-label="Edit"
            //               onClick={() => handleOpenEdit(pDef)}
            //               sx={{ mr: 1 }}
            //             >
            //               <Edit size={18} />
            //             </IconButton>
            //           </Tooltip>
            //           <Tooltip title="Delete">
            //             <IconButton
            //               aria-label="Delete"
            //               onClick={() => handleDelete(pDef.id)}
            //               sx={{ color: 'error.main' }}
            //             >
            //               <Trash size={18} />
            //             </IconButton>
            //           </Tooltip>
            //         </TableCell>
            //       </TableRow>
            //     ))}
            //   </TableBody>
            // </Table>
          )}

          <Dialog open={addMode || editMode} onClose={handleDialogClose} maxWidth="sm" fullWidth>
            <DialogTitle>
              {editMode ? 'Edit Payment definition' : 'Add Payment definition'}
            </DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  payCode: selectedPaymentDefinition?.payCode || '',
                  percentageValue: selectedPaymentDefinition?.percentageValue || 0,
                  payName: selectedPaymentDefinition?.payName || '',
                  payMode: selectedPaymentDefinition?.payMode || '',
                  payType: selectedPaymentDefinition?.payType || '',
                  value: selectedPaymentDefinition?.value || '',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting }: FormikProps<PaymentDefinition>) => {
                  return (
                    <Form>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6} p={2}>
                          <TextInput
                            label="Name"
                            id="payName"
                            value={values.payName}
                            floatingLabel
                            name="payName"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} md={6} p={2}>
                          <SelectInput
                            label="Mode"
                            id="payMode"
                            value={values.payMode}
                            field={'value'}
                            fieldDisplay={'label'}
                            options={[
                              { value: 'Standard', label: 'Standard' },
                              { value: 'Variation', label: 'Variation' },
                            ]}
                            floatingLabel
                            name="payMode"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} md={6} p={2}>
                          <SelectInput
                            label="Type"
                            id="payType"
                            value={values.payType}
                            field={'value'}
                            fieldDisplay={'label'}
                            options={[
                              { value: 'Income', label: 'Income' },
                              { value: 'Deduction', label: 'Deduction' },
                            ]}
                            floatingLabel
                            name="payType"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} md={6} p={2}>
                          <SelectInput
                            label="Value"
                            id="value"
                            value={values.value}
                            field={'value'}
                            fieldDisplay={'label'}
                            options={[
                              { value: 'Flat', label: 'Flat' },
                              { value: 'Computational', label: 'Computational' },
                            ]}
                            floatingLabel
                            name="value"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} md={6} p={2}>
                          <TextInput
                            label="Code"
                            id="payCode"
                            value={values.payCode}
                            floatingLabel
                            name="payCode"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} md={6} p={2}>
                          <TextInput
                            label="Percentage"
                            id="percentageValue"
                            value={values.percentageValue}
                            type="number"
                            floatingLabel
                            name="percentageValue"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid
                          item
                          container
                          justifyContent={'space-between'}
                          alignItems={'center'}
                          xs={12}
                        >
                          <Button variant="outlined" onClick={handleDialogClose} type="button">
                            Cancel
                          </Button>
                          <Button
                            variant="contained"
                            type="submit"
                            disabled={isSubmitting}
                            color="primary"
                          >
                            {isSubmitting ? 'Submitting..' : 'Submit'}
                          </Button>
                        </Grid>
                      </Grid>
                    </Form>
                  );
                }}
              </Formik>
            </DialogContent>
          </Dialog>
        </Box>
      }
    />
  );
}
