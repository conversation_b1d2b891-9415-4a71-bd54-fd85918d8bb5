//Chinese Simplified , 简体中文
RTE_DefaultConfig.text_language = "语言";	//"Language"
RTE_DefaultConfig.text_ok = "确定";	//"OK"
RTE_DefaultConfig.text_cancel = "取消";	//"Cancel"
RTE_DefaultConfig.text_normal = "正常";	//"Normal"
RTE_DefaultConfig.text_h1 = "标题 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "标题 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "标题 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "标题 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "标题 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "标题 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "标题 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "关闭";	//"Close"
RTE_DefaultConfig.text_bold = "粗体";	//"Bold"
RTE_DefaultConfig.text_italic = "斜体";	//"Italic"
RTE_DefaultConfig.text_underline = "下划线";	//"Underline"
RTE_DefaultConfig.text_strike = "删除线";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "上标";	//"Superscript"
RTE_DefaultConfig.text_subscript = "副犯罪";	//"Subcript"
RTE_DefaultConfig.text_ucase = "大写";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "小写";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "删除格式";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "插入链接";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "打开链接";	//"Open Link"
RTE_DefaultConfig.text_editlink = "编辑链接";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "删除链接";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "行高";	//"Line Height"
RTE_DefaultConfig.text_indent = "缩进";	//"Indent"
RTE_DefaultConfig.text_outdent = "取消缩进";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "引用";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "顺序列表";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "无序列表";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "插入水平规则";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "插入日期";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "插入表格";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "插入图像";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "插入视频";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "插入代码";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "创建 PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "插入Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "特殊字符";	//"Special characters"
RTE_DefaultConfig.text_characters = "字符";	//"Characters"
RTE_DefaultConfig.text_fontname = "字体";	//"Font"
RTE_DefaultConfig.text_fontsize = "大小";	//"Size"
RTE_DefaultConfig.text_forecolor = "文本颜色";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "背景颜色";	//"Back Color"
RTE_DefaultConfig.text_justify = "证明";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "对齐左侧";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "正确对齐";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "对齐中心";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "充分证明";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "证明无";	//"Justify None"
RTE_DefaultConfig.text_delete = "删除";	//"Delete"
RTE_DefaultConfig.text_save = "保存文件";	//"Save file"
RTE_DefaultConfig.text_selectall = "选择全部";	//"Select All"
RTE_DefaultConfig.text_code = "HTML 代码";	//"HTML Code"
RTE_DefaultConfig.text_preview = "预览";	//"Preview"
RTE_DefaultConfig.text_print = "打印";	//"Print"
RTE_DefaultConfig.text_undo = "撤消";	//"Undo"
RTE_DefaultConfig.text_redo = "整修";	//"Redo"
RTE_DefaultConfig.text_more = "更。。。";	//"More..."
RTE_DefaultConfig.text_newdoc = "新文档";	//"New Doc"
RTE_DefaultConfig.text_help = "帮助";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "适合窗口";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "退出全屏";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "图像编辑器";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "图像样式";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "内联样式";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "段落样式";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "链接样式";	//"Link Styles"
RTE_DefaultConfig.text_link = "链接";	//"Link"
RTE_DefaultConfig.text_style = "样式";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css 类";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "按 Url";	//"By Url"
RTE_DefaultConfig.text_upload = "上传";	//"Upload"
RTE_DefaultConfig.text_size = "大小";	//"Size"
RTE_DefaultConfig.text_text = "文本";	//"Text"
RTE_DefaultConfig.text_opennewwin = "在新选项卡中打开";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "插入";	//"Insert"
RTE_DefaultConfig.text_update = "更新";	//"Update"
RTE_DefaultConfig.text_find = "查找+替换";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "查找";	//"Find"
RTE_DefaultConfig.text_replacewith = "替换";	//"Replace"
RTE_DefaultConfig.text_findnext = "下一个";	//"Next"
RTE_DefaultConfig.text_replaceonce = "替换";	//"Replace"
RTE_DefaultConfig.text_replaceall = "替换全部";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "大小写匹配";	//"Match Case"
RTE_DefaultConfig.text_matchword = "单词匹配";	//"Match Word"
RTE_DefaultConfig.text_move_down = "向下移动";	//"Move Down"
RTE_DefaultConfig.text_move_up = "向上移动";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "自动大小";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% 宽度";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% 宽度";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% 宽度";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% 宽度";	//"25% width"
RTE_DefaultConfig.text_controlsize = "设置大小";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt 文本";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "证明";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "图片说明";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "合并单元格";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "垂直分割单元格";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "水平分割单元格";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "单元格文本颜色";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "单元格背面颜色";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "在上面插入行";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "插入下面的行";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "向左插入列";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "向右插入列";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "删除列";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "删除行";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "删除表";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "自动大小";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "表标题";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "添加新段落";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "粘贴";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "粘贴";	//"Paste"
RTE_DefaultConfig.text_pastetext = "粘贴文本";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "粘贴为HTML";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "粘贴WORD内容";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "请使用 CTRL+V 将内容粘贴到下面的框中。\r\n内容将自动清理。";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "段落";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "段落";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "向上移动";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "向下移动";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "重复";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "删除";	//"Delete"
RTE_DefaultConfig.text_pmore = "更多..";	//"More.."
RTE_DefaultConfig.text_togglemore = "更多..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "切换边框";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "削减";	//"Cut"
RTE_DefaultConfig.text_copy = "复制";	//"Copy"
RTE_DefaultConfig.text_copied = "复制";	//"copied"
RTE_DefaultConfig.text_insertgallery = "插入库";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "插入文档";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "插入模板";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "预览";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "正常";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "移动";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "平板 电脑";	//"Tablet"
RTE_DefaultConfig.text_table = "表";	//"Table"
RTE_DefaultConfig.text_tablecell = "表格单元格";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "表行";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "表列";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "自动";	//"Automatic"
RTE_DefaultConfig.text_colormore = "更多";	//"More"
RTE_DefaultConfig.text_colorpicker = "颜色选取器";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "网络调色板";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "命名颜色";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "基本";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "除了";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "拖放";	//"Drag and drop"
RTE_DefaultConfig.text_or = "或";	//"or"
RTE_DefaultConfig.text_clicktoupload = "单击以上传";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "默认图像标题";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "搜索";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "要添加的文本已达到此字段的字符限制。";	//"The text to be added has reached the character limit for this field."
