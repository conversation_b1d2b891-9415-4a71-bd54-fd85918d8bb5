'use client';

import { styled } from '@mui/material/styles';
import FusePageSimple from '@fuse/core/FusePageSimple/FusePageSimple';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import { useEffect, useRef, useState } from 'react';
import Link from '@fuse/core/Link';
import { Button, Divider, IconButton, Paper, Typography, CircularProgress } from '@mui/material';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import Error404Page from '@/app/(public)/404/Error404Page';
import { useParams } from 'next/navigation';
import Box from '@mui/material/Box';
import AnnouncementContent from './AnnouncementContent';
import { useAnnouncementDetail } from './useAnnouncementDetail';
import AnnouncementInfo from '../AnnouncementInfo';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

function Announcement() {
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(!isMobile);
  const pageLayout = useRef(null);

  const params = useParams();
  const [announcementId] = params.announcement as string;

  const { announcement, isLoading, error } = useAnnouncementDetail(announcementId);

  useEffect(() => {
    setLeftSidebarOpen(!isMobile);
  }, [isMobile]);

  if (isLoading) {
    return (
      <Root
        content={
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '400px',
            }}
          >
            <CircularProgress size={20} />
            <Typography variant="h6" color="text.secondary" sx={{ ml: 2 }}>
              Loading announcement...
            </Typography>
          </Box>
        }
      />
    );
  }

  if (error || !announcement) {
    return <Error404Page />;
  }

  return (
    <Root
      content={
        <div className="flex flex-col min-h-full w-full relative">
          {isMobile && (
            <Paper
              className="flex sticky top-0 z-10 items-center w-full px-4 py-2 border-b-1 shadow-0"
              square
            >
              <IconButton to="/portal/communication/announcement" component={Link}>
                <FuseSvgIcon>&apos;heroicons-outline:arrow-left&apos;</FuseSvgIcon>
              </IconButton>

              <Typography className="text-md font-medium tracking-tight mx-2.5">
                {announcement.title}
              </Typography>
            </Paper>
          )}

          <div className="flex flex-col flex-auto w-full min-h-full">
            <div className="flex justify-center p-4 pb-16 sm:p-6 sm:pb-16 md:p-12 md:pb-24 min-h-full">
              <AnnouncementContent announcement={announcement} />
            </div>
          </div>
        </div>
      }
      leftSidebarOpen={leftSidebarOpen}
      leftSidebarOnClose={() => {
        setLeftSidebarOpen(false);
      }}
      leftSidebarWidth={400}
      leftSidebarContent={
        <>
          <div className="p-8">
            <Button
              to="/portal/communication/announcement"
              component={Link}
              className="mb-6"
              color="secondary"
              variant="text"
              startIcon={<FuseSvgIcon size={20}>heroicons-outline:arrow-small-left</FuseSvgIcon>}
            >
              Back to announcements
            </Button>

            <AnnouncementInfo announcement={announcement} />

            <div className="mt-5">
              {announcement.attachments?.map((attachment, index) => {
                return (
                  <div
                    className="flex items-center gap-2 mt-2 cursor-pointer hover:bg-gray-50 p-2 rounded-md transition-colors"
                    key={index}
                    onClick={() => window.open(attachment?.url, '_blank')}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        window.open(attachment?.url, '_blank');
                      }
                    }}
                  >
                    <Box
                      sx={{ backgroundColor: '#fff' }}
                      className="flex items-center justify-center w-6 h-6 rounded-md overflow-hidden"
                    >
                      <img src="/assets/images/icons/pdf-icon.png" alt="pdf icon" />
                    </Box>

                    <div className="flex-1">
                      <Typography className="text-md font-medium tracking-tight break-words">
                        {attachment.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Click to open
                      </Typography>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          <Divider />
        </>
      }
      scroll="content"
      ref={pageLayout}
      contentScrollbarsProps={{
        scrollToTopOnChildChange: true,
      }}
    />
  );
}

export default Announcement;
