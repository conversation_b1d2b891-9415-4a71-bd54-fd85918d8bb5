'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { workflowApiService } from '@/services/api/workflowService';
import {
  CreateWorkflowDto,
  IAttachment,
  WorkflowResponseDto,
  WorkflowStatusEnum,
} from '@/services/api/types/workflow.types';
import { uploadToS3, deleteFromS3 } from '@/app/lib/s3Client';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for workflow form data passed to the createWorkflow function.
 * Attachments are now handled internally by the hook.
 */
export interface CreateWorkflowHookParams {
  title: string;
  description: string;
  amount: number;
  workflowTypeId: string;
  authorizingUser: string; // First authorizer
}

/**
 * Interface for individual file wrapper used by the hook.
 */
export interface FileWrapper {
  id: string; // Unique identifier for the file in the list
  file: File;
  preview: string; // Data URL for image previews
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string; // S3 URL after successful upload
  s3Key?: string; // S3 key after successful upload
  error?: string; // Error message if upload fails or validation fails
}

/**
 * Interface for pre-upload validation errors.
 */
export interface AttachmentValidationError {
  fileName: string;
  message: string;
}

/**
 * Interface for the hook's return value.
 */
export interface UseWorkflowCreationReturn {
  isSubmitting: boolean;
  uploadedFiles: FileWrapper[];
  attachmentValidationErrors: AttachmentValidationError[];
  submissionError: string | null;
  createWorkflow: (params: CreateWorkflowHookParams) => Promise<WorkflowResponseDto | null>;
  handleFileChange: (acceptedFiles: File[], fileRejections?: any[]) => void;
  removeFile: (fileId: string) => Promise<void>;
  cleanupUploads: () => Promise<void>;
  reset: () => void;
}

const MAX_FILES = 10; // Workflows might need more files than memos
const MAX_SIZE_BYTES = 10 * 1024 * 1024; // 10MB - matches S3 'documents' folder config

// Allowed file types for workflow attachments - comprehensive but secure
const ALLOWED_TYPES = {
  // Documents
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/vnd.ms-powerpoint': ['.ppt'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
  'text/plain': ['.txt'],
  'text/csv': ['.csv'],
  // Images
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
  'image/svg+xml': ['.svg'],
  'image/webp': ['.webp'],
  'image/gif': ['.gif'],
  // Archives (for documentation packages)
  'application/zip': ['.zip'],
  'application/x-rar-compressed': ['.rar'],
};

/**
 * Custom hook for handling workflow creation with file uploads.
 */
export function useWorkflowCreation(): UseWorkflowCreationReturn {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  // Ref to track mounted state for cleanup
  const isMountedRef = useRef(true);
  // Ref to track abort controller for request cancellation
  const abortControllerRef = useRef<AbortController | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileWrapper[]>([]);
  const [attachmentValidationErrors, setAttachmentValidationErrors] = useState<
    AttachmentValidationError[]
  >([]);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Cancel any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Clean up all object URLs to prevent memory leaks
      uploadedFiles.forEach((file) => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Additional cleanup when uploadedFiles changes (for removed files)
  useEffect(() => {
    return () => {
      // This cleanup runs when uploadedFiles changes
      // It ensures that old URLs are cleaned up when files are removed
    };
  }, [uploadedFiles]);

  /**
   * Handles file selection from dropzone
   */
  const handleFileChange = useCallback(
    (acceptedFiles: File[], fileRejections: any[] = []) => {
      setAttachmentValidationErrors([]); // Clear previous validation errors
      const newValidationErrors: AttachmentValidationError[] = [];

      // Handle rejected files from Dropzone first
      fileRejections.forEach((rejection) => {
        rejection.errors.forEach((error: any) => {
          newValidationErrors.push({
            fileName: rejection.file.name,
            message: error.message,
          });
        });
      });

      // Check if adding these files would exceed MAX_FILES
      const currentTotalFiles = uploadedFiles.length + acceptedFiles.length;
      if (currentTotalFiles > MAX_FILES) {
        newValidationErrors.push({
          fileName: 'General',
          message: `Cannot upload more than ${MAX_FILES} files. Please remove some files first.`,
        });
        setAttachmentValidationErrors(newValidationErrors);
        return;
      }

      const newFilesToProcess: FileWrapper[] = [];

      acceptedFiles.forEach((file) => {
        // Validate file size
        if (file.size > MAX_SIZE_BYTES) {
          newValidationErrors.push({
            fileName: file.name,
            message: `File is too large (max ${MAX_SIZE_BYTES / 1024 / 1024}MB).`,
          });
          return;
        }

        // Validate file type
        const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        const isValidType = Object.values(ALLOWED_TYPES).some((extensions) =>
          extensions.includes(fileExtension),
        );

        if (!isValidType) {
          const allowedExtensions = Object.values(ALLOWED_TYPES).flat().join(', ');
          newValidationErrors.push({
            fileName: file.name,
            message: `File type not allowed. Allowed types: ${allowedExtensions}`,
          });
          return;
        }

        // Create preview URL for images
        const preview = file.type.startsWith('image/') ? URL.createObjectURL(file) : '';

        // Create FileWrapper object
        const fileWrapper: FileWrapper = {
          id: uuidv4(),
          file,
          preview,
          progress: 0,
          status: 'pending',
        };

        newFilesToProcess.push(fileWrapper);
      });

      // Update states
      setAttachmentValidationErrors(newValidationErrors);
      setUploadedFiles((prev) => [...prev, ...newFilesToProcess]);
    },
    [uploadedFiles.length],
  );

  /**
   * Removes a file from the uploaded files list
   */
  const removeFile = useCallback(
    async (fileId: string) => {
      const fileToRemove = uploadedFiles.find((f) => f.id === fileId);
      if (!fileToRemove) return;

      // If file was uploaded to S3, delete it
      if (fileToRemove.s3Key) {
        try {
          await deleteFromS3(fileToRemove.s3Key);
        } catch (error) {
          console.error('Failed to delete file from S3:', error);
          // Don't throw here - we still want to remove from UI
        }
      }

      // Revoke preview URL to free memory
      if (fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

      // Remove from state
      setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    },
    [uploadedFiles],
  );

  /**
   * Uploads pending files to S3 and returns attachment DTOs
   */
  const uploadAndPrepareAttachments = useCallback(async (): Promise<IAttachment[]> => {
    const attachmentDtos: IAttachment[] = [];
    const filesToUpload = uploadedFiles.filter((f) => f.status === 'pending');

    for (const fileWrapper of filesToUpload) {
      // Update status to uploading
      setUploadedFiles((prev) =>
        prev.map((f) =>
          f.id === fileWrapper.id ? { ...f, status: 'uploading' as const, progress: 0 } : f,
        ),
      );

      try {
        // Upload to S3 using 'documents' folder for workflows (allows all file types)
        const uploadResult = await uploadToS3(fileWrapper.file, 'documents');

        // Update file status to completed
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === fileWrapper.id
              ? {
                  ...f,
                  status: 'completed' as const,
                  progress: 100,
                  url: uploadResult.url,
                  s3Key: uploadResult.key,
                }
              : f,
          ),
        );

        // Create attachment DTO
        attachmentDtos.push({
          name: fileWrapper.file.name,
          url: uploadResult.url,
          type: fileWrapper.file.type,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';

        // Update file status to error
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === fileWrapper.id ? { ...f, status: 'error' as const, error: errorMessage } : f,
          ),
        );

        throw new Error(`Failed to upload ${fileWrapper.file.name}: ${errorMessage}`);
      }
    }

    return attachmentDtos;
  }, [uploadedFiles]);

  /**
   * Cleans up uploaded files from S3
   */
  const cleanupUploads = useCallback(async () => {
    const filesToCleanup = uploadedFiles.filter((f) => f.s3Key && f.status === 'completed');

    for (const file of filesToCleanup) {
      try {
        await deleteFromS3(file.s3Key!);
      } catch (error) {
        console.error(`Failed to cleanup attachment ${file.file.name}:`, error);
        // Don't throw here - we don't want cleanup failures to affect the user experience
      }
    }
  }, [uploadedFiles]);

  /**
   * Creates a workflow with the provided form data
   */
  const createWorkflow = useCallback(
    async (params: CreateWorkflowHookParams): Promise<WorkflowResponseDto | null> => {
      setIsSubmitting(true);
      setSubmissionError(null);

      try {
        // Basic validation
        if (!params.title?.trim()) throw new Error('Workflow title is required');
        if (!params.description?.trim()) throw new Error('Workflow description is required');
        if (!params.workflowTypeId?.trim()) throw new Error('Workflow type is required');
        if (!params.authorizingUser?.trim()) throw new Error('Authorizing user is required');
        if (params.amount === undefined || params.amount < 0)
          throw new Error('Valid amount is required');

        // Upload attachments from the uploadedFiles state
        const s3Attachments = await uploadAndPrepareAttachments();

        // Prepare DTO for workflow creation
        const workflowDto: CreateWorkflowDto = {
          title: params.title.trim(),
          description: params.description.trim(),
          amount: params.amount,
          workflowTypeId: params.workflowTypeId,
          authorizingUser: params.authorizingUser,
          status: WorkflowStatusEnum.New, // New workflows start as "New"
          attachments: s3Attachments.length > 0 ? s3Attachments : undefined,
        };

        // Call API to create workflow
        const createdWorkflow = await workflowApiService.createWorkflow(workflowDto);

        // PDF generation removed from creation flow
        // PDFs are now generated on-demand in the PDF viewer with complete workflow data

        enqueueSnackbar('Workflow created successfully!', {
          variant: 'success',
          autoHideDuration: 3000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        });

        router.push(`/portal/e-office/workflow/${createdWorkflow.id}`);
        return createdWorkflow;
      } catch (err: any) {
        console.error('Workflow creation failed:', err);
        const message =
          err.response?.data?.message || err.message || 'An unexpected error occurred.';
        setSubmissionError(message);

        enqueueSnackbar(`Error: ${message}`, {
          variant: 'error',
          autoHideDuration: 5000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        });

        // If workflow creation fails AFTER attachments were uploaded, cleanup S3
        if (uploadedFiles.some((f) => f.status === 'completed' && f.s3Key)) {
          await cleanupUploads();
        }

        return null;
      } finally {
        setIsSubmitting(false);
      }
    },
    [enqueueSnackbar, router, uploadedFiles, uploadAndPrepareAttachments, cleanupUploads],
  );

  /**
   * Resets the hook state to initial values
   */
  const reset = useCallback(() => {
    // Revoke all preview URLs to free memory
    uploadedFiles.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });

    setIsSubmitting(false);
    setUploadedFiles([]);
    setAttachmentValidationErrors([]);
    setSubmissionError(null);
  }, [uploadedFiles]);

  return {
    isSubmitting,
    uploadedFiles,
    attachmentValidationErrors,
    submissionError,
    createWorkflow,
    handleFileChange,
    removeFile,
    cleanupUploads,
    reset,
  };
}
