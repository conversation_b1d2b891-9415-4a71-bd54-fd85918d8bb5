//Tamil , தமிழ்
RTE_DefaultConfig.text_language = "மொழி";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "எதிராணை";	//"Cancel"
RTE_DefaultConfig.text_normal = "இயல்பான நிலை";	//"Normal"
RTE_DefaultConfig.text_h1 = "தலைப்பு 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "தலைப்பு 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "தலைப்பு 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "தலைப்பு 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "தலைப்பு 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "தலைப்பு 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "தலைப்பு 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "மூடு";	//"Close"
RTE_DefaultConfig.text_bold = "துணிவுள்ள";	//"Bold"
RTE_DefaultConfig.text_italic = "சாய்வெழுத்து";	//"Italic"
RTE_DefaultConfig.text_underline = "அடிவரி";	//"Underline"
RTE_DefaultConfig.text_strike = "வேலைநிறுத்த வரி";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "மேலிடத்திலுள்ள";	//"Superscript"
RTE_DefaultConfig.text_subscript = "சுஸ்கிரிப்ட்";	//"Subcript"
RTE_DefaultConfig.text_ucase = "மேல் வழக்கு";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "கீழ் கேஸ்";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "வடிவமைப்பை நீக்கு";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "இணைப்பை செருகு";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "இணைப்பை திற";	//"Open Link"
RTE_DefaultConfig.text_editlink = "இணைப்பை திருத்து";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "இணைப்பை நீக்கு";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "கோடு உயரம்";	//"Line Height"
RTE_DefaultConfig.text_indent = "வடு";	//"Indent"
RTE_DefaultConfig.text_outdent = "சூலம்";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "மேற்கோள்குறி தடு";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "ஆர்டர் பட்டியல்";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "வரிசைபெறாத பட்டியல்";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "கிடைமட்ட விதி செருகவும்";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "தேதியை நுழை";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "அட்டவணையை செருகு";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "பிம்பத்தை செருகு";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "வீடியோவை செருகு";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "குறியீட்டை செருகு";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF உருவாக்கு";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "ஈமோஜியை செருகு";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "சிறப்பு எழுத்துக்கள்";	//"Special characters"
RTE_DefaultConfig.text_characters = "கேரக்டர்கள்";	//"Characters"
RTE_DefaultConfig.text_fontname = "எழுத்துரு";	//"Font"
RTE_DefaultConfig.text_fontsize = "பருமன்";	//"Size"
RTE_DefaultConfig.text_forecolor = "உரை வண்ணம்";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "பின் வண்ணம்";	//"Back Color"
RTE_DefaultConfig.text_justify = "எண்பி";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "இடது சீரமை";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "வலது சீரமை";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "நடுசீரமை";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "முழு வரிசீரமை";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "ஏதுமில்லை சீரமை";	//"Justify None"
RTE_DefaultConfig.text_delete = "தீங்கான";	//"Delete"
RTE_DefaultConfig.text_save = "கோப்பினை சேமி";	//"Save file"
RTE_DefaultConfig.text_selectall = "அனைத்தையும் தேர்ந்தெடு";	//"Select All"
RTE_DefaultConfig.text_code = "HTML குறியீடு";	//"HTML Code"
RTE_DefaultConfig.text_preview = "வெள்ளோட்டம்";	//"Preview"
RTE_DefaultConfig.text_print = "அச்சு";	//"Print"
RTE_DefaultConfig.text_undo = "கெடு";	//"Undo"
RTE_DefaultConfig.text_redo = "திருப்பிச்செய்";	//"Redo"
RTE_DefaultConfig.text_more = "விஞ்சி மிகையளவான...";	//"More..."
RTE_DefaultConfig.text_newdoc = "புதிய ஆவண";	//"New Doc"
RTE_DefaultConfig.text_help = "உதவி";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "சாளரத்திற்கு பொருத்து";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "முழுத் திரைவெளியேறு";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "பட திருத்தி";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "படிம பாணிகள்";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "இன்லைன் பாணிகள்";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "பத்தி பாணிகள்";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "இணைப்பு பாணிகள்";	//"Link Styles"
RTE_DefaultConfig.text_link = "கண்ணி";	//"Link"
RTE_DefaultConfig.text_style = "பாணிகள்";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css வகுப்புகள்";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url ஆல்";	//"By Url"
RTE_DefaultConfig.text_upload = "பதிவேற்றம்";	//"Upload"
RTE_DefaultConfig.text_size = "பருமன்";	//"Size"
RTE_DefaultConfig.text_text = "முதுப்பாடம்";	//"Text"
RTE_DefaultConfig.text_opennewwin = "புதிய தாவலில் திற";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "செருகு";	//"Insert"
RTE_DefaultConfig.text_update = "புதுப்பி";	//"Update"
RTE_DefaultConfig.text_find = "& மாற்றிடு கண்டுபிடி";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "கண்டுபிடி";	//"Find"
RTE_DefaultConfig.text_replacewith = "பின்னுற்றிடங் கொள்";	//"Replace"
RTE_DefaultConfig.text_findnext = "அடுத்தவர்";	//"Next"
RTE_DefaultConfig.text_replaceonce = "பின்னுற்றிடங் கொள்";	//"Replace"
RTE_DefaultConfig.text_replaceall = "அனைத்தையும் மாற்று";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "கேஸ் பொருத்தம்";	//"Match Case"
RTE_DefaultConfig.text_matchword = "வார்த்தை பொருத்தம்";	//"Match Word"
RTE_DefaultConfig.text_move_down = "கீழே நகர்த்து";	//"Move Down"
RTE_DefaultConfig.text_move_up = "மேலே நகர்த்து";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "தானாக அளவு";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% அகலம்";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% அகலம்";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% அகலம்";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% அகலம்";	//"25% width"
RTE_DefaultConfig.text_controlsize = "அளவை அமை";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt உரை";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "எண்பி";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "படிம தலைப்பு";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "செல்களை ஒன்றுசேர்";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "செல்களை செங்குத்தாக பிரி";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "செல்களை கிடைமட்டமாக பிரி";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "செல் உரை வண்ணம்";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "செல் பின் வண்ணம்";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "மேலே வரிசையை நுழை";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "கீழே கிடக்கையைச் செருகு";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "இடது நெடுவரிசை செருகு";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "நெடுவரிசை வலதுபுறம் நுழை";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "நெடுவரிசை நீக்கு";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "கிடக்கையைச் நீக்கு";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "அட்டவணையை நீக்கு";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "தானாக அளவு";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "அட்டவணை மேற்குறிப்பு";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "புதிய பத்தியை சேர்";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "கூழ்";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "கூழ்";	//"Paste"
RTE_DefaultConfig.text_pastetext = "உரையை ஒட்டு";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Html ஆக ஒட்டு";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "வார்த்தையை ஒட்டு";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "கீழே உள்ள பெட்டியில் உள்ளடக்கத்தை ஒட்ட CTRL+V பயன்படுத்தவும். \r\nஉள்ளடக்கம் தானாகவே சுத்தம் செய்யப்படும்.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "பத்திகள்";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "பத்திகள்";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "மேலே நகர்த்து";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "கீழே நகர்த்து";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "பார்த்தெழுதிய எதிர்ப்படி";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "தீங்கான";	//"Delete"
RTE_DefaultConfig.text_pmore = "விஞ்சி மிகையளவான..";	//"More.."
RTE_DefaultConfig.text_togglemore = "விஞ்சி மிகையளவான..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "கரை மாற்று";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "தறிப்பு";	//"Cut"
RTE_DefaultConfig.text_copy = "சரிபடிவம்";	//"Copy"
RTE_DefaultConfig.text_copied = "நகலெடுக்க";	//"copied"
RTE_DefaultConfig.text_insertgallery = "படத்தொகுப்பை செருகவும்";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "ஆவணத்தை செருகு";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "வார்ப்புரு வை செருகவும்";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "வெள்ளோட்டம்";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "இயல்பான நிலை";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "இயங்குகிற";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "பட்டயத் தகடு";	//"Tablet"
RTE_DefaultConfig.text_table = "மேசை";	//"Table"
RTE_DefaultConfig.text_tablecell = "அட்டவணை செல்";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "அட்டவணை வரிசை";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "அட்டவணை நெடுவரிசை";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "தானே இயங்குகிற";	//"Automatic"
RTE_DefaultConfig.text_colormore = "விஞ்சி மிகையளவான";	//"More"
RTE_DefaultConfig.text_colorpicker = "வண்ண தேர்வி";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "வலை தட்டு";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "பெயரிடப்பட்ட நிறங்கள்";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "அடிப்படையான";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "கூட்டல்";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "இழுத்து விடு";	//"Drag and drop"
RTE_DefaultConfig.text_or = "அல்லது";	//"or"
RTE_DefaultConfig.text_clicktoupload = "பதிவேற்ற கிளிக் செய்யவும்";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "இயல்புநிலை பட தலைப்பு";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "தேட்டம்";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "சேர்க்க வேண்டிய உரை இந்த புலத்திற்கான கேரக்டர் வரம்பை அடைந்துவிட்டது.";	//"The text to be added has reached the character limit for this field."
