export type QueryAttachment = {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
};

export type QueryResponse = {
  id: string;
  text: string;
  attachments: QueryAttachment[];
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatar: string;
  };
};

export type Query = {
  id: string;
  ref: string;
  subject: string;
  content: string;
  status: 'open' | 'closed';
  deadline: string;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    departmentName: string;
  };
  attachments?: {
    id: string;
    name: string;
    url: string;
  }[];
  read: boolean;
  closedAt?: string;
  recipient: {
    id: string;
    name: string;
    avatar: string;
  };
  responses: QueryResponse[];
};

export const queries: Query[] = [
  {
    id: '1',
    ref: 'Q-2024-001',
    subject: 'Budget Review Request',
    content: 'Please review the attached budget proposal for the upcoming fiscal year.',
    status: 'open',
    deadline: '2024-03-20T14:30:00',
    createdAt: '2024-03-15T10:00:00',
    createdBy: {
      id: '1',
      name: '<PERSON>',
      departmentName: 'Finance',
    },
    attachments: [
      {
        id: '1',
        name: 'Budget_Proposal_2024.pdf',
        url: '/assets/documents/Budget_Proposal_2024.pdf',
      },
    ],
    read: false,
    recipient: {
      id: 'user1',
      name: 'John Doe',
      avatar: '/assets/images/avatars/john.jpg',
    },
    responses: [
      {
        id: 'resp1',
        text: 'I will provide the documents by next week.',
        attachments: [],
        createdAt: '2024-03-16T14:30:00Z',
        createdBy: {
          id: 'user1',
          name: 'John Doe',
          avatar: '/assets/images/avatars/john.jpg',
        },
      },
      {
        id: 'resp2',
        text: 'Thank you for your response. Please note that we need these documents before the end of the month.',
        attachments: [],
        createdAt: '2024-03-16T15:00:00Z',
        createdBy: {
          id: 'admin1',
          name: 'Admin User',
          avatar: '/assets/images/avatars/admin.jpg',
        },
      },
      {
        id: 'resp3',
        text: 'I understand. I have attached my National ID. I will send the proof of address tomorrow as I need to get it from my bank.',
        attachments: [
          {
            id: '2',
            name: 'national_id.pdf',
            size: 2048,
            type: 'application/pdf',
            url: '/assets/documents/national_id.pdf',
          },
        ],
        createdAt: '2024-03-17T09:15:00Z',
        createdBy: {
          id: 'user1',
          name: 'John Doe',
          avatar: '/assets/images/avatars/john.jpg',
        },
      },
      {
        id: 'resp4',
        text: 'Thank you for providing your National ID. We have received it. Please ensure to send the proof of address as soon as possible.',
        attachments: [],
        createdAt: '2024-03-17T10:30:00Z',
        createdBy: {
          id: 'admin1',
          name: 'Admin User',
          avatar: '/assets/images/avatars/admin.jpg',
        },
      },
      {
        id: 'resp5',
        text: 'I have attached the bank statement as proof of address. Please let me know if you need anything else.',
        attachments: [
          {
            id: '3',
            name: 'bank_statement.pdf',
            size: 3072,
            type: 'application/pdf',
            url: '/assets/documents/bank_statement.pdf',
          },
        ],
        createdAt: '2024-03-18T11:20:00Z',
        createdBy: {
          id: 'user1',
          name: 'John Doe',
          avatar: '/assets/images/avatars/john.jpg',
        },
      },
      {
        id: 'resp6',
        text: 'We have received both documents. However, we notice that the bank statement is from last month. Could you please provide a more recent one?',
        attachments: [],
        createdAt: '2024-03-18T13:45:00Z',
        createdBy: {
          id: 'admin1',
          name: 'Admin User',
          avatar: '/assets/images/avatars/admin.jpg',
        },
      },
    ],
  },
  {
    id: '2',
    ref: 'Q-2024-002',
    subject: 'Project Timeline Update',
    content: 'Requesting an update on the current project timeline and milestones.',
    status: 'closed',
    deadline: '2024-03-18T16:00:00',
    createdAt: '2024-03-14T09:30:00',
    createdBy: {
      id: '2',
      name: 'Jane Doe',
      departmentName: 'Project Management',
    },
    read: true,
    closedAt: '2024-03-11T14:00:00Z',
    recipient: {
      id: 'user1',
      name: 'John Doe',
      avatar: '/assets/images/avatars/john.jpg',
    },
    responses: [
      {
        id: 'resp7',
        text: 'Yes, the changes are correct. Thank you for confirming.',
        attachments: [],
        createdAt: '2024-03-11T11:20:00Z',
        createdBy: {
          id: 'user1',
          name: 'John Doe',
          avatar: '/assets/images/avatars/john.jpg',
        },
      },
      {
        id: 'resp8',
        text: 'Great, we will close this query now.',
        attachments: [],
        createdAt: '2024-03-11T14:00:00Z',
        createdBy: {
          id: 'admin2',
          name: 'Support Team',
          avatar: '/assets/images/avatars/support.jpg',
        },
      },
    ],
  },
];
