import {
  AnnouncementFilterDto,
  AnnouncementResponseDto,
  CreateAnnouncementDto,
  PaginatedAnnouncementResponseDto,
  UpdateAnnouncementDto,
} from '../dtos';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { AnnouncementService } from '../services/announcement.service';

@ApiTags('Announcement - Admin')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('announcement')
export class AnnouncementController {
  constructor(private readonly announcementService: AnnouncementService) {}
  @Post()
  @ApiOperation({ summary: 'Create a new announcement' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 201, type: AnnouncementResponseDto })
  async create(
    @Body() dto: CreateAnnouncementDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<AnnouncementResponseDto> {
    return this.announcementService.create(dto, jwtPayload.id);
  }
  @Get(':id')
  @ApiOperation({ summary: 'Get one announcement record' })
  @ApiResponse({ status: 200, type: AnnouncementResponseDto })
  async getRecord(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<AnnouncementResponseDto> {
    return this.announcementService.getOne({ id, employeeId: jwtPayload.id });
  }
  @Get()
  @ApiOperation({ summary: 'Get all announcement records' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: AnnouncementResponseDto })
  async getAllAnnouncements(
    @Query() query: AnnouncementFilterDto,
  ): Promise<PaginatedAnnouncementResponseDto> {
    return this.announcementService.getAll(query);
  }
  @Patch(':id')
  @ApiOperation({ summary: 'Update announcement record' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: AnnouncementResponseDto })
  updateAnnouncement(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Body() dto: UpdateAnnouncementDto,
  ): Promise<AnnouncementResponseDto> {
    return this.announcementService.updateRecord({ id, data: dto, employeeId: jwtPayload.id });
  }
}
