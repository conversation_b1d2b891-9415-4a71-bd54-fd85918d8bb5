//Hindi , हिंदी
RTE_DefaultConfig.text_language = "भाषा";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "रद्द";	//"Cancel"
RTE_DefaultConfig.text_normal = "सामान्य";	//"Normal"
RTE_DefaultConfig.text_h1 = "हेडलाइन 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "हेडलाइन 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "हेडलाइन 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "हेडलाइन 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "हेडलाइन 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "हेडलाइन 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "हेडलाइन 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "बंद";	//"Close"
RTE_DefaultConfig.text_bold = "बोल्ड";	//"Bold"
RTE_DefaultConfig.text_italic = "तिरछा";	//"Italic"
RTE_DefaultConfig.text_underline = "रेखांकन";	//"Underline"
RTE_DefaultConfig.text_strike = "स्ट्राइक लाइन";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "सुपरस्क्रिप्ट";	//"Superscript"
RTE_DefaultConfig.text_subscript = "उपक्रिप्ट";	//"Subcript"
RTE_DefaultConfig.text_ucase = "ऊपरी मामला";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "कम मामला";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "प्रारूप निकालें";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "लिंक डालें";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "ओपन लिंक";	//"Open Link"
RTE_DefaultConfig.text_editlink = "एडिट लिंक";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "लिंक निकालें";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "लाइन हाइट";	//"Line Height"
RTE_DefaultConfig.text_indent = "इंडेंट";	//"Indent"
RTE_DefaultConfig.text_outdent = "आउटडेंट";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "ब्लॉक कोट";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "आदेश सूची";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "अव्यवस्थित सूची";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "क्षैतिज नियम डालें";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "डालने की तारीख";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "टेबल डालें";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "छवि डालें";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "वीडियो डालें";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "कोड डालें";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "पीडीएफ बनाएं";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "इमोजी डालें";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "विशेष वर्ण";	//"Special characters"
RTE_DefaultConfig.text_characters = "वर्ण";	//"Characters"
RTE_DefaultConfig.text_fontname = "फ़ॉन्ट";	//"Font"
RTE_DefaultConfig.text_fontsize = "आकार";	//"Size"
RTE_DefaultConfig.text_forecolor = "टेक्स्ट कलर";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "बैक कलर";	//"Back Color"
RTE_DefaultConfig.text_justify = "का औचित्य साबित";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "वाम का औचित्य साबित";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "सही औचित्य साबित";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "केंद्र का औचित्य साबित";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "पूर्ण औचित्य साबित";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "किसी को भी न्यायोचित नहीं ठहराया";	//"Justify None"
RTE_DefaultConfig.text_delete = "हटाएँ";	//"Delete"
RTE_DefaultConfig.text_save = "फ़ाइल सहेजें";	//"Save file"
RTE_DefaultConfig.text_selectall = "सभी का चयन करें";	//"Select All"
RTE_DefaultConfig.text_code = "एचटीएमएल कोड";	//"HTML Code"
RTE_DefaultConfig.text_preview = "पूर्वावलोकन";	//"Preview"
RTE_DefaultConfig.text_print = "प्रिंट";	//"Print"
RTE_DefaultConfig.text_undo = "पूर्ववत्";	//"Undo"
RTE_DefaultConfig.text_redo = "दोहराएँ";	//"Redo"
RTE_DefaultConfig.text_more = "अधिक...";	//"More..."
RTE_DefaultConfig.text_newdoc = "नई डॉक्टर";	//"New Doc"
RTE_DefaultConfig.text_help = "मदद";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "विंडो के लिए फिट";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "फुल स्क्रीन से बाहर निकलें";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "छवि संपादक";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "छवि शैलियों";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "इनलाइन स्टाइल्स";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "पैरा स्टाइल्स";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "लिंक स्टाइल्स";	//"Link Styles"
RTE_DefaultConfig.text_link = "लिंक";	//"Link"
RTE_DefaultConfig.text_style = "शैलियों";	//"Styles"
RTE_DefaultConfig.text_cssclass = "सीएसएस क्लासेज";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "यूआरएल द्वारा";	//"By Url"
RTE_DefaultConfig.text_upload = "अपलोड";	//"Upload"
RTE_DefaultConfig.text_size = "आकार";	//"Size"
RTE_DefaultConfig.text_text = "पाठ";	//"Text"
RTE_DefaultConfig.text_opennewwin = "नए टैब में खुला";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "सम्मिलित";	//"Insert"
RTE_DefaultConfig.text_update = "अद्यतन";	//"Update"
RTE_DefaultConfig.text_find = "खोजें और बदलें";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "ढूँढें";	//"Find"
RTE_DefaultConfig.text_replacewith = "बदलें";	//"Replace"
RTE_DefaultConfig.text_findnext = "अगले";	//"Next"
RTE_DefaultConfig.text_replaceonce = "बदलें";	//"Replace"
RTE_DefaultConfig.text_replaceall = "सभी की जगह";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "मैच मामला";	//"Match Case"
RTE_DefaultConfig.text_matchword = "मैच वर्ड";	//"Match Word"
RTE_DefaultConfig.text_move_down = "नीचे हटो";	//"Move Down"
RTE_DefaultConfig.text_move_up = "ऊपर ले जाएं";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "ऑटो का आकार";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% चौड़ाई";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% चौड़ाई";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% चौड़ाई";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% चौड़ाई";	//"25% width"
RTE_DefaultConfig.text_controlsize = "सेट साइज";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "ऑल्ट टेक्स्ट";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "का औचित्य साबित";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Image Caption";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "कोशिकाओं का विलय";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "स्प्लिट सेल वर्टिकल";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "स्प्लिट सेल क्षैतिज";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "सेल टेक्स्ट कलर";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "सेल बैक कलर";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "ऊपर पंक्ति डालें";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "नीचे पंक्ति डालें";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "कॉलम बाएं डालें";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "कॉलम सही डालें";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "कॉलम हटाएं";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "पंक्ति को हटाएं";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "टेबल हटाएं";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "ऑटो का आकार";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "टेबल हेडर";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "एक नया पैराग्राफ जोड़ें";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "चिपकाएँ";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "चिपकाएँ";	//"Paste"
RTE_DefaultConfig.text_pastetext = "पेस्ट टेक्स्ट";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "एचटीएमएल के रूप में पेस्ट";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "पेस्ट वर्ड";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "नीचे दिए गए बॉक्स में सामग्री चिपकाने के लिए सीटीआरएल + वी का उपयोग करें। \r\nThe सामग्री स्वचालित रूप से साफ हो जाएगी।";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "अनुच्छेदों";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "अनुच्छेदों";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "ऊपर ले जाएं";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "नीचे हटो";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "डुप्लिकेट";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "हटाएँ";	//"Delete"
RTE_DefaultConfig.text_pmore = "अधिक..";	//"More.."
RTE_DefaultConfig.text_togglemore = "अधिक..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "टॉगल बॉर्डर";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "कट";	//"Cut"
RTE_DefaultConfig.text_copy = "प्रतिलिपि";	//"Copy"
RTE_DefaultConfig.text_copied = "प्रतिलिपि";	//"copied"
RTE_DefaultConfig.text_insertgallery = "गैलरी डालें";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "दस्तावेज़ डालें";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "टेम्पलेट डालें";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "पूर्वावलोकन";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "सामान्य";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "मोबाइल";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "टैब्लेट";	//"Tablet"
RTE_DefaultConfig.text_table = "तालिका";	//"Table"
RTE_DefaultConfig.text_tablecell = "टेबल सेल";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "टेबल रो";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "टेबल कॉलम";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "स्वचालित";	//"Automatic"
RTE_DefaultConfig.text_colormore = "अधिक";	//"More"
RTE_DefaultConfig.text_colorpicker = "रंग बीनने वाला";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "वेब पैलेट";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "नाम के रंग";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "बुनियादी";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "अलावा";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "खींचें और छोड़";	//"Drag and drop"
RTE_DefaultConfig.text_or = "या";	//"or"
RTE_DefaultConfig.text_clicktoupload = "अपलोड करने के लिए क्लिक करें";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "डिफ़ॉल्ट Image caption";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "खोज";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "जोड़ा जाने वाला पाठ इस क्षेत्र के लिए चरित्र सीमा तक पहुंच गया है ।";	//"The text to be added has reached the character limit for this field."
