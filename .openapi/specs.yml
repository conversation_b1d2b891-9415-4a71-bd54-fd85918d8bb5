openapi: 3.0.0
paths:
  /auth/register:
    post:
      operationId: register
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegisterResponseDto"
        "400":
          description: Bad Request
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      summary: Register a new user
      tags: &ref_0
        - Auth
  /auth/login:
    post:
      operationId: login
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoginResponseDto"
        "400":
          description: Bad Request
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      summary: Login a new user
      tags: *ref_0
  /auth/change-password:
    post:
      operationId: changePassword
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChangePasswordDto"
      responses:
        "201":
          description: Password changed successfully
        "400":
          description: Bad Request
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      security:
        - bearer: []
      summary: Change user password
      tags: *ref_0
  /users:
    get:
      operationId: UserController_getUsers
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search keyword (email, first name, last name etc.)
          schema:
            type: string
        - name: role
          required: false
          in: query
          description: Filter by user roles
          schema:
            type: string
            enum:
              - admin
              - employee
              - moderator
        - name: email
          required: false
          in: query
          description: Filter by Email
          schema:
            type: string
        - name: userId
          required: false
          in: query
          description: Filter by User ID
          schema:
            type: string
      responses:
        "200":
          description: List of users
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedUserResponseDto"
        "500":
          description: Internal Server Error
      security:
        - bearer: []
      summary: Get all users
      tags:
        - User
  /organization-structure/departments:
    post:
      operationId: OrganizationStructureController_createDepartment
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDepartmentDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepartmentResponseDto"
      security: &ref_1
        - bearer: []
      summary: Create Department
      tags: &ref_2
        - Organization structure
    get:
      operationId: OrganizationStructureController_getDepartments
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/DepartmentResponseDto"
      security: *ref_1
      summary: Get all Departments
      tags: *ref_2
  /organization-structure/departments/{id}:
    get:
      operationId: OrganizationStructureController_getDepartment
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepartmentResponseDto"
      security: *ref_1
      summary: Get Department by ID
      tags: *ref_2
    patch:
      operationId: OrganizationStructureController_updateDepartment
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateDepartmentDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DepartmentResponseDto"
      security: *ref_1
      summary: Update Department by ID
      tags: *ref_2
    delete:
      operationId: OrganizationStructureController_deleteDepartment
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_1
      summary: Delete Department by ID
      tags: *ref_2
  /organization-structure/mdas:
    post:
      operationId: OrganizationStructureController_createMda
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMdaDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MdaResponseDto"
      security: *ref_1
      summary: Create MDA
      tags: *ref_2
    get:
      operationId: OrganizationStructureController_getMdas
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MdaResponseDto"
      security: *ref_1
      summary: Get all MDAs
      tags: *ref_2
  /organization-structure/mdas/{id}:
    get:
      operationId: OrganizationStructureController_getMda
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MdaResponseDto"
      security: *ref_1
      summary: Get MDA by ID
      tags: *ref_2
    patch:
      operationId: OrganizationStructureController_updateMda
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMdaDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MdaResponseDto"
      security: *ref_1
      summary: Update MDA by ID
      tags: *ref_2
    delete:
      operationId: OrganizationStructureController_deleteMda
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_1
      summary: Delete MDA by ID
      tags: *ref_2
  /organization-structure/units:
    post:
      operationId: OrganizationStructureController_createUnit
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateUnitDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnitResponseDto"
      security: *ref_1
      summary: Create Unit
      tags: *ref_2
    get:
      operationId: OrganizationStructureController_getUnits
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UnitResponseDto"
      security: *ref_1
      summary: Get all Units
      tags: *ref_2
  /organization-structure/units/{id}:
    get:
      operationId: OrganizationStructureController_getUnit
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnitResponseDto"
      security: *ref_1
      summary: Get Unit by ID
      tags: *ref_2
    patch:
      operationId: OrganizationStructureController_updateUnit
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateUnitDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnitResponseDto"
      security: *ref_1
      summary: Update Unit by ID
      tags: *ref_2
    delete:
      operationId: OrganizationStructureController_deleteUnit
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_1
      summary: Delete Unit by ID
      tags: *ref_2
  /organization-structure/positions:
    post:
      operationId: OrganizationStructureController_createPosition
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePositionDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PositionResponseDto"
      security: *ref_1
      summary: Create Position
      tags: *ref_2
    get:
      operationId: OrganizationStructureController_getPositions
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PositionResponseDto"
      security: *ref_1
      summary: Get all Positions
      tags: *ref_2
  /organization-structure/positions/{id}:
    get:
      operationId: OrganizationStructureController_getPosition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PositionResponseDto"
      security: *ref_1
      summary: Get Position by ID
      tags: *ref_2
    patch:
      operationId: OrganizationStructureController_updatePosition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePositionDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PositionResponseDto"
      security: *ref_1
      summary: Update Position by ID
      tags: *ref_2
    delete:
      operationId: OrganizationStructureController_deletePosition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_1
      summary: Delete Position by ID
      tags: *ref_2
  /organization-structure/organization-profiles:
    post:
      operationId: OrganizationStructureController_createOrganizationProfile
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrganizationProfileDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationProfileResponseDto"
      security: *ref_1
      summary: Create Organization Profile
      tags: *ref_2
    get:
      operationId: OrganizationStructureController_getOrganizationProfiles
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OrganizationProfileResponseDto"
      security: *ref_1
      summary: Get all Organization Profiles
      tags: *ref_2
  /organization-structure/organization-profiles/{id}:
    get:
      operationId: OrganizationStructureController_getOrganizationProfile
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationProfileResponseDto"
      security: *ref_1
      summary: Get Organization Profile by ID
      tags: *ref_2
    patch:
      operationId: OrganizationStructureController_updateOrganizationProfile
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateOrganizationProfileDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationProfileResponseDto"
      security: *ref_1
      summary: Update Organization Profile by ID
      tags: *ref_2
    delete:
      operationId: OrganizationStructureController_deleteOrganizationProfile
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_1
      summary: Delete Organization Profile by ID
      tags: *ref_2
  /hr-settings/leave-types:
    post:
      operationId: HrSettingController_createLeaveType
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateLeaveTypeDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveTypeResponseDto"
      security: &ref_3
        - bearer: []
      summary: Create a new leave type
      tags: &ref_4
        - HR Settings
    get:
      operationId: HrSettingController_getAllLeaveTypes
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/LeaveTypeResponseDto"
      security: *ref_3
      summary: Get all leave types
      tags: *ref_4
  /hr-settings/leave-types/{id}:
    get:
      operationId: HrSettingController_getLeaveType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveTypeResponseDto"
      security: *ref_3
      summary: Get a single leave type by ID
      tags: *ref_4
    patch:
      operationId: HrSettingController_updateLeaveType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateLeaveTypeDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LeaveTypeResponseDto"
      security: *ref_3
      summary: Update a leave type by ID
      tags: *ref_4
    delete:
      operationId: HrSettingController_deleteLeaveType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "204":
          description: ""
      security: *ref_3
      summary: Delete a leave type by ID
      tags: *ref_4
  /hr-settings/payment-definitions:
    post:
      operationId: HrSettingController_createPaymentDefinition
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentDefinitionDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentDefinitionResponseDto"
      security: *ref_3
      summary: Create a new payment definition
      tags: *ref_4
    get:
      operationId: HrSettingController_getAllPaymentDefinitions
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/PaymentDefinitionResponseDto"
      security: *ref_3
      summary: Get all payment definitions
      tags: *ref_4
  /hr-settings/payment-definitions/{id}:
    get:
      operationId: HrSettingController_getPaymentDefinition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentDefinitionResponseDto"
      security: *ref_3
      summary: Get a single payment definition by ID
      tags: *ref_4
    patch:
      operationId: HrSettingController_updatePaymentDefinition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePaymentDefinitionDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaymentDefinitionResponseDto"
      security: *ref_3
      summary: Update a payment definition by ID
      tags: *ref_4
    delete:
      operationId: HrSettingController_deletePaymentDefinition
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "204":
          description: ""
      security: *ref_3
      summary: Delete a payment definition by ID
      tags: *ref_4
  /hr-settings/tax-rates:
    post:
      operationId: HrSettingController_createTaxRate
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTaxRateDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TaxRateResponseDto"
      security: *ref_3
      summary: Create a new tax rate
      tags: *ref_4
    get:
      operationId: HrSettingController_getAllTaxRates
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TaxRateResponseDto"
      security: *ref_3
      summary: Get all tax rates
      tags: *ref_4
  /hr-settings/tax-rates/{id}:
    get:
      operationId: HrSettingController_getTaxRate
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TaxRateResponseDto"
      security: *ref_3
      summary: Get a single tax rate by ID
      tags: *ref_4
    patch:
      operationId: HrSettingController_updateTaxRate
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateTaxRateDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TaxRateResponseDto"
      security: *ref_3
      summary: Update a tax rate by ID
      tags: *ref_4
    delete:
      operationId: HrSettingController_deleteTaxRate
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "204":
          description: ""
      security: *ref_3
      summary: Delete a tax rate by ID
      tags: *ref_4
  /employees:
    post:
      operationId: EmployeeController_createEmployee
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateEmployeeDto"
      responses:
        "201":
          description: Employee created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      security: &ref_5
        - bearer: []
      summary: Create Employee
      tags: &ref_6
        - Employee
    get:
      operationId: EmployeeController_getAllEmployees
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search by name or email
          schema:
            type: string
        - name: gender
          required: false
          in: query
          description: Filter by gender
          schema:
            type: string
            enum:
              - Male
              - Female
        - name: unitId
          required: false
          in: query
          description: Filter by unit ID
          schema:
            type: string
        - name: departmentId
          required: false
          in: query
          description: Filter by department ID
          schema:
            type: string
        - name: gradeLevel
          required: false
          in: query
          description: Filter by grade level
          schema:
            type: string
            enum:
              - Level 1
              - Level 2
              - Level 3
              - Level 4
              - Level 5
              - Level 6
              - Level 7
              - Level 8
              - Level 9
              - Level 10
              - Level 11
              - Level 12
              - Level 13
              - Level 14
              - Level 15
              - Level 16
              - Level 17
        - name: stateOfOrigin
          required: false
          in: query
          description: Filter by state of origin
          schema:
            type: string
            enum:
              - Abia
              - Adamawa
              - Akwa Ibom
              - Anambra
              - Bauchi
              - Bayelsa
              - Benue
              - Borno
              - Cross River
              - Delta
              - Ebonyi
              - Edo
              - Ekiti
              - Enugu
              - Gombe
              - Imo
              - Jigawa
              - Kaduna
              - Kano
              - Katsina
              - Kebbi
              - Kogi
              - Kwara
              - Lagos
              - Nasarawa
              - Niger
              - Ogun
              - Ondo
              - Osun
              - Oyo
              - Plateau
              - Rivers
              - Sokoto
              - Taraba
              - Yobe
              - Zamfara
              - Federal Capital Territory
        - name: bloodGroup
          required: false
          in: query
          description: Filter by blood group
          schema:
            type: string
            enum:
              - A+
              - A-
              - B+
              - B-
              - AB+
              - AB-
              - O+
              - O-
        - name: fromBirthDate
          required: false
          in: query
          description: Filter employees born from this date (YYYY-MM-DD)
          schema:
            format: date-time
            type: string
        - name: toBirthDate
          required: false
          in: query
          description: Filter employees born up to this date (YYYY-MM-DD)
          schema:
            format: date-time
            type: string
        - name: fromFirstAppointmentDate
          required: false
          in: query
          description: Filter by first appointment from this date (YYYY-MM-DD)
          schema:
            format: date-time
            type: string
        - name: toFirstAppointmentDate
          required: false
          in: query
          description: Filter by first appointment up to this date (YYYY-MM-DD)
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: List of employees
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedEmployeeResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Get All Employees
      tags: *ref_6
  /employees/{id}:
    get:
      operationId: EmployeeController_getEmployee
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Employee details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Get Employee
      tags: *ref_6
    patch:
      operationId: EmployeeController_updateEmployee
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateEmployeeDto"
      responses:
        "200":
          description: Employee updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Update Employee
      tags: *ref_6
  /employees/token:
    post:
      operationId: EmployeeController_setupEToken
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ETokenDto"
      responses:
        "200":
          description: Action Successful
        "201":
          description: eToken updated!
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Sets or update employee eToken
      tags: *ref_6
  /employees/validate-token:
    post:
      operationId: EmployeeController_eTokenValidation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ETokenDto"
      responses:
        "200":
          description: Action Successful
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Checks provided eToken is correct
      tags: *ref_6
  /employees/signature:
    post:
      operationId: EmployeeController_uploadESignature
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ESignatureDto"
      responses:
        "200":
          description: Action successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_5
      summary: Employee eSignature
      tags: *ref_6
  /employees-self-service/me:
    get:
      operationId: EmployeeSelfServiceController_getEmployeeSelfService
      parameters: []
      responses:
        "200":
          description: Employee self service data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_7
        - bearer: []
      summary: Get Employee Self Service
      tags: &ref_8
        - Employee Self Service
  /employees-self-service/token:
    post:
      operationId: EmployeeSelfServiceController_setupEToken
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SelfServiceETokenDto"
      responses:
        "200":
          description: Action Successful
        "201":
          description: eToken updated!
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_7
      summary: Sets or update employee eToken
      tags: *ref_8
  /employees-self-service/validate-token:
    post:
      operationId: EmployeeSelfServiceController_eTokenValidation
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SelfServiceETokenDto"
      responses:
        "200":
          description: Action Successful
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_7
      summary: Checks provided eToken is correct
      tags: *ref_8
  /employees-self-service/signature:
    post:
      operationId: EmployeeSelfServiceController_uploadESignature
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SelfServiceESignatureDto"
      responses:
        "200":
          description: Action successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_7
      summary: Employee eSignature
      tags: *ref_8
  /chat-messages:
    get:
      operationId: ChatMessageController_getUserMessages
      parameters:
        - name: limit
          required: false
          in: query
          schema:
            type: number
        - name: after
          required: false
          in: query
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: Messages retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ChatMessageResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      security: &ref_9
        - bearer: []
      summary: Get User Messages
      tags: &ref_10
        - Chat Messages
  /chat-messages/{receiverId}:
    get:
      operationId: ChatMessageController_getConversations
      parameters:
        - name: receiverId
          required: true
          in: path
          schema:
            type: string
        - name: limit
          required: false
          in: query
          schema:
            type: number
        - name: after
          required: false
          in: query
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: Messages retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ChatMessageResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
      security: *ref_9
      summary: Get User Conversations
      tags: *ref_10
  /chat-messages/{messageId}/read:
    patch:
      operationId: ChatMessageController_markAsRead
      parameters:
        - name: messageId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Message marked as read successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChatMessageResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Message not found
        "500":
          description: Internal Server Error
      security: *ref_9
      summary: Mark message as read
      tags: *ref_10
  /memos/{id}:
    get:
      operationId: MemoController_show
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
      security: &ref_11
        - bearer: []
      summary: Get one memo record
      tags: &ref_12
        - Memo
  /memos:
    get:
      operationId: MemoController_getAllMemos
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search keyword (title, content, etc.)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by memo status
          schema:
            type: string
            enum:
              - Draft
              - Pending
              - Approved
              - Rejected
        - name: createdById
          required: false
          in: query
          description: User ID of the creator
          schema:
            type: string
        - name: fromEmployeeId
          required: false
          in: query
          description: User ID of the From
          schema:
            type: string
        - name: toEmployeeIds
          required: false
          in: query
          description: Recepients Ids
          schema:
            type: array
            items:
              type: string
        - name: throughEmployeeIds
          required: false
          in: query
          description: Through Receipients IDs
          schema:
            type: array
            items:
              type: string
        - name: fromDate
          required: false
          in: query
          description: Start date for filtering
          schema:
            format: date-time
            type: string
        - name: toDate
          required: false
          in: query
          description: End date for filtering
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_11
      tags: *ref_12
  /memos-self-service/sent:
    get:
      operationId: MemoSelfServiceController_getSentMemos
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search keyword (title, content, etc.)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by memo status
          schema:
            type: string
            enum:
              - Draft
              - Pending
              - Approved
              - Rejected
        - name: createdById
          required: false
          in: query
          description: User ID of the creator
          schema:
            type: string
        - name: fromEmployeeId
          required: false
          in: query
          description: User ID of the From
          schema:
            type: string
        - name: toEmployeeIds
          required: false
          in: query
          description: Recepients Ids
          schema:
            type: array
            items:
              type: string
        - name: throughEmployeeIds
          required: false
          in: query
          description: Through Receipients IDs
          schema:
            type: array
            items:
              type: string
        - name: fromDate
          required: false
          in: query
          description: Start date for filtering
          schema:
            format: date-time
            type: string
        - name: toDate
          required: false
          in: query
          description: End date for filtering
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_13
        - bearer: []
      tags: &ref_14
        - Memo Self Service
  /memos-self-service/inbox:
    get:
      operationId: MemoSelfServiceController_getInboxMemos
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search keyword (title, content, etc.)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by memo status
          schema:
            type: string
            enum:
              - Draft
              - Pending
              - Approved
              - Rejected
        - name: createdById
          required: false
          in: query
          description: User ID of the creator
          schema:
            type: string
        - name: fromEmployeeId
          required: false
          in: query
          description: User ID of the From
          schema:
            type: string
        - name: toEmployeeIds
          required: false
          in: query
          description: Recepients Ids
          schema:
            type: array
            items:
              type: string
        - name: throughEmployeeIds
          required: false
          in: query
          description: Through Receipients IDs
          schema:
            type: array
            items:
              type: string
        - name: fromDate
          required: false
          in: query
          description: Start date for filtering
          schema:
            format: date-time
            type: string
        - name: toDate
          required: false
          in: query
          description: End date for filtering
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_13
      tags: *ref_14
  /memos-self-service/outbox:
    get:
      operationId: MemoSelfServiceController_getOutBoxMemos
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search keyword (title, content, etc.)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by memo status
          schema:
            type: string
            enum:
              - Draft
              - Pending
              - Approved
              - Rejected
        - name: createdById
          required: false
          in: query
          description: User ID of the creator
          schema:
            type: string
        - name: fromEmployeeId
          required: false
          in: query
          description: User ID of the From
          schema:
            type: string
        - name: toEmployeeIds
          required: false
          in: query
          description: Recepients Ids
          schema:
            type: array
            items:
              type: string
        - name: throughEmployeeIds
          required: false
          in: query
          description: Through Receipients IDs
          schema:
            type: array
            items:
              type: string
        - name: fromDate
          required: false
          in: query
          description: Start date for filtering
          schema:
            format: date-time
            type: string
        - name: toDate
          required: false
          in: query
          description: End date for filtering
          schema:
            format: date-time
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_13
      tags: *ref_14
  /memos-self-service:
    post:
      operationId: MemoSelfServiceController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateMemoDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
      security: *ref_13
      summary: Create a new memo
      tags: *ref_14
  /memos-self-service/{id}:
    get:
      operationId: MemoSelfServiceController_getOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
      security: *ref_13
      summary: Get one memo record
      tags: *ref_14
    patch:
      operationId: MemoSelfServiceController_updateMemo
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateMemoDto"
      responses:
        "200":
          description: Memo updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_13
      summary: Update Memo
      tags: *ref_14
  /memos-self-service/{id}/comments:
    post:
      operationId: MemoSelfServiceController_leaveComment
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MemoCommentCreateDto"
      responses:
        "200":
          description: Comment Registered
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MemoCommentDto"
        "400":
          description: Bad Request
        "404":
          description: Record not found
        "500":
          description: Internal Server Error
      security: *ref_13
      tags: *ref_14
  /circulars:
    get:
      operationId: CircularController_getAllCirculars
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term to filter circulars by title or body
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - Draft
              - Publish
        - name: createdBy
          required: false
          in: query
          description: ID of the creator of the circular
          schema:
            type: string
        - name: from
          required: false
          in: query
          description: ID of the sender (fromEmployee)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
        - name: departmentId
          required: false
          in: query
          description: circular department ID
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_15
        - bearer: []
      summary: Get all circulars
      tags: &ref_16
        - Circular
  /circulars/{id}:
    get:
      operationId: CircularController_getOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
      security: *ref_15
      summary: Get one circular record
      tags: *ref_16
  /circular-self-service:
    post:
      operationId: CircularSelfServiceController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCircularDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
      security: &ref_17
        - bearer: []
      summary: Create a new circular
      tags: &ref_18
        - Circular
  /circular-self-service/outbox:
    get:
      operationId: CircularSelfServiceController_getOutBoxCirculars
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term to filter circulars by title or body
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - Draft
              - Publish
        - name: createdBy
          required: false
          in: query
          description: ID of the creator of the circular
          schema:
            type: string
        - name: from
          required: false
          in: query
          description: ID of the sender (fromEmployee)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
        - name: departmentId
          required: false
          in: query
          description: circular department ID
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_17
      tags: *ref_18
  /circular-self-service/sent:
    get:
      operationId: CircularSelfServiceController_getSentCirculars
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term to filter circulars by title or body
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - Draft
              - Publish
        - name: createdBy
          required: false
          in: query
          description: ID of the creator of the circular
          schema:
            type: string
        - name: from
          required: false
          in: query
          description: ID of the sender (fromEmployee)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
        - name: departmentId
          required: false
          in: query
          description: circular department ID
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_17
      tags: *ref_18
  /circular-self-service/inbox:
    get:
      operationId: CircularSelfServiceController_getInBoxCirculars
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term to filter circulars by title or body
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - Draft
              - Publish
        - name: createdBy
          required: false
          in: query
          description: ID of the creator of the circular
          schema:
            type: string
        - name: from
          required: false
          in: query
          description: ID of the sender (fromEmployee)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
        - name: departmentId
          required: false
          in: query
          description: circular department ID
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_17
      tags: *ref_18
  /circular-self-service/{id}:
    get:
      operationId: CircularSelfServiceController_getOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
      security: *ref_17
      summary: Get one circular record
      tags: *ref_18
    patch:
      operationId: CircularSelfServiceController_updateCircular
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCircularDto"
      responses:
        "200":
          description: Circular updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CircularResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_17
      tags: *ref_18
  /query-self-service/{id}:
    get:
      operationId: QuerySelfServiceController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
      security: &ref_19
        - bearer: []
      summary: Get one query record
      tags: &ref_20
        - Query Self Service
    patch:
      operationId: QuerySelfServiceController_updateQuery
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateQueryDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Update details of query
      tags: *ref_20
  /query-self-service/reply-query:
    post:
      operationId: QuerySelfServiceController_replyQuery
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateQueryAnnouncementDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryAnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Reply query/announcement
      tags: *ref_20
  /query-self-service:
    get:
      operationId: QuerySelfServiceController_myQueries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: issuedById
          required: false
          in: query
          description: Filter by Issued by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - issued
              - open
              - expired
              - closed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Get all employees queries
      tags: *ref_20
  /query-self-service/queries/issued:
    get:
      operationId: QuerySelfServiceController_issuedQueries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: issuedById
          required: false
          in: query
          description: Filter by Issued by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - issued
              - open
              - expired
              - closed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Get all employee issued
      tags: *ref_20
  /query-self-service/queries/outbox:
    get:
      operationId: QuerySelfServiceController_outboxQueries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: issuedById
          required: false
          in: query
          description: Filter by Issued by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - issued
              - open
              - expired
              - closed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Get all employee outbox queries
      tags: *ref_20
  /query-self-service/queries/inbox:
    get:
      operationId: QuerySelfServiceController_inboxQueries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: issuedById
          required: false
          in: query
          description: Filter by Issued by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - issued
              - open
              - expired
              - closed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Get all employee inbox queries
      tags: *ref_20
  /query-self-service/status/{id}:
    patch:
      operationId: QuerySelfServiceController_updateStatus
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateStatusDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_19
      summary: Update status of query
      tags: *ref_20
  /query:
    post:
      operationId: QueryController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateQueryDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_21
        - bearer: []
      summary: Create a new query
      tags: &ref_22
        - "Query - Admin "
    get:
      operationId: QueryController_getAllQueries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: issuedById
          required: false
          in: query
          description: Filter by Issued by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - issued
              - open
              - expired
              - closed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_21
      summary: Get all employees queries
      tags: *ref_22
  /query/comment:
    post:
      operationId: QueryController_leaveComment
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateQueryAnnouncementDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryAnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_21
      summary: Comment on a query
      tags: *ref_22
  /query/{id}:
    get:
      operationId: QueryController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
      security: *ref_21
      summary: Get one query record
      tags: *ref_22
    patch:
      operationId: QueryController_updateQuery
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateQueryDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_21
      summary: Update details of query
      tags: *ref_22
  /query/status/{id}:
    patch:
      operationId: QueryController_updateStatus
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateStatusDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_21
      summary: Update status of query
      tags: *ref_22
  /announcement:
    post:
      operationId: AnnouncementController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAnnouncementDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_23
        - bearer: []
      summary: Create a new announcement
      tags: &ref_24
        - Announcement - Admin
    get:
      operationId: AnnouncementController_getAllAnnouncements
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: postedById
          required: false
          in: query
          description: Filter by Posted ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - published
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_23
      summary: Get all announcement records
      tags: *ref_24
  /announcement/{id}:
    get:
      operationId: AnnouncementController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
      security: *ref_23
      summary: Get one announcement record
      tags: *ref_24
    patch:
      operationId: AnnouncementController_updateAnnouncement
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAnnouncementDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_23
      summary: Update announcement record
      tags: *ref_24
  /self-service-announcement/{id}:
    get:
      operationId: AnnouncementSelfServiceController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
      security: &ref_25
        - bearer: []
      summary: Get one announcement record
      tags: &ref_26
        - Announcement - Self-Service
  /self-service-announcement:
    get:
      operationId: AnnouncementSelfServiceController_getAllAnnouncements
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: postedById
          required: false
          in: query
          description: Filter by Posted ID (UUID)
          schema:
            type: string
        - name: draftedById
          required: false
          in: query
          description: Filter by Drafted by (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - draft
              - published
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
        - name: fromDate
          required: false
          in: query
          description: Filter by start date (createdAt)
          schema:
            type: string
        - name: toDate
          required: false
          in: query
          description: Filter by end date (createdAt)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AnnouncementResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_25
      summary: Get all employee announcement records
      tags: *ref_26
  /trainings:
    get:
      operationId: TrainingController_getTrainings
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: title
          required: false
          in: query
          schema:
            type: string
        - name: status
          required: false
          in: query
          schema:
            type: string
            enum:
              - draft
              - published
              - archived
              - deleted
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedTrainingResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_27
        - bearer: []
      tags: &ref_28
        - Trainings
    post:
      operationId: TrainingController_createTraining
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTrainingDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_27
      summary: Create a new training
      tags: *ref_28
  /trainings/{id}:
    patch:
      operationId: TrainingController_updateTraining
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateTrainingDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Training not found
        "500":
          description: Internal Server Error
      security: *ref_27
      summary: Update Training
      tags: *ref_28
    get:
      operationId: TrainingController_getOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
      security: *ref_27
      summary: Get one training
      tags: *ref_28
  /trainings/{id}/add-modules:
    post:
      operationId: TrainingController_addTrainingModules
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/TrainingModuleResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_27
      summary: Add Training Modules
      tags: *ref_28
  /trainings/{moduleId}/modules:
    patch:
      operationId: TrainingController_updateTrainingModule
      parameters:
        - name: moduleId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateTrainingModuleDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingModuleResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Module not found
        "500":
          description: Internal Server Error
      security: *ref_27
      summary: Update Training Module
      tags: *ref_28
  /trainings-self-service/my-trainings:
    get:
      operationId: TrainingSelfServiceController_getEnrolledTrainings
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          schema:
            type: string
        - name: trainingId
          required: false
          in: query
          schema:
            type: string
        - name: status
          required: false
          in: query
          schema:
            type: string
            enum:
              - enrolled
              - ongoing
              - completed
        - name: feedback
          required: false
          in: query
          schema:
            type: string
        - name: rating
          required: false
          in: query
          schema:
            type: number
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedEmployeeTrainingResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_29
        - bearer: []
      tags: &ref_30
        - Training Self Service
  /trainings-self-service/trainings:
    get:
      operationId: TrainingSelfServiceController_getAllTrainings
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          schema:
            type: string
        - name: trainingId
          required: false
          in: query
          schema:
            type: string
        - name: status
          required: false
          in: query
          schema:
            type: string
            enum:
              - enrolled
              - ongoing
              - completed
        - name: feedback
          required: false
          in: query
          schema:
            type: string
        - name: rating
          required: false
          in: query
          schema:
            type: number
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedEmployeeTrainingResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_29
      tags: *ref_30
  /trainings-self-service/{id}:
    get:
      operationId: TrainingSelfServiceController_getOne
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
      security: *ref_29
      summary: Get one training
      tags: *ref_30
  /trainings-self-service/{id}/enroll:
    post:
      operationId: TrainingSelfServiceController_enrollInTraining
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Training enrolled successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeTrainingResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Training not found
        "500":
          description: Internal Server Error
      security: *ref_29
      tags: *ref_30
  /trainings-self-service/{id}/complete:
    patch:
      operationId: TrainingSelfServiceController_markTrainingAsCompleted
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Training marked as completed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Training not found
        "500":
          description: Internal Server Error
      security: *ref_29
      summary: Mark Training as Completed
      tags: *ref_30
  /trainings-self-service/{id}/on-going:
    patch:
      operationId: TrainingSelfServiceController_markTrainingAsOngoing
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Training marked as ongoing
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TrainingResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Training not found
        "500":
          description: Internal Server Error
      security: *ref_29
      summary: Mark Training as Ongoing
      tags: *ref_30
  /trainings-self-service/{id}/feedback:
    post:
      operationId: TrainingSelfServiceController_leaveFeedBack
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateFeedbackEmployeeTrainingDto"
      responses:
        "200":
          description: FeedBack Registered
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeTrainingResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Record not found
        "500":
          description: Internal Server Error
      security: *ref_29
      tags: *ref_30
  /g-drive/files/root:
    get:
      operationId: GDriveFileController_getFiles
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: folderId
          required: false
          in: query
          schema:
            type: string
        - name: search
          required: false
          in: query
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveFileResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_31
        - bearer: []
      tags: &ref_32
        - GDriveFile
  /g-drive/files/{folderId}:
    get:
      operationId: GDriveFileController_getFilesFromFolder
      parameters:
        - name: folderId
          required: true
          in: path
          schema:
            type: string
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveFileResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_31
      tags: *ref_32
  /g-drive/files:
    post:
      operationId: GDriveFileController_uploadFile
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateGDriveFileDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GDriveFileResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_31
      tags: *ref_32
  /g-drive/shares/incoming:
    get:
      operationId: GDriveShareController_getIncomingShares
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: sharedItemType
          required: false
          in: query
          schema:
            type: string
            enum:
              - file
              - folder
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveShareResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_33
        - bearer: []
      tags: &ref_34
        - GDriveShare
  /g-drive/shares/outgoing:
    get:
      operationId: GDriveShareController_getOutgoingShares
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: sharedItemType
          required: false
          in: query
          schema:
            type: string
            enum:
              - file
              - folder
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveShareResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_33
      tags: *ref_34
  /g-drive/shares:
    post:
      operationId: GDriveShareController_shareFileOrFolder
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateGDriveShareDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GDriveShareResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_33
      tags: *ref_34
  /g-drive/shares/{id}:
    delete:
      operationId: GDriveShareController_revokeShare
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: Share revoked
        "500":
          description: Internal Server Error
      security: *ref_33
      tags: *ref_34
  /g-drive/folders/root:
    get:
      operationId: GDriveFolderController_getRootFolders
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term for folder names
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveFolderResponseDto"
        "500":
          description: Internal Server Error
      security: &ref_35
        - bearer: []
      tags: &ref_36
        - GDriveFolder
  /g-drive/folders/subfolders:
    get:
      operationId: GDriveFolderController_getSubFolders
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: search
          required: false
          in: query
          description: Search term for folder names
          schema:
            type: string
        - name: parentId
          required: false
          in: query
          description: Parent folder ID to filter subfolders
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedGDriveFolderResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_35
      tags: *ref_36
  /g-drive/folders:
    post:
      operationId: GDriveFolderController_createFolder
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateGDriveFolderDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GDriveFolderResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_35
      tags: *ref_36
  /g-drive/folders/{id}:
    patch:
      operationId: GDriveFolderController_updateFolder
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateGDriveFolderDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GDriveFolderResponseDto"
        "500":
          description: Internal Server Error
      security: *ref_35
      tags: *ref_36
  /registries:
    post:
      operationId: RegistryController_createRegistry
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateRegistryDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistryResponseDto"
      security: &ref_37
        - bearer: []
      tags: &ref_38
        - Registry
    get:
      operationId: RegistryController_getRegistries
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: ownerId
          required: false
          in: query
          schema:
            type: string
        - name: search
          required: false
          in: query
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedRegistryResponseDto"
      security: *ref_37
      tags: *ref_38
  /registries/{id}:
    get:
      operationId: RegistryController_getRegistry
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistryResponseDto"
      security: *ref_37
      tags: *ref_38
    patch:
      operationId: RegistryController_updateRegistry
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateRegistryDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistryResponseDto"
      security: *ref_37
      tags: *ref_38
    delete:
      operationId: RegistryController_deleteRegistry
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_37
      tags: *ref_38
  /registries/{registryId}/collaborators:
    post:
      operationId: RegistryController_addCollaborator
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCollaboratorDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistryCollaboratorResponseDto"
      security: *ref_37
      tags: *ref_38
    get:
      operationId: RegistryController_getCollaborators
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PaginatedRegistryCollaboratorResponseDto"
      security: *ref_37
      tags: *ref_38
  /registries/{registryId}/collaborators/batch:
    post:
      operationId: RegistryController_addCollaborators
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCollaboratorsDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RegistryCollaboratorResponseDto"
      security: *ref_37
      tags: *ref_38
  /registries/{registryId}/collaborators/{employeeId}:
    delete:
      operationId: RegistryController_removeCollaborator
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
        - name: employeeId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_37
      tags: *ref_38
  /registries/{registryId}/collaborators/delete-batch:
    post:
      operationId: RegistryController_removeCollaborators
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCollaboratorsDto"
      responses:
        "201":
          description: ""
      security: *ref_37
      tags: *ref_38
  /registries-self-service/my-registries:
    get:
      operationId: RegistrySelfServiceController_getMyRegistries
      parameters: []
      responses:
        "200":
          description: ""
      security: &ref_39
        - bearer: []
      tags: &ref_40
        - RegistrySelfService
  /registries-self-service/correspondences:
    post:
      operationId: RegistrySelfServiceController_logCorrespondence
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCorrespondenceDto"
      responses:
        "201":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/correspondences/{id}:
    get:
      operationId: RegistrySelfServiceController_getCorrespondence
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
    patch:
      operationId: RegistrySelfServiceController_updateCorrespondence
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCorrespondenceDto"
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/{registryId}/correspondences:
    get:
      operationId: RegistrySelfServiceController_getCorrespondences
      parameters:
        - name: registryId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/{correspondenceId}/employee-correspondences:
    get:
      operationId: RegistrySelfServiceController_getEmployeeCorrespondences
      parameters:
        - name: correspondenceId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/{correspondenceId}/forward:
    post:
      operationId: RegistrySelfServiceController_forwardCorrespondence
      parameters:
        - name: correspondenceId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ForwardCorrespondenceDto"
      responses:
        "201":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/inbox:
    get:
      operationId: RegistrySelfServiceController_getInbox
      parameters: []
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/inbox/{employeeCorrespondenceId}/receive:
    patch:
      operationId: RegistrySelfServiceController_markReceived
      parameters:
        - name: employeeCorrespondenceId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /registries-self-service/inbox/{employeeCorrespondenceId}/file:
    patch:
      operationId: RegistrySelfServiceController_markFiled
      parameters:
        - name: employeeCorrespondenceId
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
      security: *ref_39
      tags: *ref_40
  /workflow-types:
    post:
      operationId: WorkflowTypeController_createWorkflowType
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createWorkflowTypeDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowTypeResponseDto"
      security: &ref_41
        - bearer: []
      summary: Create a new workflow type
      tags: &ref_42
        - Workflow - Admin
    get:
      operationId: WorkflowTypeController_getAllWorkflowTypes
      parameters: []
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/WorkflowTypeResponseDto"
      security: *ref_41
      summary: Get all workflow types
      tags: *ref_42
  /workflow-types/{id}:
    get:
      operationId: WorkflowTypeController_getWorkflowType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowTypeResponseDto"
      security: *ref_41
      summary: Get a single workflow type by ID
      tags: *ref_42
    patch:
      operationId: WorkflowTypeController_updateWorkflowType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateWorkflowTypeDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowTypeResponseDto"
      security: *ref_41
      summary: Update a workflow type by ID
      tags: *ref_42
    delete:
      operationId: WorkflowTypeController_deleteWorkflowType
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "204":
          description: ""
      security: *ref_41
      summary: Delete a workflow type by ID
      tags: *ref_42
  /workflows:
    post:
      operationId: WorkflowController_create
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_43
        - bearer: []
      summary: Create workflow request
      tags: &ref_44
        - Workflow - Admin
  /workflows/{id}:
    patch:
      operationId: WorkflowController_updateRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkflowDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_43
      summary: Update workflow request
      tags: *ref_44
    get:
      operationId: WorkflowController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
      security: *ref_43
      summary: Get workflow record by ID
      tags: *ref_44
  /workflows/employee/authorizing:
    get:
      operationId: WorkflowController_getAllAuthorizingRequests
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: initiatedBy
          required: false
          in: query
          description: Filter by Initiated by (UUID)
          schema:
            type: string
        - name: workflowTypeId
          required: false
          in: query
          description: Filter by Workflow type (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - New
              - Ongoing
              - Completed
              - Declined
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_43
      summary: Get all workflow requests where user is authorizing
      tags: *ref_44
  /workflow-self-service/authorize:
    post:
      operationId: WorkflowSelfServiceController_authorizeRequest
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AuthorizeRequestDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_45
        - bearer: []
      summary: Authorize request
      tags: &ref_46
        - Workflow - Self-service
  /workflow-self-service/comment:
    post:
      operationId: WorkflowSelfServiceController_leaveComment
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/WorkflowCommentDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_45
      summary: Comment on workflow
      tags: *ref_46
  /workflow-self-service:
    get:
      operationId: WorkflowSelfServiceController_getAllWorkflowRequests
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: employeeId
          required: false
          in: query
          description: Filter by employee ID (UUID)
          schema:
            type: string
        - name: initiatedBy
          required: false
          in: query
          description: Filter by Initiated by (UUID)
          schema:
            type: string
        - name: workflowTypeId
          required: false
          in: query
          description: Filter by Workflow type (UUID)
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - New
              - Ongoing
              - Completed
              - Declined
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkflowResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_45
      summary: Get all workflow requests
      tags: *ref_46
  /workgroup-self-service:
    post:
      operationId: WorkgroupSelfServiceController_createWorkgroup
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkgroupDto"
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: &ref_47
        - bearer: []
      summary: Initiate a workgroup task(New task)
      tags: &ref_48
        - Workgroup - Self-service
    get:
      operationId: WorkgroupSelfServiceController_getAllWorkgroupRequests
      parameters:
        - name: skip
          required: false
          in: query
          description: Number of item to skip.
          schema:
            minimum: 0
            type: number
        - name: limit
          required: false
          in: query
          description: Maximum number of items to return. Defaults to `10` if not provided.
          schema:
            minimum: 1
            type: number
        - name: initiatedById
          required: false
          in: query
          description: Filter by initiated by ID (UUID)
          schema:
            type: string
        - name: executorId
          required: false
          in: query
          description: Filter by executor ID (UUID)
          schema:
            type: string
        - name: title
          required: false
          in: query
          description: Filter by Title
          schema:
            type: string
        - name: description
          required: false
          in: query
          description: Filter by description
          schema:
            type: string
        - name: dueDate
          required: false
          in: query
          description: Filter by dueDate
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: Filter by status
          schema:
            type: string
            enum:
              - New
              - Ongoing
              - Completed
        - name: search
          required: false
          in: query
          description: Search keyword (optional)
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Get all workgroup requests
      tags: *ref_48
  /workgroup-self-service/{id}:
    patch:
      operationId: WorkgroupSelfServiceController_updateRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkgroupDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Update workgroup request
      tags: *ref_48
    get:
      operationId: WorkgroupSelfServiceController_getRecord
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        "200":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
      security: *ref_47
      summary: Get workgroup record by ID
      tags: *ref_48
  /workgroup-self-service/comment:
    post:
      operationId: WorkgroupSelfServiceController_leaveComment
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/WorkgroupCommentCreateDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Comment on workgroup
      tags: *ref_48
  /workgroup-self-service/add-executor:
    post:
      operationId: WorkgroupSelfServiceController_addExecutor
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddWorkgroupExecutorDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Add executor to workgroup
      tags: *ref_48
  /workgroup-self-service/remove-executor:
    post:
      operationId: WorkgroupSelfServiceController_removeExecutor
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddWorkgroupExecutorDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Remove executor to workgroup
      tags: *ref_48
  /workgroup-self-service/remove-document:
    post:
      operationId: WorkgroupSelfServiceController_removeDocument
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RemoveWorkgroupDocsDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Remove document in a workgroup
      tags: *ref_48
  /workgroup-self-service/add-document:
    post:
      operationId: WorkgroupSelfServiceController_addDocument
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddWorkgroupDocsDto"
      responses:
        "201":
          description: ""
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/WorkgroupResponseDto"
        "400":
          description: Bad Request
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      security: *ref_47
      summary: Add document in a workgroup
      tags: *ref_48
info:
  title: IGOV API
  description: The IGOV API documentation
  version: "1.0"
  contact:
    name: igov
    url: https://support.igov.com/
    email: <EMAIL>
  license:
    name: igov
    url: https://igov.com/
tags: []
servers:
  - url: https://staging.cnxigov.com/api
    description: DEV
    variables: {}
  - url: http://localhost:3001/api
    description: localhost
    variables: {}
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      type: http
  schemas:
    RegisterDto:
      type: object
      properties:
        email:
          type: string
        password:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        role:
          type: string
          enum:
            - admin
            - employee
            - moderator
      required:
        - email
        - password
        - firstName
        - lastName
    RegisterResponseDto:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        token:
          type: string
        role:
          type: string
          enum:
            - admin
            - employee
            - moderator
      required:
        - id
        - email
        - firstName
        - lastName
        - createdAt
        - updatedAt
        - token
        - role
    LoginDto:
      type: object
      properties:
        email:
          type: string
        password:
          type: string
      required:
        - email
        - password
    LoginResponseDto:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        token:
          type: string
        role:
          type: string
          enum:
            - admin
            - employee
            - moderator
      required:
        - id
        - email
        - createdAt
        - updatedAt
        - token
        - role
    ChangePasswordDto:
      type: object
      properties:
        userId:
          type: string
          description: Existing User ID
        currentPassword:
          type: string
          description: Current password
        newPassword:
          type: string
          description: Choose new password
        confirmPassword:
          type: string
          description: Confirm new password
      required:
        - userId
        - currentPassword
        - newPassword
        - confirmPassword
    UserResponseDto:
      type: object
      properties:
        id:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        role:
          type: string
          enum:
            - admin
            - employee
            - moderator
      required:
        - id
        - firstName
        - lastName
        - email
        - createdAt
        - updatedAt
        - role
    PaginatedUserResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/UserResponseDto"
        total:
          type: number
          description: Total number of users
      required:
        - data
        - total
    CreateDepartmentDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        commRef:
          type: string
      required:
        - code
        - name
    DepartmentResponseDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        commRef:
          type: string
        id:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - code
        - name
        - id
    UpdateDepartmentDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        commRef:
          type: string
    CreateMdaDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
      required:
        - code
        - name
    MdaResponseDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        id:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - code
        - name
        - id
    UpdateMdaDto:
      type: object
      properties:
        code:
          type: string
        name:
          type: string
    CreateUnitDto:
      type: object
      properties:
        departmentCode:
          type: string
        name:
          type: string
        commRef:
          type: string
        departmentId:
          type: string
      required:
        - departmentCode
        - name
        - departmentId
    UnitResponseDto:
      type: object
      properties:
        departmentCode:
          type: string
        name:
          type: string
        commRef:
          type: string
        departmentId:
          type: string
        id:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - departmentCode
        - name
        - departmentId
        - id
    UpdateUnitDto:
      type: object
      properties:
        departmentCode:
          type: string
        name:
          type: string
        commRef:
          type: string
        departmentId:
          type: string
    CreatePositionDto:
      type: object
      properties:
        name:
          type: string
      required:
        - name
    PositionResponseDto:
      type: object
      properties:
        name:
          type: string
        id:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - name
        - id
    UpdatePositionDto:
      type: object
      properties:
        name:
          type: string
    CreateOrganizationProfileDto:
      type: object
      properties:
        name:
          type: string
        address:
          type: string
        phoneNumber:
          type: string
        email:
          type: string
        website:
          type: string
        logoUrl:
          type: string
          description: URL to logo file
      required:
        - name
    OrganizationProfileResponseDto:
      type: object
      properties:
        name:
          type: string
        address:
          type: string
        phoneNumber:
          type: string
        email:
          type: string
        website:
          type: string
        logoUrl:
          type: string
          description: URL to logo file
        id:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - name
        - id
    UpdateOrganizationProfileDto:
      type: object
      properties:
        name:
          type: string
        address:
          type: string
        phoneNumber:
          type: string
        email:
          type: string
        website:
          type: string
        logoUrl:
          type: string
          description: URL to logo file
    CreateLeaveTypeDto:
      type: object
      properties:
        name:
          type: string
        accrualRate:
          type: number
        mode:
          type: string
          enum:
            - Monthly
            - Yearly
        max:
          type: number
      required:
        - name
        - accrualRate
        - mode
        - max
    LeaveTypeResponseDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        accrualRate:
          type: number
        mode:
          type: string
          enum:
            - Monthly
            - Yearly
        max:
          type: number
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - name
        - accrualRate
        - mode
        - max
        - createdAt
        - updatedAt
    UpdateLeaveTypeDto:
      type: object
      properties:
        name:
          type: string
        accrualRate:
          type: number
        mode:
          type: string
          enum:
            - Monthly
            - Yearly
        max:
          type: number
    CreatePaymentDefinitionDto:
      type: object
      properties:
        payCode:
          type: string
        payName:
          type: string
        payMode:
          type: string
          enum:
            - Standard
            - Variation
        payType:
          type: string
          enum:
            - Income
            - Deduction
        value:
          type: string
          enum:
            - Flat
            - Computational
        percentageValue:
          type: number
      required:
        - payCode
        - payName
        - payMode
        - payType
        - value
        - percentageValue
    PaymentDefinitionResponseDto:
      type: object
      properties:
        id:
          type: string
        payCode:
          type: string
        payName:
          type: string
        payMode:
          type: string
          enum:
            - Standard
            - Variation
        payType:
          type: string
          enum:
            - Income
            - Deduction
        value:
          type: string
          enum:
            - Flat
            - Computational
        percentageValue:
          type: number
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - payCode
        - payName
        - payMode
        - payType
        - value
        - percentageValue
        - createdAt
        - updatedAt
    UpdatePaymentDefinitionDto:
      type: object
      properties:
        payCode:
          type: string
        payName:
          type: string
        payMode:
          type: string
          enum:
            - Standard
            - Variation
        payType:
          type: string
          enum:
            - Income
            - Deduction
        value:
          type: string
          enum:
            - Flat
            - Computational
        percentageValue:
          type: number
    CreateTaxRateDto:
      type: object
      properties:
        band:
          type: string
        rate:
          type: number
      required:
        - band
        - rate
    TaxRateResponseDto:
      type: object
      properties:
        id:
          type: string
        band:
          type: string
        rate:
          type: number
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - band
        - rate
        - createdAt
        - updatedAt
    UpdateTaxRateDto:
      type: object
      properties:
        band:
          type: string
        rate:
          type: number
    NextOfKinDto:
      type: object
      properties:
        name:
          type: string
          description: Name of the next of kin
        address:
          type: string
          description: Address of the next of kin
        mobileNo:
          type: string
          description: Mobile number of the next of kin
    AddressDto:
      type: object
      properties:
        contact:
          type: string
          description: Contact address of the employee
        postal:
          type: string
          description: Postal address of the employee
    CreateEmployeeDto:
      type: object
      properties:
        userId:
          type: string
          description: User ID associated with the employee (optional)
        title:
          type: string
          description: Employee title (e.g., Mr, Mrs, Dr)
        firstName:
          type: string
          description: First name of the employee
        lastName:
          type: string
          description: Last name of the employee
        otherNames:
          type: string
          description: Other names of the employee (if any)
        gender:
          type: string
          enum: &ref_49
            - Male
            - Female
          description: Gender of the employee
        email:
          type: string
          description: Email address of the employee
        mobileNumber:
          type: string
          description: Mobile phone number of the employee
        bloodGroup:
          type: string
          enum: &ref_50
            - A+
            - A-
            - B+
            - B-
            - AB+
            - AB-
            - O+
            - O-
          description: Blood group of the employee
        stateOfOrigin:
          type: string
          enum: &ref_51
            - Abia
            - Adamawa
            - Akwa Ibom
            - Anambra
            - Bauchi
            - Bayelsa
            - Benue
            - Borno
            - Cross River
            - Delta
            - Ebonyi
            - Edo
            - Ekiti
            - Enugu
            - Gombe
            - Imo
            - Jigawa
            - Kaduna
            - Kano
            - Katsina
            - Kebbi
            - Kogi
            - Kwara
            - Lagos
            - Nasarawa
            - Niger
            - Ogun
            - Ondo
            - Osun
            - Oyo
            - Plateau
            - Rivers
            - Sokoto
            - Taraba
            - Yobe
            - Zamfara
            - Federal Capital Territory
          description: State of origin of the employee
        photoUrl:
          type: string
          description: URL to the employee photo
        birthDate:
          format: date-time
          type: string
          description: Birth date of the employee (ISO string)
        unitId:
          type: string
          description: ID of the unit where the employee belongs
        gradeLevel:
          type: string
          enum: &ref_52
            - Level 1
            - Level 2
            - Level 3
            - Level 4
            - Level 5
            - Level 6
            - Level 7
            - Level 8
            - Level 9
            - Level 10
            - Level 11
            - Level 12
            - Level 13
            - Level 14
            - Level 15
            - Level 16
            - Level 17
          description: Grade level of the employee
        firstAppointmentDate:
          format: date-time
          type: string
          description: First appointment date (ISO string)
        presentAppointmentDate:
          format: date-time
          type: string
          description: Present appointment date (ISO string)
        presentPosting:
          type: string
          description: Present posting location of the employee
        departmentId:
          type: string
          description: ID of the department where the employee belongs (optional)
        nextOfKin:
          description: Next of kin information
          allOf:
            - $ref: "#/components/schemas/NextOfKinDto"
        address:
          description: Address details of the employee
          allOf:
            - $ref: "#/components/schemas/AddressDto"
      required:
        - firstName
        - lastName
        - gender
        - email
        - unitId
    EmployeeResponseDto:
      type: object
      properties:
        userId:
          type: string
          description: User ID associated with the employee (optional)
        title:
          type: string
          description: Employee title (e.g., Mr, Mrs, Dr)
        firstName:
          type: string
          description: First name of the employee
        lastName:
          type: string
          description: Last name of the employee
        otherNames:
          type: string
          description: Other names of the employee (if any)
        gender:
          type: string
          enum:
            - Male
            - Female
          description: Gender of the employee
        email:
          type: string
          description: Email address of the employee
        mobileNumber:
          type: string
          description: Mobile phone number of the employee
        bloodGroup:
          type: string
          enum:
            - A+
            - A-
            - B+
            - B-
            - AB+
            - AB-
            - O+
            - O-
          description: Blood group of the employee
        stateOfOrigin:
          type: string
          enum:
            - Abia
            - Adamawa
            - Akwa Ibom
            - Anambra
            - Bauchi
            - Bayelsa
            - Benue
            - Borno
            - Cross River
            - Delta
            - Ebonyi
            - Edo
            - Ekiti
            - Enugu
            - Gombe
            - Imo
            - Jigawa
            - Kaduna
            - Kano
            - Katsina
            - Kebbi
            - Kogi
            - Kwara
            - Lagos
            - Nasarawa
            - Niger
            - Ogun
            - Ondo
            - Osun
            - Oyo
            - Plateau
            - Rivers
            - Sokoto
            - Taraba
            - Yobe
            - Zamfara
            - Federal Capital Territory
          description: State of origin of the employee
        photoUrl:
          type: string
          description: URL to the employee photo
        birthDate:
          format: date-time
          type: string
          description: Birth date of the employee (ISO string)
        unitId:
          type: string
          description: ID of the unit where the employee belongs
        gradeLevel:
          type: string
          enum:
            - Level 1
            - Level 2
            - Level 3
            - Level 4
            - Level 5
            - Level 6
            - Level 7
            - Level 8
            - Level 9
            - Level 10
            - Level 11
            - Level 12
            - Level 13
            - Level 14
            - Level 15
            - Level 16
            - Level 17
          description: Grade level of the employee
        firstAppointmentDate:
          format: date-time
          type: string
          description: First appointment date (ISO string)
        presentAppointmentDate:
          format: date-time
          type: string
          description: Present appointment date (ISO string)
        presentPosting:
          type: string
          description: Present posting location of the employee
        departmentId:
          type: string
          description: ID of the department where the employee belongs (optional)
        nextOfKin:
          description: Next of kin information
          allOf:
            - $ref: "#/components/schemas/NextOfKinDto"
        address:
          description: Address details of the employee
          allOf:
            - $ref: "#/components/schemas/AddressDto"
        eToken:
          type: string
          description: eToken of the employee (if any)
        eSignature:
          type: string
          description: eSignature URL of the employee (if any)
        createdAt:
          format: date-time
          type: string
          description: Date when the employee record was created
        updatedAt:
          format: date-time
          type: string
          description: Date when the employee record was last updated
      required:
        - firstName
        - lastName
        - gender
        - email
        - unitId
        - createdAt
        - updatedAt
    UpdateEmployeeDto:
      type: object
      properties:
        userId:
          type: string
          description: User ID associated with the employee (optional)
        title:
          type: string
          description: Employee title (e.g., Mr, Mrs, Dr)
        firstName:
          type: string
          description: First name of the employee
        lastName:
          type: string
          description: Last name of the employee
        otherNames:
          type: string
          description: Other names of the employee (if any)
        gender:
          type: string
          enum: *ref_49
          description: Gender of the employee
        email:
          type: string
          description: Email address of the employee
        mobileNumber:
          type: string
          description: Mobile phone number of the employee
        bloodGroup:
          type: string
          enum: *ref_50
          description: Blood group of the employee
        stateOfOrigin:
          type: string
          enum: *ref_51
          description: State of origin of the employee
        photoUrl:
          type: string
          description: URL to the employee photo
        birthDate:
          format: date-time
          type: string
          description: Birth date of the employee (ISO string)
        unitId:
          type: string
          description: ID of the unit where the employee belongs
        gradeLevel:
          type: string
          enum: *ref_52
          description: Grade level of the employee
        firstAppointmentDate:
          format: date-time
          type: string
          description: First appointment date (ISO string)
        presentAppointmentDate:
          format: date-time
          type: string
          description: Present appointment date (ISO string)
        presentPosting:
          type: string
          description: Present posting location of the employee
        departmentId:
          type: string
          description: ID of the department where the employee belongs (optional)
        nextOfKin:
          description: Next of kin information
          allOf:
            - $ref: "#/components/schemas/NextOfKinDto"
        address:
          description: Address details of the employee
          allOf:
            - $ref: "#/components/schemas/AddressDto"
    PaginatedEmployeeResponseDto:
      type: object
      properties:
        employees:
          description: List of employees
          type: array
          items:
            $ref: "#/components/schemas/EmployeeResponseDto"
        total:
          type: number
          description: Total number of employees
      required:
        - employees
        - total
    ETokenDto:
      type: object
      properties:
        userId:
          type: string
          description: Existing User ID
        token:
          type: string
          description: User security token
        passwordConfirmation:
          type: string
          description: Password Confirmation
      required:
        - userId
        - token
        - passwordConfirmation
    ESignatureDto:
      type: object
      properties:
        userId:
          type: string
          description: Existing User ID
        signatureUrl:
          type: string
          description: Signature URL is required
        passwordConfirmation:
          type: string
          description: Password Confirmation
      required:
        - userId
        - signatureUrl
        - passwordConfirmation
    SelfServiceETokenDto:
      type: object
      properties:
        token:
          type: string
          description: User security token
        passwordConfirmation:
          type: string
          description: Password Confirmation
      required:
        - token
        - passwordConfirmation
    SelfServiceESignatureDto:
      type: object
      properties:
        signatureUrl:
          type: string
          description: Signature URL is required
        passwordConfirmation:
          type: string
          description: Password Confirmation
      required:
        - signatureUrl
        - passwordConfirmation
    ChatMessageResponseDto:
      type: object
      properties:
        id:
          type: string
        senderId:
          type: string
        receiverId:
          type: string
        content:
          type: string
        deliveredAt:
          format: date-time
          type: string
        readAt:
          format: date-time
          type: string
        isRead:
          type: boolean
        isEdited:
          type: boolean
        createdAt:
          type: string
        updatedAt:
          type: string
      required:
        - id
        - senderId
        - receiverId
        - content
        - deliveredAt
        - readAt
        - isRead
        - isEdited
    MemoCommentDto:
      type: object
      properties:
        id:
          type: string
        comment:
          type: string
        commenter:
          description: Commenter Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
      required:
        - id
        - comment
        - commenter
        - createdAt
    MemoHistoryDto:
      type: object
      properties:
        id:
          type: string
        action:
          type: string
          enum:
            - created
            - edited
            - approved
            - rejected
            - commented
        memoId:
          type: string
        snapshot:
          type: object
        performedBy:
          type: string
        employee:
          description: From Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
      required:
        - id
        - action
        - memoId
        - snapshot
        - performedBy
        - employee
        - createdAt
    MemoResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        body:
          type: string
        attachments:
          type: array
          items:
            type: string
        status:
          type: string
          enum:
            - Draft
            - Pending
            - Approved
            - Rejected
          description: Staus of Memo
        createdAt:
          format: date-time
          type: string
        createdByEmployee:
          description: CreatedBy Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        fromEmployee:
          description: From Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        toEmployees:
          description: To Employees
          type: array
          items:
            $ref: "#/components/schemas/EmployeeResponseDto"
        throughEmployees:
          description: Through Employees
          type: array
          items:
            $ref: "#/components/schemas/EmployeeResponseDto"
        comments:
          description: Comments on Memo
          type: array
          items:
            $ref: "#/components/schemas/MemoCommentDto"
        history:
          description: History of Memo
          type: array
          items:
            $ref: "#/components/schemas/MemoHistoryDto"
      required:
        - id
        - title
        - body
        - attachments
        - status
        - createdAt
        - createdByEmployee
        - fromEmployee
        - toEmployees
        - throughEmployees
        - comments
        - history
    CreateMemoDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the memo
        body:
          type: string
          description: Body/content of the memo
        reference:
          type: string
          description: Memo reference number
        attachments:
          description: Memo attachments
          type: array
          items:
            type: string
        createdBy:
          type: string
          description: ID of the sender (from user)
        fromEmployeeId:
          type: string
          description: ID of the from sender (from user)
        toEmployees:
          description: IDs of the users the memo is addressed to
          type: array
          items:
            type: string
        throughEmployees:
          description: IDs of the users the memo passes through
          type: array
          items:
            type: string
      required:
        - title
        - body
        - reference
        - attachments
        - createdBy
        - fromEmployeeId
        - toEmployees
    UpdateMemoDto:
      type: object
      properties:
        title:
          type: string
          description: Updated title of the memo
        body:
          type: string
          description: Updated body/content of the memo
        status:
          type: string
          enum:
            - Draft
            - Pending
            - Approved
            - Rejected
          description: Staus of Memo
      required:
        - title
        - body
        - status
    MemoCommentCreateDto:
      type: object
      properties:
        comment:
          type: string
          description: Comment body
      required:
        - comment
    AttachmentDto:
      type: object
      properties:
        name:
          type: string
          description: Actual file name
        url:
          type: string
          description: File url
        type:
          type: string
          description: File type
      required:
        - name
        - url
        - type
    CircularHistoryDto:
      type: object
      properties:
        id:
          type: string
        action:
          type: string
          enum:
            - created
            - edited
            - approved
            - rejected
            - commented
        circularId:
          type: string
        snapshot:
          type: object
        performedBy:
          type: string
        employee:
          description: From Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
      required:
        - id
        - action
        - circularId
        - snapshot
        - performedBy
        - employee
        - createdAt
    CircularResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        body:
          type: string
        reference:
          type: string
        status:
          type: object
        attachments:
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        createdBy:
          type: string
        from:
          type: string
        createdByEmployee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        fromEmployee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        circularDepartment:
          type: array
          items:
            $ref: "#/components/schemas/DepartmentResponseDto"
        history:
          type: array
          items:
            $ref: "#/components/schemas/CircularHistoryDto"
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - title
        - body
        - reference
        - status
        - createdBy
        - from
        - createdByEmployee
        - fromEmployee
        - circularDepartment
        - createdAt
        - updatedAt
    CreateCircularDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the circular
        body:
          type: string
          description: Body/content of the circular
        status:
          type: string
          enum:
            - Draft
            - Publish
          description: Staus of Circular
        reference:
          type: string
          description: Circular reference number
        attachments:
          description: Circular attachments
          type: array
          items:
            type: string
        createdBy:
          type: string
          description: ID of the employee creating the circular
        from:
          type: string
          description: ID of the employee sending the circular
        circularDepartment:
          description: List of department IDs the circular is addressed to
          type: array
          items:
            type: string
      required:
        - title
        - body
        - status
        - reference
        - attachments
        - createdBy
        - from
        - circularDepartment
    UpdateCircularDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the circular
        body:
          type: string
          description: Body/content of the circular
        status:
          type: string
          enum:
            - Draft
            - Publish
          description: Staus of Circular
      required:
        - status
    QueryHistoryDto:
      type: object
      properties:
        id:
          type: string
        queryId:
          type: string
        performedBy:
          type: string
        action:
          type: string
        snapshot:
          type: object
        createdAt:
          format: date-time
          type: string
      required:
        - id
        - queryId
        - performedBy
        - action
        - snapshot
        - createdAt
    QueryResponseDto:
      type: object
      properties:
        id:
          type: string
        reference:
          type: string
        title:
          type: string
        message:
          type: string
        attachments:
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        employeeId:
          type: string
        draftedBy:
          type: string
        issuedBy:
          type: string
        status:
          type: string
          enum:
            - draft
            - issued
            - open
            - expired
            - closed
        dueDate:
          type: string
          format: date-time
        employee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        draftedByEmployee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        issuedByEmployee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        history:
          type: array
          items:
            $ref: "#/components/schemas/QueryHistoryDto"
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - reference
        - title
        - message
        - employeeId
        - draftedBy
        - issuedBy
        - status
        - employee
        - draftedByEmployee
        - issuedByEmployee
        - createdAt
        - updatedAt
    CreateQueryAnnouncementDto:
      type: object
      properties:
        comment:
          type: string
          description: Comment
        attachment:
          description: Query attachments (optional)
          allOf:
            - $ref: "#/components/schemas/AttachmentDto"
        queryId:
          type: string
          description: ID of the query
      required:
        - comment
        - queryId
    QueryAnnouncementResponseDto:
      type: object
      properties:
        comment:
          type: string
          description: Comment
        attachment:
          description: Query attachments (optional)
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        employeeId:
          type: string
          description: ID of the employee
      required:
        - comment
        - employeeId
    UpdateQueryDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the query
        message:
          type: string
          description: Message or content of the query
        status:
          type: string
          description: Status of the query
          enum:
            - draft
            - issued
            - open
            - expired
            - closed
    UpdateStatusDto:
      type: object
      properties:
        status:
          type: string
          description: Status of the query
          enum:
            - draft
            - issued
            - open
            - expired
            - closed
    CreateQueryDto:
      type: object
      properties:
        reference:
          type: string
          description: Query reference number
        title:
          type: string
          description: Title of the query
        message:
          type: string
          description: Message body/content of the query
        attachments:
          description: Query attachments (optional)
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        employeeId:
          type: string
          description: ID of the employee the query is addressed to
        issuedBy:
          type: string
          description: ID of the user who issued the query
        dueDate:
          type: string
          description: Due date for responding to the query (optional)
      required:
        - reference
        - title
        - message
        - employeeId
        - issuedBy
    CreateAnnouncementDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the announcement
        message:
          type: string
          description: Message body/content of the announcement
        attachments:
          description: Announcement attachments (optional)
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        postedById:
          type: string
          description: ID of the employee that posted the announcement
      required:
        - title
        - message
        - postedById
    AnnouncementResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        message:
          type: string
        attachments:
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        postedById:
          type: string
        draftedById:
          type: string
        status:
          type: string
          enum:
            - draft
            - published
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - title
        - message
        - postedById
        - draftedById
        - status
        - createdAt
        - updatedAt
    UpdateAnnouncementDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the announcement
        message:
          type: string
          description: Message body/content of the announcement
        status:
          type: string
          enum:
            - draft
            - published
          description: Status of the announcement
      required:
        - title
        - message
        - status
    TrainingModuleResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        material:
          type: array
          items:
            type: string
        trainingId:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - title
        - description
        - material
        - trainingId
        - createdAt
        - updatedAt
    TrainingResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        material:
          type: array
          items:
            type: string
        userId:
          type: string
        createdBy:
          $ref: "#/components/schemas/UserResponseDto"
        ratings:
          type: number
        ratingsCount:
          type: number
        enrolmentCount:
          type: number
        finishedCount:
          type: number
        inProgressCount:
          type: number
        publishedDate:
          format: date-time
          type: string
        status:
          type: string
          enum:
            - draft
            - published
            - archived
            - deleted
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        autoEnrollEmployeeIds:
          type: array
          items:
            type: string
        modules:
          $ref: "#/components/schemas/TrainingModuleResponseDto"
      required:
        - id
        - title
        - description
        - material
        - userId
        - createdBy
        - status
        - createdAt
        - updatedAt
    PaginatedTrainingResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/TrainingResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateTrainingModuleDto:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        material:
          type: array
          items:
            type: string
      required:
        - title
        - description
        - material
    CreateTrainingDto:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        material:
          type: array
          items:
            type: string
        autoEnrollEmployeeIds:
          type: array
          items:
            type: string
        status:
          type: string
          enum: &ref_53
            - draft
            - published
            - archived
            - deleted
        includeModules:
          type: boolean
        trainingModules:
          type: array
          items:
            $ref: "#/components/schemas/CreateTrainingModuleDto"
      required:
        - title
        - description
        - material
        - status
    UpdateTrainingDto:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum: *ref_53
      required:
        - title
        - description
        - status
    UpdateTrainingModuleDto:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        material:
          type: array
          items:
            type: string
    EmployeeTrainingResponseDto:
      type: object
      properties:
        id:
          type: string
        employeeId:
          type: string
        trainingId:
          type: string
        status:
          type: string
          enum:
            - enrolled
            - ongoing
            - completed
        feedback:
          type: string
        rating:
          type: number
        training:
          $ref: "#/components/schemas/TrainingResponseDto"
        employee:
          $ref: "#/components/schemas/EmployeeResponseDto"
      required:
        - id
        - employeeId
        - trainingId
        - status
    PaginatedEmployeeTrainingResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/EmployeeTrainingResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateFeedbackEmployeeTrainingDto:
      type: object
      properties:
        feedback:
          type: string
        rating:
          type: number
      required:
        - feedback
        - rating
    CreateAttachmentDto:
      type: object
      properties:
        name:
          type: string
          description: Actual file name
        url:
          type: string
          description: File url
        type:
          type: string
          description: File type
      required:
        - name
        - url
        - type
    GDriveFileResponseDto:
      type: object
      properties:
        id:
          type: string
        fileName:
          type: string
        employeeId:
          type: string
        folderId:
          type: string
        readDate:
          type: string
          format: date-time
        attachment:
          $ref: "#/components/schemas/CreateAttachmentDto"
        employee:
          description: Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        archive:
          type: boolean
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - fileName
        - employeeId
        - attachment
        - employee
        - archive
        - createdAt
        - updatedAt
    PaginatedGDriveFileResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/GDriveFileResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateGDriveFileDto:
      type: object
      properties:
        fileName:
          type: string
        folderId:
          type: string
        readDate:
          type: string
          format: date-time
        attachment:
          $ref: "#/components/schemas/CreateAttachmentDto"
        archive:
          type: boolean
      required:
        - fileName
        - attachment
        - archive
    GDriveFolderResponseDto:
      type: object
      properties:
        id:
          type: string
        folderName:
          type: string
        parentId:
          type: string
        employeeId:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        employee:
          description: Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
      required:
        - id
        - folderName
        - employeeId
        - createdAt
        - updatedAt
        - employee
    GDriveShareResponseDto:
      type: object
      properties:
        id:
          type: string
        sharedItemType:
          type: string
          enum:
            - file
            - folder
        sharedItemId:
          type: string
        fromEmployeeId:
          type: string
        toEmployeeId:
          type: string
        status:
          type: string
          enum:
            - viewed
            - new_incoming
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        fromEmployee:
          description: From Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        toEmployee:
          description: To Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        file:
          description: File
          allOf:
            - $ref: "#/components/schemas/GDriveFileResponseDto"
        folder:
          description: Folder
          allOf:
            - $ref: "#/components/schemas/GDriveFolderResponseDto"
      required:
        - id
        - sharedItemType
        - sharedItemId
        - fromEmployeeId
        - toEmployeeId
        - status
        - createdAt
        - updatedAt
        - fromEmployee
        - toEmployee
        - file
        - folder
    PaginatedGDriveShareResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/GDriveShareResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateGDriveShareDto:
      type: object
      properties:
        sharedItemType:
          type: string
          enum:
            - file
            - folder
        sharedItemId:
          type: string
        toEmployeeId:
          type: string
        status:
          type: string
          enum:
            - viewed
            - new_incoming
      required:
        - sharedItemType
        - sharedItemId
        - toEmployeeId
        - status
    PaginatedGDriveFolderResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/GDriveFolderResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateGDriveFolderDto:
      type: object
      properties:
        folderName:
          type: string
          description: Name of the folder
        parentId:
          type: string
          description: Optional parent folder ID (UUID)
      required:
        - folderName
    CreateRegistryDto:
      type: object
      properties:
        registryName:
          type: string
        ownerId:
          type: string
      required:
        - registryName
        - ownerId
    RegistryResponseDto:
      type: object
      properties:
        id:
          type: string
        registryName:
          type: string
        ownerId:
          type: string
        folder:
          $ref: "#/components/schemas/GDriveFolderResponseDto"
        owner:
          $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - registryName
        - ownerId
        - folder
        - owner
        - createdAt
        - updatedAt
    PaginatedRegistryResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/RegistryResponseDto"
        total:
          type: number
      required:
        - data
        - total
    UpdateRegistryDto:
      type: object
      properties:
        registryName:
          type: string
        ownerId:
          type: string
    CreateCollaboratorDto:
      type: object
      properties:
        employeeId:
          type: string
      required:
        - employeeId
    RegistryCollaboratorResponseDto:
      type: object
      properties:
        id:
          type: string
        registryId:
          type: string
        employeeId:
          type: string
        employee:
          $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - registryId
        - employeeId
        - createdAt
        - updatedAt
    CreateCollaboratorsDto:
      type: object
      properties:
        employeeIds:
          type: array
          items:
            type: string
      required:
        - employeeIds
    PaginatedRegistryCollaboratorResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/RegistryCollaboratorResponseDto"
        total:
          type: number
      required:
        - data
        - total
    CreateCorrespondenceDto:
      type: object
      properties:
        refNo:
          type: string
        subject:
          type: string
        receivedFrom:
          type: string
        sentTo:
          type: string
        type:
          type: string
          enum: &ref_54
            - incoming
            - outgoing
        correspondenceDate:
          format: date-time
          type: string
        receiptDate:
          format: date-time
          type: string
        attachment:
          $ref: "#/components/schemas/CreateAttachmentDto"
        registryId:
          type: string
      required:
        - refNo
        - subject
        - receivedFrom
        - sentTo
        - type
        - correspondenceDate
        - receiptDate
        - attachment
        - registryId
    UpdateCorrespondenceDto:
      type: object
      properties:
        refNo:
          type: string
        subject:
          type: string
        receivedFrom:
          type: string
        sentTo:
          type: string
        type:
          type: string
          enum: *ref_54
        correspondenceDate:
          format: date-time
          type: string
        receiptDate:
          format: date-time
          type: string
        attachment:
          $ref: "#/components/schemas/CreateAttachmentDto"
        registryId:
          type: string
    ForwardCorrespondenceDto:
      type: object
      properties:
        employeeId:
          type: string
        status:
          type: string
          enum:
            - pending
            - received
            - transferred
            - filed
        notes:
          type: string
      required:
        - employeeId
        - status
    createWorkflowTypeDto:
      type: object
      properties:
        workflowType:
          type: string
          description: Workflow type name
      required:
        - workflowType
    WorkflowTypeResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Workflow type ID
        workflowType:
          type: string
          description: Workflow type name
      required:
        - id
        - workflowType
    updateWorkflowTypeDto:
      type: object
      properties:
        workflowType:
          type: string
          description: Workflow type name
      required:
        - workflowType
    CreateWorkflowDto:
      type: object
      properties:
        authorizingUser:
          type: string
          description: Authorizing User/Employee
        title:
          type: string
          description: Workflow title
        description:
          type: string
          description: Workflow description
        amount:
          type: number
          description: Amount
        status:
          type: object
          description: Workflow status
        attachments:
          description: Workflow attachments
          type: array
          items:
            type: string
        workflowTypeId:
          type: string
          description: Workflow type ID
      required:
        - authorizingUser
        - title
        - description
        - amount
        - status
        - attachments
        - workflowTypeId
    WorkflowResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        status:
          type: object
        attachments:
          type: array
          items:
            $ref: "#/components/schemas/AttachmentDto"
        amount:
          type: number
        initiatedBy:
          $ref: "#/components/schemas/EmployeeResponseDto"
        workflowType:
          $ref: "#/components/schemas/WorkflowResponseDto"
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
      required:
        - id
        - title
        - description
        - status
        - amount
        - initiatedBy
        - workflowType
        - createdAt
        - updatedAt
    UpdateWorkflowDto:
      type: object
      properties:
        title:
          type: string
          description: Workflow title
        description:
          type: string
          description: Workflow description
        amount:
          type: number
          description: Amount
        status:
          type: object
          description: Workflow status
        attachments:
          description: Workflow attachments
          type: array
          items:
            type: string
        workflowTypeId:
          type: string
          description: Workflow type ID
      required:
        - title
        - description
        - amount
        - status
        - attachments
        - workflowTypeId
    AuthorizeRequestDto:
      type: object
      properties:
        markAsFinal:
          type: string
          enum:
            - "Yes"
            - "No"
          description: Authorizing User/Employee action
        authorizingUserId:
          type: string
          description: Authorizing User/Employee ID
        authorizationStatus:
          type: object
          description: Authorization action
        workflowId:
          type: string
          description: Workflow ID
      required:
        - markAsFinal
        - authorizingUserId
        - authorizationStatus
        - workflowId
    WorkflowCommentDto:
      type: object
      properties:
        comment:
          type: string
          description: Comment
        attachment:
          description: Workflow attachments (optional)
          allOf:
            - $ref: "#/components/schemas/AttachmentDto"
        workflowId:
          type: string
          description: ID of the query
      required:
        - comment
        - workflowId
    CreateWorkgroupDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the Workgroup
        description:
          type: string
          description: Body/content of the Workgroup
        executors:
          description: Executors of the Workgroup
          type: array
          items:
            type: string
        dueDate:
          format: date-time
          type: string
          description: Workgroup due date
        status:
          type: string
          enum:
            - New
            - Ongoing
            - Completed
          description: Staus of Workgroup
        amount:
          type: number
          description: Workgroup amount
        attachments:
          description: Workgroup attachments
          type: array
          items:
            type: string
        initiatedById:
          type: string
          description: ID of the initiator
      required:
        - title
        - description
        - executors
        - dueDate
        - status
        - amount
        - attachments
        - initiatedById
    WorkgroupCommentsEntity:
      type: object
      properties: {}
    WorkgroupHistoryDto:
      type: object
      properties:
        id:
          type: string
        status:
          type: string
          enum:
            - New
            - Ongoing
            - Completed
        workgroupId:
          type: string
        snapshot:
          type: object
        performedBy:
          type: string
        employee:
          description: " Employee"
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        createdAt:
          format: date-time
          type: string
      required:
        - id
        - status
        - workgroupId
        - snapshot
        - performedBy
        - employee
        - createdAt
    WorkgroupResponseDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        attachments:
          type: array
          items:
            type: string
        status:
          type: string
          enum:
            - New
            - Ongoing
            - Completed
          description: Staus of Workgroup
        createdAt:
          format: date-time
          type: string
        initiatedBy:
          description: CreatedBy Employee
          allOf:
            - $ref: "#/components/schemas/EmployeeResponseDto"
        comments:
          description: Comments on Workgroup
          type: array
          items:
            $ref: "#/components/schemas/WorkgroupCommentsEntity"
        history:
          description: History of Memo
          type: array
          items:
            $ref: "#/components/schemas/WorkgroupHistoryDto"
      required:
        - id
        - title
        - description
        - attachments
        - status
        - createdAt
        - initiatedBy
        - comments
        - history
    UpdateWorkgroupDto:
      type: object
      properties:
        title:
          type: string
          description: Title of the Workgroup
        description:
          type: string
          description: Body/content of the Workgroup
        dueDate:
          type: string
          description: Workgroup due date
        amount:
          type: number
          description: Workgroup amount
        status:
          type: string
          enum:
            - New
            - Ongoing
            - Completed
          description: Status of the Workgroup
    WorkgroupCommentCreateDto:
      type: object
      properties:
        comment:
          type: string
          description: Comment
        workgroupId:
          type: string
          description: ID of the query
      required:
        - comment
        - workgroupId
    AddWorkgroupExecutorDto:
      type: object
      properties:
        employeeId:
          type: string
          description: Employee ID
        workgroupId:
          type: string
          description: ID of the query
      required:
        - employeeId
        - workgroupId
    RemoveWorkgroupDocsDto:
      type: object
      properties:
        gDriveFile:
          type: string
          description: G-Drive File
        workgroupId:
          type: string
          description: Workgroup ID
        id:
          type: string
          description: Document ID
      required:
        - gDriveFile
        - workgroupId
        - id
    AddWorkgroupDocsDto:
      type: object
      properties:
        gDriveFileId:
          type: string
          description: G-Drive File UUID
        workgroupId:
          type: string
          description: Workgroup ID
      required:
        - gDriveFileId
        - workgroupId
