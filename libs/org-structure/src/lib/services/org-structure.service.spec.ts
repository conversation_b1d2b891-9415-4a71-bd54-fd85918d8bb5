import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { OrganizationStructureService } from './org-structure.service';
import {
  DepartmentRepository,
  MdaRepository,
  UnitRepository,
  PositionRepository,
  OrganizationProfileRepository,
} from '../repositories';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  CreateMdaDto,
  MdaResponseDto,
  CreateUnitDto,
  UnitResponseDto,
  CreatePositionDto,
  CreateOrganizationProfileDto,
  OrganizationProfileResponseDto,
} from '../dtos';
import { Department, MDA, OrganizationProfile, Unit, Position } from '../entities';

describe('OrganizationStructureService', () => {
  let service: OrganizationStructureService;
  let departmentRepository: jest.Mocked<DepartmentRepository>;
  let mdaRepository: jest.Mocked<MdaRepository>;
  let unitRepository: jest.Mocked<UnitRepository>;
  let positionRepository: jest.Mocked<PositionRepository>;
  let organizationProfileRepository: jest.Mocked<OrganizationProfileRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationStructureService,
        {
          provide: DepartmentRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: MdaRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: UnitRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: PositionRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: OrganizationProfileRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<OrganizationStructureService>(OrganizationStructureService);
    departmentRepository = module.get(DepartmentRepository);
    mdaRepository = module.get(MdaRepository);
    unitRepository = module.get(UnitRepository);
    positionRepository = module.get(PositionRepository);
    organizationProfileRepository = module.get(OrganizationProfileRepository);
  });

  describe('Department Operations', () => {
    const mockDepartmentDto: CreateDepartmentDto = {
      name: 'IT Department',
      code: 'IT',
      commRef: 'Information Technology Department',
    };

    const mockDepartmentResponse: DepartmentResponseDto = {
      id: '1',
      ...mockDepartmentDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should save a department', async () => {
      departmentRepository.save.mockResolvedValue(mockDepartmentResponse as unknown as Department);

      const result = await service.saveDepartment(mockDepartmentDto);

      expect(departmentRepository.save).toHaveBeenCalledWith(mockDepartmentDto);
      expect(result).toEqual(mockDepartmentResponse);
      expect(result).toBeInstanceOf(Object as unknown as DepartmentResponseDto);
    });

    it('should update a department', async () => {
      const updateDto: CreateDepartmentDto = {
        ...mockDepartmentDto,
        name: 'Updated IT Department',
      };
      const updatedResponse: DepartmentResponseDto = {
        ...mockDepartmentResponse,
        name: 'Updated IT Department',
      };

      departmentRepository.findOne.mockResolvedValue(
        mockDepartmentResponse as unknown as Department,
      );
      departmentRepository.save.mockResolvedValue(updatedResponse as unknown as Department);

      const result = await service.updateDepartment('1', updateDto);

      expect(departmentRepository.findOne).toHaveBeenCalledWith('1');
      expect(departmentRepository.save).toHaveBeenCalledWith({
        id: '1',
        ...updateDto,
      });
      expect(result).toEqual(updatedResponse);
    });

    it('should throw NotFoundException when updating non-existent department', async () => {
      departmentRepository.findOne.mockResolvedValue(null);

      await expect(service.updateDepartment('999', mockDepartmentDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should get a department by id', async () => {
      departmentRepository.findOne.mockResolvedValue(
        mockDepartmentResponse as unknown as Department,
      );

      const result = await service.getDepartment('1');

      expect(departmentRepository.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockDepartmentResponse);
    });

    it('should throw NotFoundException when getting non-existent department', async () => {
      departmentRepository.findOne.mockResolvedValue(null);

      await expect(service.getDepartment('999')).rejects.toThrow(NotFoundException);
    });

    it('should delete a department', async () => {
      departmentRepository.findOne.mockResolvedValue(
        mockDepartmentResponse as unknown as Department,
      );
      departmentRepository.delete.mockResolvedValue(undefined);

      await service.deleteDepartment('1');

      expect(departmentRepository.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('MDA Operations', () => {
    const mockMdaDto: CreateMdaDto = {
      name: 'Ministry of Finance',
      code: 'MOF',
    };

    const mockMdaResponse: MdaResponseDto = {
      id: '1',
      ...mockMdaDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should save an MDA', async () => {
      mdaRepository.save.mockResolvedValue(mockMdaResponse as unknown as MDA);

      const result = await service.saveMda(mockMdaDto);

      expect(mdaRepository.save).toHaveBeenCalledWith(mockMdaDto);
      expect(result).toEqual(mockMdaResponse);
    });

    it('should update an MDA', async () => {
      const updateDto: CreateMdaDto = {
        ...mockMdaDto,
        name: 'Updated Ministry of Finance',
      };
      const updatedResponse: MdaResponseDto = {
        ...mockMdaResponse,
        name: 'Updated Ministry of Finance',
      };

      mdaRepository.findOne.mockResolvedValue(mockMdaResponse as unknown as MDA);
      mdaRepository.save.mockResolvedValue(updatedResponse as unknown as MDA);

      const result = await service.updateMda('1', updateDto);

      expect(mdaRepository.findOne).toHaveBeenCalledWith('1');
      expect(mdaRepository.save).toHaveBeenCalledWith({
        id: '1',
        ...updateDto,
      });
      expect(result).toEqual(updatedResponse);
    });

    it('should throw NotFoundException when updating non-existent MDA', async () => {
      mdaRepository.findOne.mockResolvedValue(null);

      await expect(service.updateMda('999', mockMdaDto)).rejects.toThrow(NotFoundException);
    });

    it('should get an MDA by id', async () => {
      mdaRepository.findOne.mockResolvedValue(mockMdaResponse as unknown as MDA);

      const result = await service.getMda('1');

      expect(mdaRepository.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockMdaResponse);
    });

    it('should throw NotFoundException when getting non-existent MDA', async () => {
      mdaRepository.findOne.mockResolvedValue(null);

      await expect(service.getMda('999')).rejects.toThrow(NotFoundException);
    });

    it('should delete an MDA', async () => {
      mdaRepository.findOne.mockResolvedValue(mockMdaResponse as unknown as MDA);
      mdaRepository.delete.mockResolvedValue(undefined);

      await service.deleteMda('1');

      expect(mdaRepository.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('Unit Operations', () => {
    const mockUnitDto: CreateUnitDto = {
      name: 'Software Development',
      departmentCode: 'SD',
      commRef: 'Software development unit',
      departmentId: '1',
    };

    const mockUnitResponse: UnitResponseDto = {
      id: '1',
      ...mockUnitDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should save a unit', async () => {
      unitRepository.save.mockResolvedValue(mockUnitResponse as unknown as Unit);

      const result = await service.saveUnit(mockUnitDto);

      expect(unitRepository.save).toHaveBeenCalledWith(mockUnitDto);
      expect(result).toEqual(mockUnitResponse);
    });

    it('should update a unit', async () => {
      const updateDto: CreateUnitDto = {
        ...mockUnitDto,
        name: 'Updated Software Development',
      };
      const updatedResponse: UnitResponseDto = {
        ...mockUnitResponse,
        name: 'Updated Software Development',
      };

      unitRepository.findOne.mockResolvedValue(mockUnitResponse as unknown as Unit);
      unitRepository.save.mockResolvedValue(updatedResponse as unknown as Unit);

      const result = await service.updateUnit('1', updateDto);

      expect(unitRepository.findOne).toHaveBeenCalledWith('1');
      expect(unitRepository.save).toHaveBeenCalledWith({
        id: '1',
        ...updateDto,
      });
      expect(result).toEqual(updatedResponse);
    });

    it('should throw NotFoundException when updating non-existent unit', async () => {
      unitRepository.findOne.mockResolvedValue(null);

      await expect(service.updateUnit('999', mockUnitDto)).rejects.toThrow(NotFoundException);
    });

    it('should get a unit by id', async () => {
      unitRepository.findOne.mockResolvedValue(mockUnitResponse as unknown as Unit);

      const result = await service.getUnit('1');

      expect(unitRepository.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockUnitResponse);
    });

    it('should throw NotFoundException when getting non-existent unit', async () => {
      unitRepository.findOne.mockResolvedValue(null);

      await expect(service.getUnit('999')).rejects.toThrow(NotFoundException);
    });

    it('should delete a unit', async () => {
      unitRepository.delete.mockResolvedValue(undefined);

      await service.deleteUnit('1');

      expect(unitRepository.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('Position Operations', () => {
    const mockPositionDto: CreatePositionDto = {
      name: 'Software Engineer',
    };

    const mockPositionResponse: Position = {
      id: '1',
      ...mockPositionDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should save a position', async () => {
      positionRepository.save.mockResolvedValue(mockPositionResponse);

      const result = await service.savePosition(mockPositionDto);

      expect(positionRepository.save).toHaveBeenCalledWith(mockPositionDto);
      expect(result).toEqual(mockPositionResponse);
    });

    it('should update a position', async () => {
      const updateDto: CreatePositionDto = {
        ...mockPositionDto,
        name: 'Senior Software Engineer',
      };
      const updatedResponse: Position = {
        ...mockPositionResponse,
        name: 'Senior Software Engineer',
      };

      positionRepository.findOne.mockResolvedValue(mockPositionResponse);
      positionRepository.save.mockResolvedValue(updatedResponse);

      const result = await service.updatePosition('1', updateDto);

      expect(positionRepository.findOne).toHaveBeenCalledWith('1');
      expect(positionRepository.save).toHaveBeenCalledWith({
        id: '1',
        ...updateDto,
      });
      expect(result).toEqual(updatedResponse);
    });

    it('should throw NotFoundException when updating non-existent position', async () => {
      positionRepository.findOne.mockResolvedValue(null);

      await expect(service.updatePosition('999', mockPositionDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should get a position by id', async () => {
      positionRepository.findOne.mockResolvedValue(mockPositionResponse);

      const result = await service.getPosition('1');

      expect(positionRepository.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockPositionResponse);
    });

    it('should throw NotFoundException when getting non-existent position', async () => {
      positionRepository.findOne.mockResolvedValue(null);

      await expect(service.getPosition('999')).rejects.toThrow(NotFoundException);
    });

    it('should delete a position', async () => {
      positionRepository.findOne.mockResolvedValue(mockPositionResponse);
      positionRepository.delete.mockResolvedValue(undefined);

      await service.deletePosition('1');

      expect(positionRepository.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('Organization Profile Operations', () => {
    const mockOrgProfileDto: CreateOrganizationProfileDto = {
      name: 'Tech Corp',
    };

    const mockOrgProfileResponse: OrganizationProfileResponseDto = {
      id: '1',
      ...mockOrgProfileDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should save an organization profile', async () => {
      organizationProfileRepository.save.mockResolvedValue(
        mockOrgProfileResponse as unknown as OrganizationProfile,
      );

      const result = await service.saveOrganization(mockOrgProfileDto);

      expect(organizationProfileRepository.save).toHaveBeenCalledWith(mockOrgProfileDto);
      expect(result).toEqual(mockOrgProfileResponse);
    });

    it('should update an organization profile', async () => {
      const updateDto: CreateOrganizationProfileDto = {
        ...mockOrgProfileDto,
        name: 'Updated Tech Corp',
      };
      const updatedResponse: OrganizationProfileResponseDto = {
        ...mockOrgProfileResponse,
        name: 'Updated Tech Corp',
      };

      organizationProfileRepository.findOne.mockResolvedValue(
        mockOrgProfileResponse as unknown as OrganizationProfile,
      );
      organizationProfileRepository.save.mockResolvedValue(
        updatedResponse as unknown as OrganizationProfile,
      );

      const result = await service.updateOrganization('1', updateDto);

      expect(organizationProfileRepository.findOne).toHaveBeenCalledWith('1');
      expect(organizationProfileRepository.save).toHaveBeenCalledWith({
        id: '1',
        ...updateDto,
      });
      expect(result).toEqual(updatedResponse);
    });

    it('should throw NotFoundException when updating non-existent organization profile', async () => {
      organizationProfileRepository.findOne.mockResolvedValue(null);

      await expect(service.updateOrganization('999', mockOrgProfileDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should get an organization profile by id', async () => {
      organizationProfileRepository.findOne.mockResolvedValue(
        mockOrgProfileResponse as unknown as OrganizationProfile,
      );

      const result = await service.getOrganization('1');

      expect(organizationProfileRepository.findOne).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockOrgProfileResponse);
    });

    it('should throw NotFoundException when getting non-existent organization profile', async () => {
      organizationProfileRepository.findOne.mockResolvedValue(null);

      await expect(service.getOrganization('999')).rejects.toThrow(NotFoundException);
    });

    it('should delete an organization profile', async () => {
      organizationProfileRepository.findOne.mockResolvedValue(
        mockOrgProfileResponse as unknown as OrganizationProfile,
      );

      organizationProfileRepository.delete.mockResolvedValue(undefined);

      await service.deleteOrganization('1');

      expect(organizationProfileRepository.delete).toHaveBeenCalledWith('1');
    });
  });
});
