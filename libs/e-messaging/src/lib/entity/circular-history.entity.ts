import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { CircularEntity } from '../entity';
import { Employee } from '@igov/employee';

@Entity('circular_histories')
export class CircularHistoryEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid', { nullable: false })
  performedBy!: string;

  @Column('uuid', { nullable: false })
  circularId!: string;

  @Column('jsonb', { nullable: true })
  snapshot?: object;

  @ManyToOne(() => CircularEntity, (circular) => circular.id)
  @JoinColumn({ name: 'circular_id' })
  circular!: CircularEntity;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'performed_by' })
  employee!: Employee;
}
