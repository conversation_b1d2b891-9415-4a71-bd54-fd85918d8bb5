export interface CreateOrganizationProfileDto {
  name: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
}

export interface UpdateOrganizationProfileDto {
  name?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
}

export interface OrganizationProfileResponseDto {
  id: string;
  name: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
}