import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import _ from 'lodash';

function getStateOptions() {
  return [
    'Abia',
    'Adam<PERSON>',
    'Akwa Ibom',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Borno',
    'Cross River',
    'Delta',
    'E<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Enugu',
    '<PERSON><PERSON>',
    'I<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>du<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Kogi',
    'Kwara',
    'Lagos',
    'Nasarawa',
    'Niger',
    'Ogun',
    'Ondo',
    'Osun',
    'Oyo',
    'Plateau',
    'Rivers',
    'Sokoto',
    'Taraba',
    'Yobe',
    'Zamfara',
    'Federal Capital Territory',
  ].map((state) => ({
    value: state,
    label: state,
  }));
}

const handleMultipleFileUpload = async (files: File[]) => {
  try {
    const aws_secret_access_key = process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY;
    const aws_access_key_id = process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID;
    const aws_region = process.env.NEXT_PUBLIC_AWS_REGION;
    const aws_bucket_name = process.env.NEXT_PUBLIC_AWS_S3_BUCKET_NAME;

    const s3 = new S3Client({
      region: aws_region,
      credentials: {
        accessKeyId: aws_access_key_id,
        secretAccessKey: aws_secret_access_key,
      },
    });

    const uploadedFileUrls: string[] = [];

    for (const file of files) {
      const key = `uploads/${Date.now()}-${file.name}`;
      const buffer = await file.arrayBuffer().then((arrayBuffer) => Buffer.from(arrayBuffer));

      const command = new PutObjectCommand({
        Bucket: aws_bucket_name,
        Key: key,
        Body: buffer,
        ContentType: file.type,
      });
      await s3.send(command);

      const fileUrl = `https://${aws_bucket_name}.s3.${aws_region}.amazonaws.com/${key}`;
      uploadedFileUrls.push(fileUrl);
    }

    return uploadedFileUrls;
  } catch (error) {
    console.error('File upload failed:', error);
    return [];
    // throw error;
  }
};
const handleSingleFileUpload = async (file: File) => {
  try {
    const aws_secret_access_key = process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY;
    const aws_access_key_id = process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID;
    const aws_region = process.env.NEXT_PUBLIC_AWS_REGION;
    const aws_bucket_name = process.env.NEXT_PUBLIC_AWS_S3_BUCKET_NAME;

    const s3 = new S3Client({
      region: aws_region,
      credentials: {
        accessKeyId: aws_access_key_id,
        secretAccessKey: aws_secret_access_key,
      },
    });

    const key = `uploads/${Date.now()}-${file.name}`;
    const buffer = await file.arrayBuffer().then((arrayBuffer) => Buffer.from(arrayBuffer));

    const command = new PutObjectCommand({
      Bucket: aws_bucket_name,
      Key: key,
      Body: buffer,
      ContentType: file.type,
    });
    await s3.send(command);

    return `https://${aws_bucket_name}.s3.${aws_region}.amazonaws.com/${key}`;

  } catch (error) {
    console.error('File upload failed:', error);
    return '';
  }
};

function firstLetterUpperCase(value: string): string {
  if (!value) return '';
  return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
}

function getGradeLevels() {
  return _.range(1, 18).map((level) => ({
    value: `Level ${level}`,
    label: `Level ${level}`,
  }));
}

function getBloodGroups() {
  return ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map((group) => ({
    value: group,
    label: group,
  }));
}

function getGenders() {
  return ['Male', 'Female', 'Other'].map((gender) => ({
    value: gender,
    label: gender,
  }));
}

function getTitles() {
  return [
    'Mr',
    'Mrs',
    'Miss',
    'Dr',
    'Prof',
    'Engr',
    'Chief',
    'Sir',
    'Lady',
    'Hon',
    'Barr',
    'Alhaji',
    'Alhaja',
  ].map((title) => ({
    value: title,
    label: title,
  }));
}
export const generatePassword = (
  length = 12,
  options = {
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSpecialChars: true,
  },
): string => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  let chars = '';
  let password = '';

  // Add character sets based on options
  if (options.includeUppercase) chars += uppercase;
  if (options.includeLowercase) chars += lowercase;
  if (options.includeNumbers) chars += numbers;
  if (options.includeSpecialChars) chars += specialChars;

  // Ensure at least one character from each selected set
  if (options.includeUppercase) password += uppercase[Math.floor(Math.random() * uppercase.length)];
  if (options.includeLowercase) password += lowercase[Math.floor(Math.random() * lowercase.length)];
  if (options.includeNumbers) password += numbers[Math.floor(Math.random() * numbers.length)];
  if (options.includeSpecialChars)
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

  // Fill the rest of the password
  const remainingLength = length - password.length;
  for (let i = 0; i < remainingLength; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    password += chars[randomIndex];
  }

  // Shuffle the password
  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');
};

export {
  getBloodGroups,
  getGradeLevels,
  firstLetterUpperCase,
  getStateOptions,
  getGenders,
  getTitles,
  handleMultipleFileUpload as handleFileUpload,
  handleSingleFileUpload,
};
