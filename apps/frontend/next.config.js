//@ts-check
const path = require('path');

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  nx: {
    svgr: false,
  },
  reactStrictMode: false,
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production'
  },
  typescript: {
    ignoreBuildErrors: true
  },
  // Transpile PSPDFKit to handle modern JavaScript syntax (fixes optional chaining errors)
  transpilePackages: ['pspdfkit'],
  webpack: (config, { isServer }) => {
    // Add path aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@auth_': path.resolve(__dirname, './@auth'),
      '@i18n': path.resolve(__dirname, './@i18n'),
      '@fuse': path.resolve(__dirname, './@fuse'),
      '@history': path.resolve(__dirname, './@history'),
      '@mock-utils': path.resolve(__dirname, './@mock-utils'),
      '@schema': path.resolve(__dirname, './@schema'),
      '@': path.resolve(__dirname, '.'),
    };

    // Existing raw loader rule
    if (config.module && config.module.rules) {
      config.module.rules.push({
        test: /\.(json|js|ts|tsx|jsx)$/,
        resourceQuery: /raw/,
        use: 'raw-loader'
      });
    }


    return config;
  },
  
};

const plugins = [
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
