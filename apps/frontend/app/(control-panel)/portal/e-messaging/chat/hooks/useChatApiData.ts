import { useState, useCallback, useEffect, useContext, useRef } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { useChatNotifications } from '@/contexts/ChatNotificationContext';
import { chatApiService } from '@/services/api/chatService';
import { employeeApiService } from '@/services/api/employeeService';
import { ChatMessageResponseDto } from '@/services/api/types/chat.types';
import { EmployeeResponseDto } from '@/services/api/types/employee.types';
import { Chat, Contact, Message, Profile } from '../types/chat.types';
import AppContext from '@/contexts/AppContext';

interface UseChatApiDataState {
  // Core data matching what components expect
  chatList: Chat[];
  contacts: Contact[];
  user: Profile | null;

  // Loading and error states
  loading: boolean;
  error: string | null;

  // Specific loading states for better UX
  contactsLoading: boolean;
  contactsError: string | null;

  // Individual conversation data
  activeConversation: {
    chat: Chat | null;
    messages: Message[];
    loading: boolean;
    error: string | null;
  };

  // Caching for employee data
  employeesCache: {
    data: Contact[];
    timestamp: number;
    ttl: number;
  } | null;

  // Message deduplication tracking
  processedMessageIds: Set<string>;
}

interface UseChatApiDataActions {
  // Data fetching actions
  refreshChatList: () => Promise<void>;
  refreshContacts: () => Promise<void>;
  loadConversation: (receiverId: string) => Promise<void>;
  refreshActiveConversation: () => Promise<void>;

  // Message actions (will integrate with WebSocket later)
  markAsRead: (conversationId: string) => Promise<void>;
  addMessage: (message: Message) => void;
  startNewConversation: (contactId: string) => string; // Returns chat ID for navigation
}

/**
 * ============================================================================
 * CHAT DATA MANAGEMENT HOOK - CENTRAL ORCHESTRATOR FOR CHAT SYSTEM
 * ============================================================================
 *
 * This is the primary data management hook for the entire chat feature.
 * It serves as the single source of truth for all chat-related state and operations.
 *
 * ARCHITECTURE OVERVIEW:
 * =====================
 *
 * 1. DATA SOURCES & INTEGRATIONS:
 *    ├── chatApiService (REST API):
 *    │   ├── getUserMessages() - Fetches unread/undelivered messages TO user
 *    │   ├── getConversation() - Gets full conversation history between users
 *    │   └── markAsRead() - Marks individual messages as read
 *    ├── employeeApiService - Provides contact data for all employees
 *    ├── WebSocket - Real-time message delivery and live chat features
 *    └── Auth Context - Current user authentication and profile data
 *
 * 2. CORE STATE STRUCTURE:
 *    ├── chatList: Chat[] - All user's conversations with metadata
 *    │   ├── Each chat contains: id, contactIds, lastMessage, unreadCount, etc.
 *    │   └── Built from API messages, grouped by conversation participants
 *    ├── activeConversation - Currently opened chat with full message history
 *    │   ├── chat: Chat | null - Reference to the opened chat
 *    │   ├── messages: Message[] - Full message history for this chat
 *    │   └── loading/error states for conversation-specific operations
 *    ├── contacts: Contact[] - All employees available for messaging
 *    │   ├── Cached for 5 minutes to optimize performance
 *    │   └── Transformed from employee API data to chat-friendly format
 *    └── user: Profile | null - Current user's chat profile
 *
 * 3. KEY OPERATIONS & WORKFLOWS:
 *    ├── INITIALIZATION:
 *    │   ├── loadChatList() - Fetches unread messages and builds conversation list
 *    │   ├── loadContacts() - Fetches and caches all employees as potential contacts
 *    │   └── Sets up WebSocket listeners for real-time updates
 *    ├── CONVERSATION MANAGEMENT:
 *    │   ├── loadConversation(receiverId) - Loads full message history
 *    │   ├── Auto-marks messages as read when conversation opens
 *    │   └── Updates both conversation state and chat list metadata
 *    ├── MESSAGE HANDLING:
 *    │   ├── addMessage() - Processes incoming messages (WebSocket + deduplication)
 *    │   ├── sendMessage() - Optimistic UI updates + WebSocket emission
 *    │   └── Message deduplication prevents duplicates from multiple sources
 *    └── READ STATUS MANAGEMENT:
 *        ├── markAsRead() - Manual marking of conversations as read
 *        ├── Auto-read when conversations are opened
 *        └── Refreshes chat list after read operations to update UI
 *
 * 4. REAL-TIME FEATURES:
 *    ├── WebSocket Integration:
 *    │   ├── Listens for 'chat:message' events for instant message delivery
 *    │   ├── Transforms WebSocket messages to frontend Message format
 *    │   └── Triggers notifications for messages from other users
 *    ├── Optimistic Updates:
 *    │   ├── Messages appear instantly in UI before API confirmation
 *    │   ├── Temporary message IDs until server assigns real IDs
 *    │   └── Graceful handling of send failures with error states
 *    └── Deduplication Strategy:
 *        ├── processedIdsRef tracks already-processed message IDs
 *        ├── Prevents duplicate messages from WebSocket and API calls
 *        └── Uses refs to avoid React state race conditions
 *
 * 5. PERFORMANCE OPTIMIZATIONS:
 *    ├── Employee Caching:
 *    │   ├── 5-minute TTL to balance freshness and performance
 *    │   ├── Async cache updates to avoid render blocking
 *    │   └── Fallback to cache while refreshing in background
 *    ├── Message Deduplication:
 *    │   ├── Set-based tracking for O(1) duplicate detection
 *    │   ├── Ref-based storage to avoid triggering re-renders
 *    │   └── Prevents memory leaks from infinite message accumulation
 *    └── Optimistic Updates:
 *        ├── Immediate UI feedback for better perceived performance
 *        ├── Background API synchronization without blocking UI
 *        └── Error handling with graceful fallbacks
 *
 * @returns {UseChatApiDataState & UseChatApiDataActions} Complete chat state and operations
 */
export function useChatApiData(): UseChatApiDataState & UseChatApiDataActions {
  const { user: authUser, employeeDetails } = useAuth();
  const { webSocket } = useContext(AppContext);
  const { showChatNotification } = useChatNotifications();

  // ========================================================================
  // REFS FOR PERFORMANCE & RACE CONDITION PREVENTION
  // ========================================================================

  // Tracks processed message IDs to prevent duplicates from multiple sources
  // Uses ref instead of state to avoid triggering re-renders and race conditions
  const processedIdsRef = useRef(new Set<string>());

  // Prevents loadConversation from running immediately after sending messages
  // This prevents overwriting optimistic updates with stale API data
  const recentMessageSentRef = useRef<string | null>(null);

  // ========================================================================
  // MAIN STATE OBJECT - CONTAINS ALL CHAT-RELATED DATA
  // ========================================================================

  const [state, setState] = useState<UseChatApiDataState>({
    // Core chat data consumed by UI components
    chatList: [], // Array of all user's conversations with metadata
    contacts: [], // All employees available for messaging
    user: null, // Current user's profile for chat system

    // Global loading and error states
    loading: true, // Overall loading state for initial data fetch
    error: null, // Global error message for critical failures

    // Granular loading states for better UX
    contactsLoading: false, // Specific loading state for contacts/employees
    contactsError: null, // Specific error state for contacts operations

    // Active conversation data (currently opened chat)
    activeConversation: {
      chat: null, // Reference to the opened chat from chatList
      messages: [], // Full message history for this conversation
      loading: false, // Loading state for conversation operations
      error: null, // Error state for conversation-specific failures
    },

    // Performance optimization: cached employee data with TTL
    employeesCache: null, // Cache object with timestamp and 5-minute TTL
    processedMessageIds: new Set(), // Legacy - moved to ref for performance
  });

  /**
   * Transform API message to frontend Message type
   */
  const transformMessage = useCallback((apiMessage: ChatMessageResponseDto): Message => {
    return {
      id: apiMessage.id,
      senderId: apiMessage.senderId,
      receiverId: apiMessage.receiverId,
      content: apiMessage.content,
      deliveredAt: apiMessage.deliveredAt,
      readAt: apiMessage.readAt,
      isRead: apiMessage.isRead,
      isEdited: apiMessage.isEdited,
      createdAt: apiMessage.createdAt,
      updatedAt: apiMessage.updatedAt,
    };
  }, []);

  /**
   * Create user Profile from auth data
   */
  const createUserProfile = useCallback((): Profile | null => {
    if (!authUser || !employeeDetails) return null;

    // Construct full name from available fields
    const fullName = [
      employeeDetails.firstName,
      employeeDetails.otherNames,
      employeeDetails.lastName,
    ]
      .filter(Boolean)
      .join(' ');

    // Fallback name from User if employee name not available
    const userFallbackName =
      [authUser.firstName, authUser.lastName].filter(Boolean).join(' ') || authUser.email;

    return {
      id: authUser.id.toString(),
      name: fullName || userFallbackName,
      email: employeeDetails.email || authUser.email,
      avatar: employeeDetails.photoUrl || '#', // Use photoUrl, not avatar
      about: employeeDetails.title || 'Government Employee', // Use title, not jobTitle
      status: 'online', // Default to online, will integrate with presence later
    };
  }, [authUser, employeeDetails]);

  /**
   * Transform Employee API response to Contact format
   * Returns null for employees without valid userId (filtered out later)
   */
  const transformEmployeeToContact = useCallback(
    (employee: EmployeeResponseDto): Contact | null => {
      // Skip employees without userId - they can't receive messages
      if (!employee.userId || employee.userId.trim() === '') {
        return null;
      }

      // Construct full name from available fields
      const fullName = [employee.firstName, employee.otherNames, employee.lastName]
        .filter(Boolean)
        .join(' ');

      // Validate that we have a meaningful name
      if (!fullName.trim()) {
        return null;
      }

      return {
        id: employee.userId, // Guaranteed to exist due to validation above
        name: fullName,
        about: employee.title || 'Government Employee',
        avatar: employee.photoUrl || '#',
        status: 'offline' as const, // Default to offline, will integrate with presence later
        details: {
          emails: employee.email ? [{ email: employee.email, label: 'Work' }] : [],
          phoneNumbers: employee.mobileNumber
            ? [{ country: 'NG', phoneNumber: employee.mobileNumber, label: 'Work' }]
            : [],
          title: employee.title || '',
          company: 'Government Employee', // Could enhance with unit/department info later
          birthday: employee.birthDate || '',
          address: employee.address?.contact || '',
        },
        attachments: {
          media: [],
          docs: [],
          links: [],
        },
      };
    },
    [],
  );

  /**
   * Transform API messages into Chat conversations
   * Groups messages by conversation partner and creates Chat objects
   */
  const transformMessagesToChats = useCallback(
    (messages: ChatMessageResponseDto[], currentUserId: string): Chat[] => {
      if (!messages.length) return [];

      // Group messages by conversation partner
      const conversationMap = new Map<
        string,
        {
          messages: ChatMessageResponseDto[];
          latestMessage: ChatMessageResponseDto;
        }
      >();

      messages.forEach((message) => {
        // Determine conversation partner (the other person in the conversation)
        const partnerId =
          message.senderId === currentUserId ? message.receiverId : message.senderId;

        if (!conversationMap.has(partnerId)) {
          conversationMap.set(partnerId, {
            messages: [],
            latestMessage: message,
          });
        }

        const conversation = conversationMap.get(partnerId)!;
        conversation.messages.push(message);

        // Update latest message if this one is newer
        if (
          new Date(message.createdAt || '') > new Date(conversation.latestMessage.createdAt || '')
        ) {
          conversation.latestMessage = message;
        }
      });

      // Convert to Chat objects
      return Array.from(conversationMap.entries())
        .map(([partnerId, data]) => {
          // Calculate unread count (messages TO the current user that are unread)
          const unreadCount = data.messages.filter(
            (msg) => msg.receiverId === currentUserId && !msg.isRead,
          ).length;

          const chat: Chat = {
            id: partnerId, // Use partner ID as chat ID
            contactIds: [partnerId, currentUserId],
            unreadCount,
            muted: false, // Not in API yet
            lastMessage: data.latestMessage.content,
            lastMessageAt: data.latestMessage.createdAt || new Date().toISOString(),
            messages: data.messages.map(transformMessage),
          };

          return chat;
        })
        .sort(
          (a, b) =>
            // Sort by latest message time (most recent first)
            new Date(b.lastMessageAt).getTime() - new Date(a.lastMessageAt).getTime(),
        );
    },
    [transformMessage],
  );

  /**
   * Fetch chat list from API using getUserMessages
   * Gets unread/undelivered messages TO the user, then derives conversations
   */
  const fetchChatList = useCallback(async () => {
    if (!authUser) return;

    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      // Get user's unread/undelivered messages
      const userMessages = await chatApiService.getUserMessages({ limit: 100 });

      // Transform to Chat format
      const chats = transformMessagesToChats(userMessages, authUser.id.toString());

      setState((prev) => ({
        ...prev,
        chatList: chats,
        loading: false,
      }));

      console.log('🔄 Chat list refreshed:', { 
        messageCount: userMessages.length, 
        chatCount: chats.length,
        unreadCounts: chats.map(c => ({ id: c.id, unread: c.unreadCount }))
      });
    } catch (error: any) {
      console.error('Failed to fetch chat list:', error);
      setState((prev) => ({
        ...prev,
        chatList: [],
        loading: false,
        error: error.message || 'Failed to fetch conversations',
      }));
    }
  }, [authUser, transformMessagesToChats]);

  /**
   * Fetch all employees as potential contacts for messaging
   * Replaces placeholder contact generation with real employee data
   * Includes caching to prevent unnecessary API calls
   */
  const fetchContacts = useCallback(async () => {
    if (!authUser) return;

    try {
      // Cache settings
      const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache
      const now = Date.now();

      // Check if we have valid cached data - use async update to avoid setState during render
      const hasValidCache =
        state.employeesCache && now - state.employeesCache.timestamp < state.employeesCache.ttl;

      if (hasValidCache) {
        // Use setTimeout to defer state update and avoid setState during render
        setTimeout(() => {
          setState((prev) => ({
            ...prev,
            contacts: prev.employeesCache?.data || [],
            contactsLoading: false,
            contactsError: null,
          }));
        }, 0);
        return;
      }

      // Set loading state
      setState((prev) => ({
        ...prev,
        contactsLoading: true,
        contactsError: null,
      }));

      // Get all employees from the API
      const employeesResponse = await employeeApiService.getAllEmployees();
      const employees = employeesResponse.employees;

      // Transform employees to Contact format, excluding current user
      const contacts: Contact[] = employees
        .filter((employee) => {
          // Exclude current user from contacts list
          return employee.userId && employee.userId !== authUser.id.toString();
        })
        .map(transformEmployeeToContact)
        .filter((contact): contact is Contact => contact !== null); // Remove null contacts (invalid employees)

      // Update state with contacts and cache
      setState((prev) => ({
        ...prev,
        contacts,
        contactsLoading: false,
        contactsError: null,
        employeesCache: {
          data: contacts,
          timestamp: now,
          ttl: CACHE_TTL,
        },
      }));
    } catch (error: any) {
      console.error('Failed to fetch employee contacts:', error);

      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        'Failed to load employee contacts. Please try again.';

      setState((prev) => ({
        ...prev,
        contacts: [],
        contactsLoading: false,
        contactsError: errorMessage,
      }));
    }
  }, [authUser, transformEmployeeToContact, state.employeesCache]);

  /**
   * ========================================================================
   * LOAD CONVERSATION - FETCHES FULL MESSAGE HISTORY FOR A SPECIFIC CHAT
   * ========================================================================
   *
   * This function handles loading complete conversation history between the current
   * user and another user. It's triggered when:
   * - User clicks on a chat in the sidebar
   * - User navigates to a specific chat URL
   * - Components need to refresh conversation data
   *
   * KEY BEHAVIORS:
   * - Fetches full bidirectional message history via API
   * - Automatically marks unread messages as read when conversation opens
   * - Refreshes chat list after marking messages as read to update UI
   * - Handles loading states and error conditions gracefully
   *
   * OPTIMISTIC UPDATE PROTECTION:
   * Uses recentMessageSentRef to prevent loading stale data immediately after
   * sending a message, which would overwrite the optimistic UI update.
   *
   * @param receiverId - ID of the other user in the conversation
   */
  const loadConversation = useCallback(
    async (receiverId: string) => {
      if (!authUser) return;

      // SAFEGUARD: Don't load conversation immediately after sending a message
      // This prevents overwriting optimistic updates with potentially stale API data
      if (recentMessageSentRef.current === receiverId) {
        return;
      }

      try {
        // Set conversation loading state
        setState((prev) => ({
          ...prev,
          activeConversation: {
            ...prev.activeConversation,
            loading: true,
            error: null,
          },
        }));

        // Get full conversation messages between current user and receiverId
        const messages = await chatApiService.getConversation(receiverId, { limit: 50 });

        // Transform messages
        const transformedMessages = messages.map(transformMessage);

        setState((prev) => {
          // TARGETED FIX: Preserve optimistic messages (temp IDs) when loading conversation
          const existingOptimisticMessages = prev.activeConversation.messages.filter((msg) =>
            msg.id.startsWith('temp-'),
          );

          // Merge: existing optimistic messages + fresh API messages (deduplicated)
          const mergedMessages = [
            ...existingOptimisticMessages,
            ...transformedMessages.filter(
              (apiMsg) =>
                !existingOptimisticMessages.some(
                  (optMsg) =>
                    optMsg.content === apiMsg.content &&
                    Math.abs(
                      new Date(optMsg.createdAt).getTime() - new Date(apiMsg.createdAt).getTime(),
                    ) < 5000,
                ),
            ),
          ].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

          // Find or create chat object for this conversation using current state
          let chat = prev.chatList.find((c) => c.id === receiverId);
          if (!chat && messages.length > 0) {
            // Create chat from messages if not in list
            const latestMessage = messages[messages.length - 1];
            chat = {
              id: receiverId,
              contactIds: [receiverId, authUser.id.toString()],
              unreadCount: messages.filter(
                (m) => m.receiverId === authUser.id.toString() && !m.isRead,
              ).length,
              muted: false,
              lastMessage: latestMessage.content,
              lastMessageAt: latestMessage.createdAt || new Date().toISOString(),
              messages: transformedMessages,
            };
          }

          return {
            ...prev,
            activeConversation: {
              chat: chat || null,
              messages: mergedMessages, // FIXED: Use merged messages instead of overwriting
              loading: false,
              error: null,
            },
          };
        });

        // Auto-mark messages as read when conversation is opened
        // Find unread messages sent TO the current user
        const unreadMessages = transformedMessages.filter(
          (msg) => !msg.isRead && msg.receiverId === authUser.id.toString(),
        );

        if (unreadMessages.length > 0) {
          console.log('📖 Auto-marking messages as read:', { 
            conversationId: receiverId, 
            unreadCount: unreadMessages.length 
          });

          // Mark messages as read via API
          const markPromises = unreadMessages.map((msg) =>
            chatApiService.markAsRead(msg.id).catch((error) => {
              console.error(`Failed to auto-mark message ${msg.id} as read:`, error);
            }),
          );

          // Refresh chat list after marking messages as read
          Promise.allSettled(markPromises).then(() => {
            console.log('✅ All messages marked as read, refreshing chat list');
            fetchChatList();
          });
        }
      } catch (error: any) {
        console.error('Failed to load conversation:', error);
        setState((prev) => ({
          ...prev,
          activeConversation: {
            ...prev.activeConversation,
            chat: null,
            messages: [],
            loading: false,
            error: error.message || 'Failed to load conversation',
          },
        }));
      }
    },
    [authUser, transformMessage],
  );

  // Initialize data on mount
  useEffect(() => {
    if (authUser && employeeDetails) {
      // Calculate userProfile inside effect to avoid dependency issues
      const currentUserProfile = createUserProfile();
      setState((prev) => ({ ...prev, user: currentUserProfile }));
      fetchChatList();
      fetchContacts();
    }
  }, [authUser?.id, employeeDetails?.email]); // Only primitive stable values

  // Action functions
  const refreshChatList = useCallback(async () => {
    await fetchChatList();
  }, [fetchChatList]);

  const refreshContacts = useCallback(async () => {
    await fetchContacts();
  }, [fetchContacts]);

  const refreshActiveConversation = useCallback(async () => {
    if (state.activeConversation.chat) {
      await loadConversation(state.activeConversation.chat.id);
    }
  }, [state.activeConversation.chat, loadConversation]);

  const markAsRead = useCallback(
    async (conversationId: string) => {
      if (!state.user?.id) return;

      try {
        console.log('🔄 Manual markAsRead called for:', conversationId);

        // Get unread messages for this conversation
        const conversation =
          state.activeConversation.chat?.id === conversationId ? state.activeConversation : null;

        if (conversation?.messages) {
          // Find unread messages that were sent TO the current user
          const unreadMessages = conversation.messages.filter(
            (msg) => !msg.isRead && msg.receiverId === state.user!.id,
          );

          if (unreadMessages.length > 0) {
            console.log('📖 Manual marking messages as read:', { 
              conversationId, 
              unreadCount: unreadMessages.length 
            });

            // Mark each unread message as read via API
            const markPromises = unreadMessages.map((msg) =>
              chatApiService.markAsRead(msg.id).catch((error) => {
                console.error(`Failed to mark message ${msg.id} as read:`, error);
                // Don't throw to prevent blocking other messages
              }),
            );

            // Refresh chat list after marking messages as read
            Promise.allSettled(markPromises).then(() => {
              console.log('✅ Manual mark as read complete, refreshing chat list');
              fetchChatList();
            });
          }
        }
      } catch (error) {
        console.error('Failed to mark conversation as read:', error);
      }
    },
    [state.user?.id, state.activeConversation, fetchChatList],
  );

  const addMessage = useCallback(
    (message: Message) => {
      // Message deduplication - check if we've already processed this message
      if (processedIdsRef.current.has(message.id)) {
        return;
      }

      // Add message ID to processed set immediately to prevent race conditions
      processedIdsRef.current.add(message.id);

      // Check if this message belongs to the active conversation and conversations
      const currentUserId = authUser?.id.toString();

      // TARGETED FIX: Determine which conversation this message belongs to
      const messageConversationId =
        String(message.senderId) === currentUserId
          ? String(message.receiverId) // I sent to this person
          : String(message.senderId); // This person sent to me

      // Store notification data to trigger outside setState
      let notificationData: any = null;

      setState((prevState) => {
        // SAFEGUARD: If this is a message I sent, mark it to prevent loadConversation overwrites
        if (String(message.senderId) === currentUserId) {
          recentMessageSentRef.current = messageConversationId;
          // Clear the flag after a short delay to allow normal loading later
          setTimeout(() => {
            recentMessageSentRef.current = null;
          }, 2000);
        }

        // FIXED: Simply check if this message belongs to the currently active conversation
        const isActiveConversation =
          prevState.activeConversation.chat?.id === messageConversationId;

        // PREPARE NOTIFICATION: Prepare notification data for incoming messages (not sent by current user)
        if (String(message.senderId) !== currentUserId) {
          // Find the sender contact info for the notification
          const senderContact = prevState.contacts.find(
            (contact) => contact.id === message.senderId,
          );
          const senderName = senderContact?.name || `User ${message.senderId}`;
          const senderAvatar = senderContact?.avatar || undefined;

          notificationData = {
            id: message.id,
            senderName,
            senderAvatar,
            message: message.content,
            timestamp: message.createdAt || new Date().toISOString(),
            chatId: messageConversationId,
          };
        }

        // Update chat list with new message
        const updatedChatList = prevState.chatList.map((chat) => {
          const isThisChat = chat.id === messageConversationId;

          if (isThisChat) {
            return {
              ...chat,
              messages: [...chat.messages, message],
              lastMessage: message.content,
              lastMessageAt: message.createdAt || new Date().toISOString(),
              unreadCount:
                String(message.senderId) !== currentUserId
                  ? chat.unreadCount + 1
                  : chat.unreadCount,
            };
          }
          return chat;
        });

        // Update active conversation if relevant
        const updatedActiveConversation = isActiveConversation
          ? {
              ...prevState.activeConversation,
              messages: [...prevState.activeConversation.messages, message],
            }
          : prevState.activeConversation;

        return {
          ...prevState,
          chatList: updatedChatList,
          activeConversation: updatedActiveConversation,
        };
      });

      // TRIGGER NOTIFICATION: Show chat notification outside setState to avoid render cycle error
      if (notificationData) {
        setTimeout(() => {
          showChatNotification(notificationData);
        }, 0);
      }
    },
    [authUser, showChatNotification],
  );

  /**
   * Start a new conversation with a contact
   * Creates an empty chat if one doesn't exist and sets it as active
   */
  const startNewConversation = useCallback(
    (contactId: string): string => {
      if (!authUser || !contactId) {
        throw new Error('Cannot start conversation: Missing user or contact ID');
      }

      let resultChatId = '';

      setState((prev) => {
        // Check if conversation already exists using current state
        const existingChat = prev.chatList.find((chat) => chat.contactIds.includes(contactId));

        if (existingChat) {
          // Conversation exists, just set it as active
          resultChatId = existingChat.id;

          return {
            ...prev,
            activeConversation: {
              chat: existingChat,
              messages: existingChat.messages,
              loading: false,
              error: null,
            },
          };
        }

        // Create new empty conversation
        const newChat: Chat = {
          id: contactId, // Use contactId as chat ID for new conversations
          contactIds: [contactId, authUser.id.toString()],
          unreadCount: 0,
          muted: false,
          lastMessage: '',
          lastMessageAt: new Date().toISOString(),
          messages: [],
        };

        resultChatId = newChat.id;

        // Add to chat list and set as active conversation
        return {
          ...prev,
          chatList: [newChat, ...prev.chatList], // Add to beginning of list
          activeConversation: {
            chat: newChat,
            messages: [],
            loading: false,
            error: null,
          },
        };
      });

      return resultChatId;
    },
    [authUser],
  );

  // ========================================================================
  // WEBSOCKET INTEGRATION - REAL-TIME MESSAGE HANDLING
  // ========================================================================

  // Centralized WebSocket message handling for real-time chat functionality
  useEffect(() => {
    if (!authUser || !webSocket.isConnected) return;

    // Single subscription point for all incoming chat messages
    // This handles real-time message delivery from other users
    const messageSubscription = webSocket.subscribe('chat:message', (event) => {
      const message = event.data;

      // Transform WebSocket message format to our frontend Message type
      // This ensures consistency between API and WebSocket message formats
      const transformedMessage: Message = {
        id: message.id,
        senderId: message.senderId,
        receiverId: message.receiverId,
        content: message.content,
        createdAt: message.createdAt,
        deliveredAt: message.deliveredAt ? new Date(message.deliveredAt) : undefined,
        readAt: message.readAt ? new Date(message.readAt) : undefined,
        isRead: message.isRead,
        isEdited: message.isEdited,
      };

      // Process incoming message with deduplication and state updates
      addMessage(transformedMessage);
    });

    // Cleanup on unmount
    return () => {
      webSocket.unsubscribe(messageSubscription);
    };
  }, [authUser, webSocket.isConnected, addMessage]);

  return {
    ...state,
    refreshChatList,
    refreshContacts,
    loadConversation,
    refreshActiveConversation,
    markAsRead,
    addMessage,
    startNewConversation,
  };
}
