/*!
 * PSPDFKit for Web 2024.8.2 (https://pspdfkit.com/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://pspdfkit.com/acknowledgements/web/
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PSPDFKit=t():e.PSPDFKit=t()}(globalThis,(()=>(()=>{var e={24601:(e,t,n)=>{var r=n(78420),o=n(13838),i=TypeError;e.exports=function(e){if(r(e))return e;throw i(o(e)+" is not a function")}},73938:(e,t,n)=>{var r=n(65335),o=String,i=TypeError;e.exports=function(e){if(r(e))return e;throw i(o(e)+" is not an object")}},58186:(e,t,n)=>{var r=n(5476),o=n(6539),i=n(23493),a=function(e){return function(t,n,a){var s,c=r(t),l=i(c),u=o(a,l);if(e&&n!=n){for(;l>u;)if((s=c[u++])!=s)return!0}else for(;l>u;u++)if((e||u in c)&&c[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},29609:(e,t,n)=>{var r=n(30281);e.exports=r([].slice)},18569:(e,t,n)=>{var r=n(30281),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},84361:(e,t,n)=>{var r=n(36490),o=n(15816),i=n(97632),a=n(43610);e.exports=function(e,t,n){for(var s=o(t),c=a.f,l=i.f,u=0;u<s.length;u++){var d=s[u];r(e,d)||n&&r(n,d)||c(e,d,l(t,d))}}},97712:(e,t,n)=>{var r=n(65077),o=n(43610),i=n(66843);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},66843:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7485:(e,t,n)=>{var r=n(78420),o=n(43610),i=n(48218),a=n(59430);e.exports=function(e,t,n,s){s||(s={});var c=s.enumerable,l=void 0!==s.name?s.name:t;if(r(n)&&i(n,l,s),s.global)c?e[t]=n:a(t,n);else{try{s.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=n:o.f(e,t,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return e}},59430:(e,t,n)=>{var r=n(30200),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},65077:(e,t,n)=>{var r=n(92074);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},96568:e=>{var t="object"==typeof document&&document.all,n=void 0===t&&void 0!==t;e.exports={all:t,IS_HTMLDDA:n}},23262:(e,t,n)=>{var r=n(30200),o=n(65335),i=r.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},16874:e=>{e.exports="function"==typeof Bun&&Bun&&"string"==typeof Bun.version},62050:(e,t,n)=>{var r=n(47061);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},75223:(e,t,n)=>{var r=n(18569),o=n(30200);e.exports="process"==r(o.process)},47061:(e,t,n)=>{var r=n(56492);e.exports=r("navigator","userAgent")||""},6845:(e,t,n)=>{var r,o,i=n(30200),a=n(47061),s=i.process,c=i.Deno,l=s&&s.versions||c&&c.version,u=l&&l.v8;u&&(o=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(o=+r[1]),e.exports=o},30290:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},51605:(e,t,n)=>{var r=n(30200),o=n(97632).f,i=n(97712),a=n(7485),s=n(59430),c=n(84361),l=n(84977);e.exports=function(e,t){var n,u,d,p,f,m=e.target,g=e.global,h=e.stat;if(n=g?r:h?r[m]||s(m,{}):(r[m]||{}).prototype)for(u in t){if(p=t[u],d=e.dontCallGetSet?(f=o(n,u))&&f.value:n[u],!l(g?u:m+(h?".":"#")+u,e.forced)&&void 0!==d){if(typeof p==typeof d)continue;c(p,d)}(e.sham||d&&d.sham)&&i(p,"sham",!0),a(n,u,p,e)}}},92074:e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},99070:(e,t,n)=>{var r=n(38823),o=Function.prototype,i=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(i):function(){return a.apply(i,arguments)})},46885:(e,t,n)=>{var r=n(43091),o=n(24601),i=n(38823),a=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?a(e,t):function(){return e.apply(t,arguments)}}},38823:(e,t,n)=>{var r=n(92074);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},72368:(e,t,n)=>{var r=n(38823),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},12071:(e,t,n)=>{var r=n(65077),o=n(36490),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=o(i,"name"),c=s&&"something"===function(){}.name,l=s&&(!r||r&&a(i,"name").configurable);e.exports={EXISTS:s,PROPER:c,CONFIGURABLE:l}},43091:(e,t,n)=>{var r=n(18569),o=n(30281);e.exports=function(e){if("Function"===r(e))return o(e)}},30281:(e,t,n)=>{var r=n(38823),o=Function.prototype,i=o.call,a=r&&o.bind.bind(i,i);e.exports=r?a:function(e){return function(){return i.apply(e,arguments)}}},56492:(e,t,n)=>{var r=n(30200),o=n(78420),i=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?i(r[e]):r[e]&&r[e][t]}},46457:(e,t,n)=>{var r=n(24601),o=n(88406);e.exports=function(e,t){var n=e[t];return o(n)?void 0:r(n)}},30200:(e,t,n)=>{var r=function(e){return e&&e.Math==Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||function(){return this}()||Function("return this")()},36490:(e,t,n)=>{var r=n(30281),o=n(92612),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},67708:e=>{e.exports={}},28890:(e,t,n)=>{var r=n(56492);e.exports=r("document","documentElement")},87694:(e,t,n)=>{var r=n(65077),o=n(92074),i=n(23262);e.exports=!r&&!o((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},48664:(e,t,n)=>{var r=n(30281),o=n(92074),i=n(18569),a=Object,s=r("".split);e.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"==i(e)?s(e,""):a(e)}:a},69965:(e,t,n)=>{var r=n(30281),o=n(78420),i=n(49310),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},99206:(e,t,n)=>{var r,o,i,a=n(8369),s=n(30200),c=n(65335),l=n(97712),u=n(36490),d=n(49310),p=n(25904),f=n(67708),m="Object already initialized",g=s.TypeError,h=s.WeakMap;if(a||d.state){var y=d.state||(d.state=new h);y.get=y.get,y.has=y.has,y.set=y.set,r=function(e,t){if(y.has(e))throw g(m);return t.facade=e,y.set(e,t),t},o=function(e){return y.get(e)||{}},i=function(e){return y.has(e)}}else{var b=p("state");f[b]=!0,r=function(e,t){if(u(e,b))throw g(m);return t.facade=e,l(e,b,t),t},o=function(e){return u(e,b)?e[b]:{}},i=function(e){return u(e,b)}}e.exports={set:r,get:o,has:i,enforce:function(e){return i(e)?o(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!c(t)||(n=o(t)).type!==e)throw g("Incompatible receiver, "+e+" required");return n}}}},78420:(e,t,n)=>{var r=n(96568),o=r.all;e.exports=r.IS_HTMLDDA?function(e){return"function"==typeof e||e===o}:function(e){return"function"==typeof e}},84977:(e,t,n)=>{var r=n(92074),o=n(78420),i=/#|\.prototype\./,a=function(e,t){var n=c[s(e)];return n==u||n!=l&&(o(t)?r(t):!!t)},s=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},88406:e=>{e.exports=function(e){return null==e}},65335:(e,t,n)=>{var r=n(78420),o=n(96568),i=o.all;e.exports=o.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:r(e)||e===i}:function(e){return"object"==typeof e?null!==e:r(e)}},6926:e=>{e.exports=!1},32328:(e,t,n)=>{var r=n(56492),o=n(78420),i=n(47658),a=n(5225),s=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return o(t)&&i(t.prototype,s(e))}},23493:(e,t,n)=>{var r=n(33747);e.exports=function(e){return r(e.length)}},48218:(e,t,n)=>{var r=n(92074),o=n(78420),i=n(36490),a=n(65077),s=n(12071).CONFIGURABLE,c=n(69965),l=n(99206),u=l.enforce,d=l.get,p=Object.defineProperty,f=a&&!r((function(){return 8!==p((function(){}),"length",{value:8}).length})),m=String(String).split("String"),g=e.exports=function(e,t,n){"Symbol("===String(t).slice(0,7)&&(t="["+String(t).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!i(e,"name")||s&&e.name!==t)&&(a?p(e,"name",{value:t,configurable:!0}):e.name=t),f&&n&&i(n,"arity")&&e.length!==n.arity&&p(e,"length",{value:n.arity});try{n&&i(n,"constructor")&&n.constructor?a&&p(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=u(e);return i(r,"source")||(r.source=m.join("string"==typeof t?t:"")),e};Function.prototype.toString=g((function(){return o(this)&&d(this).source||c(this)}),"toString")},19830:e=>{var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},43610:(e,t,n)=>{var r=n(65077),o=n(87694),i=n(94491),a=n(73938),s=n(86032),c=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",f="writable";t.f=r?i?function(e,t,n){if(a(e),t=s(t),a(n),"function"==typeof e&&"prototype"===t&&"value"in n&&f in n&&!n.writable){var r=u(e,t);r&&r.writable&&(e[t]=n.value,n={configurable:p in n?n.configurable:r.configurable,enumerable:d in n?n.enumerable:r.enumerable,writable:!1})}return l(e,t,n)}:l:function(e,t,n){if(a(e),t=s(t),a(n),o)try{return l(e,t,n)}catch(e){}if("get"in n||"set"in n)throw c("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},97632:(e,t,n)=>{var r=n(65077),o=n(72368),i=n(9304),a=n(66843),s=n(5476),c=n(86032),l=n(36490),u=n(87694),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=s(e),t=c(t),u)try{return d(e,t)}catch(e){}if(l(e,t))return a(!o(i.f,e,t),e[t])}},64789:(e,t,n)=>{var r=n(16347),o=n(30290).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},18916:(e,t)=>{t.f=Object.getOwnPropertySymbols},47658:(e,t,n)=>{var r=n(30281);e.exports=r({}.isPrototypeOf)},16347:(e,t,n)=>{var r=n(30281),o=n(36490),i=n(5476),a=n(58186).indexOf,s=n(67708),c=r([].push);e.exports=function(e,t){var n,r=i(e),l=0,u=[];for(n in r)!o(s,n)&&o(r,n)&&c(u,n);for(;t.length>l;)o(r,n=t[l++])&&(~a(u,n)||c(u,n));return u}},9304:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},69751:(e,t,n)=>{var r=n(72368),o=n(78420),i=n(65335),a=TypeError;e.exports=function(e,t){var n,s;if("string"===t&&o(n=e.toString)&&!i(s=r(n,e)))return s;if(o(n=e.valueOf)&&!i(s=r(n,e)))return s;if("string"!==t&&o(n=e.toString)&&!i(s=r(n,e)))return s;throw a("Can't convert object to primitive value")}},15816:(e,t,n)=>{var r=n(56492),o=n(30281),i=n(64789),a=n(18916),s=n(73938),c=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(s(e)),n=a.f;return n?c(t,n(e)):t}},71229:(e,t,n)=>{var r=n(88406),o=TypeError;e.exports=function(e){if(r(e))throw o("Can't call method on "+e);return e}},18827:(e,t,n)=>{"use strict";var r,o=n(30200),i=n(99070),a=n(78420),s=n(16874),c=n(47061),l=n(29609),u=n(56589),d=o.Function,p=/MSIE .\./.test(c)||s&&((r=o.Bun.version.split(".")).length<3||0==r[0]&&(r[1]<3||3==r[1]&&0==r[2]));e.exports=function(e,t){var n=t?2:1;return p?function(r,o){var s=u(arguments.length,1)>n,c=a(r)?r:d(r),p=s?l(arguments,n):[],f=s?function(){i(c,this,p)}:c;return t?e(f,o):e(f)}:e}},25904:(e,t,n)=>{var r=n(50002),o=n(50665),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},49310:(e,t,n)=>{var r=n(30200),o=n(59430),i="__core-js_shared__",a=r[i]||o(i,{});e.exports=a},50002:(e,t,n)=>{var r=n(6926),o=n(49310);(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.27.1",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.27.1/LICENSE",source:"https://github.com/zloirock/core-js"})},82072:(e,t,n)=>{var r=n(6845),o=n(92074);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},74922:(e,t,n)=>{var r,o,i,a,s=n(30200),c=n(99070),l=n(46885),u=n(78420),d=n(36490),p=n(92074),f=n(28890),m=n(29609),g=n(23262),h=n(56589),y=n(62050),b=n(75223),w=s.setImmediate,v=s.clearImmediate,_=s.process,O=s.Dispatch,F=s.Function,S=s.MessageChannel,x=s.String,I=0,E={},A="onreadystatechange";try{r=s.location}catch(e){}var j=function(e){if(d(E,e)){var t=E[e];delete E[e],t()}},D=function(e){return function(){j(e)}},P=function(e){j(e.data)},k=function(e){s.postMessage(x(e),r.protocol+"//"+r.host)};w&&v||(w=function(e){h(arguments.length,1);var t=u(e)?e:F(e),n=m(arguments,1);return E[++I]=function(){c(t,void 0,n)},o(I),I},v=function(e){delete E[e]},b?o=function(e){_.nextTick(D(e))}:O&&O.now?o=function(e){O.now(D(e))}:S&&!y?(a=(i=new S).port2,i.port1.onmessage=P,o=l(a.postMessage,a)):s.addEventListener&&u(s.postMessage)&&!s.importScripts&&r&&"file:"!==r.protocol&&!p(k)?(o=k,s.addEventListener("message",P,!1)):o=A in g("script")?function(e){f.appendChild(g("script")).onreadystatechange=function(){f.removeChild(this),j(e)}}:function(e){setTimeout(D(e),0)}),e.exports={set:w,clear:v}},6539:(e,t,n)=>{var r=n(79328),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},5476:(e,t,n)=>{var r=n(48664),o=n(71229);e.exports=function(e){return r(o(e))}},79328:(e,t,n)=>{var r=n(19830);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},33747:(e,t,n)=>{var r=n(79328),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},92612:(e,t,n)=>{var r=n(71229),o=Object;e.exports=function(e){return o(r(e))}},50874:(e,t,n)=>{var r=n(72368),o=n(65335),i=n(32328),a=n(46457),s=n(69751),c=n(31602),l=TypeError,u=c("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var n,c=a(e,u);if(c){if(void 0===t&&(t="default"),n=r(c,e,t),!o(n)||i(n))return n;throw l("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},86032:(e,t,n)=>{var r=n(50874),o=n(32328);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},13838:e=>{var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},50665:(e,t,n)=>{var r=n(30281),o=0,i=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},5225:(e,t,n)=>{var r=n(82072);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},94491:(e,t,n)=>{var r=n(65077),o=n(92074);e.exports=r&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},56589:e=>{var t=TypeError;e.exports=function(e,n){if(e<n)throw t("Not enough arguments");return e}},8369:(e,t,n)=>{var r=n(30200),o=n(78420),i=r.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},31602:(e,t,n)=>{var r=n(30200),o=n(50002),i=n(36490),a=n(50665),s=n(82072),c=n(5225),l=o("wks"),u=r.Symbol,d=u&&u.for,p=c?u:u&&u.withoutSetter||a;e.exports=function(e){if(!i(l,e)||!s&&"string"!=typeof l[e]){var t="Symbol."+e;s&&i(u,e)?l[e]=u[e]:l[e]=c&&d?d(t):p(t)}return l[e]}},61857:(e,t,n)=>{var r=n(51605),o=n(30200),i=n(74922).clear;r({global:!0,bind:!0,enumerable:!0,forced:o.clearImmediate!==i},{clearImmediate:i})},75417:(e,t,n)=>{n(61857),n(19708)},19708:(e,t,n)=>{var r=n(51605),o=n(30200),i=n(74922).set,a=n(18827),s=o.setImmediate?a(i,!1):i;r({global:!0,bind:!0,enumerable:!0,forced:o.setImmediate!==s},{setImmediate:s})},55480:()=>{},97967:e=>{e.exports="d0817ae63ff8c80d"},80658:e=>{e.exports="ddcc88b7de1cd562"}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);return(()=>{"use strict";n(75417);"object"==typeof window&&(window._babelPolyfill=!1);const e=function e(t){let n;return n=t instanceof Error?t:new Error(t),Object.setPrototypeOf(n,e.prototype),n};e.prototype=Object.create(Error.prototype,{name:{value:"PSPDFKitError",enumerable:!1}});const t=e;function r(e,n){if(!e)throw new t(`Assertion failed: ${n||"Condition not met"}\n\nFor further assistance, please go to: https://pspdfkit.com/support/request`)}function o(e){console.log(e)}function i(){console.warn(...arguments)}function a(e){console.error(e)}["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])'].join(",");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){var t=function(e,t){if("object"!==s(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===s(t)?t:String(t)}function l(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}new WeakMap;const u="function"==typeof Buffer,d=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),p=(e=>{let t={};return e.forEach(((e,n)=>t[e]=n)),t})(d),f=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,m=String.fromCharCode.bind(String),g="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),h=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),y=e=>{let t,n,r,o,i="";const a=e.length%3;for(let a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|o,i+=d[t>>18&63]+d[t>>12&63]+d[t>>6&63]+d[63&t]}return a?i.slice(0,a-3)+"===".substring(a):i},b=e=>{if(e=e.replace(/\s+/g,""),!f.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,n,r,o="";for(let i=0;i<e.length;)t=p[e.charAt(i++)]<<18|p[e.charAt(i++)]<<12|(n=p[e.charAt(i++)])<<6|(r=p[e.charAt(i++)]),o+=64===n?m(t>>16&255):64===r?m(t>>16&255,t>>8&255):m(t>>16&255,t>>8&255,255&t);return o},w="function"==typeof atob?e=>atob(h(e)):u?e=>Buffer.from(e,"base64").toString("binary"):b,v=u?e=>g(Buffer.from(e,"base64")):e=>g(w(e).split("").map((e=>e.charCodeAt(0)))),_=e=>v(O(e)),O=e=>h(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),F=_;function S(e){return{name:e.name,scale:{unitFrom:e.scale.unitFrom,unitTo:e.scale.unitTo,from:Number(e.scale.fromValue),to:Number(e.scale.toValue)},precision:e.precision}}var x=n(97967),I=n.n(x),E=n(80658),A=n.n(E),j=n(55480),D=n.n(j);let P;P="object"==typeof process&&"object"==typeof process.versions&&void 0!==process.versions.node?D():function(e){return"object"==typeof window?new Promise(((t,n)=>{const o=document.createElement("script");o.type="text/javascript",o.async=!0,o.onload=()=>t(window.PSPDFModuleInit),o.onerror=n,o.src=e;const{documentElement:i}=document;r(i),i.appendChild(o)})):(self.importScripts(e),Promise.resolve(self.PSPDFModuleInit))};const k=P,C="pspdfkit-lib/",T=`${C}pspdfkit${"-"+A()}.wasm.js`,N=`${C}pspdfkit${"-"+I()}.wasm`;function B(e,t){let n;if("object"==typeof e){n=e.wasm;return{wasmBinaryFile:N,locateFile:e=>e,wasmBinary:n}}const r={wasmBinaryFile:e+N,locateFile:e=>e,wasmBinary:n};{const e=fetch(r.wasmBinaryFile,{credentials:"same-origin"}),n=13800063;e.then((e=>{const t=e.headers.get("content-length");return t&&parseInt(t)>=n&&i("The WASM binary file is being served without compression. Due to its size, it's recommended to configure the server so application/wasm files are served with compression.\n\nFind more details in our Standalone performance guides: https://pspdfkit.com/guides/web/best-practices/caching-on-the-web/#gzip-and-brotli"),e})).catch((e=>{throw new Error(`Failed to load WASM binary file: ${e}`)})),r.instantiateWasm=(n,i)=>((async()=>{o(`Start ${r.wasmBinaryFile} download.`);const s=Date.now();let c,l;const u=!t&&"function"==typeof WebAssembly.instantiateStreaming;function d(){return e.then((e=>{if(!e.ok)throw new Error(`Error loading ${r.wasmBinaryFile}: ${e.statusText}`);return e.arrayBuffer()})).then((e=>(l=Date.now(),o(`Download complete, took: ${l-s}ms`),WebAssembly.instantiate(new Uint8Array(e),n))))}c=u?WebAssembly.instantiateStreaming(e,n).then((e=>(l=Date.now(),o(`Download and Instantiation complete, took: ${Date.now()-s}ms`),e))).catch((e=>{if(/mime.*type/i.test(e.message))return a(e.message),null;throw e})):d();let p=await c;null===p&&(a("Streaming instantiation failed! Falling back to classic instantiation. This might result in slower initialization time therefore we highly recommend to follow the troubleshooting instructions in our guides to fix this error: https://pspdfkit.com/guides/web/current/troubleshooting/common-issues/#response-has-unsupported-mime-type-error."),p=await d()),!u&&l&&o(`Compilation and Instantiation complete, took: ${Date.now()-l}ms`),i(p.instance,p.module)})(),{})}return r}function $(e){e.PSPDFLoggingServices={error(e,t){a(`[${e}] ${t}`)},warn(e,t){i(`[${e}] ${t}`)},info(e,t){0},debug(e,t){0},trace(e,t){0}},e.PSPDFUnicodeServices={stripDiacritics(e){const t={Á:"A",Ă:"A",Ắ:"A",Ặ:"A",Ằ:"A",Ẳ:"A",Ẵ:"A",Ǎ:"A",Â:"A",Ấ:"A",Ậ:"A",Ầ:"A",Ẩ:"A",Ẫ:"A",Ä:"A",Ǟ:"A",Ȧ:"A",Ǡ:"A",Ạ:"A",Ȁ:"A",À:"A",Ả:"A",Ȃ:"A",Ā:"A",Ą:"A",Å:"A",Ǻ:"A",Ḁ:"A",Ⱥ:"A",Ã:"A",Ꜳ:"AA",Æ:"AE",Ǽ:"AE",Ǣ:"AE",Ꜵ:"AO",Ꜷ:"AU",Ꜹ:"AV",Ꜻ:"AV",Ꜽ:"AY",Ḃ:"B",Ḅ:"B",Ɓ:"B",Ḇ:"B",Ƀ:"B",Ƃ:"B",Ć:"C",Č:"C",Ç:"C",Ḉ:"C",Ĉ:"C",Ċ:"C",Ƈ:"C",Ȼ:"C",Ď:"D",Ḑ:"D",Ḓ:"D",Ḋ:"D",Ḍ:"D",Ɗ:"D",Ḏ:"D",ǲ:"D",ǅ:"D",Đ:"D",Ƌ:"D",Ǳ:"DZ",Ǆ:"DZ",É:"E",Ĕ:"E",Ě:"E",Ȩ:"E",Ḝ:"E",Ê:"E",Ế:"E",Ệ:"E",Ề:"E",Ể:"E",Ễ:"E",Ḙ:"E",Ë:"E",Ė:"E",Ẹ:"E",Ȅ:"E",È:"E",Ẻ:"E",Ȇ:"E",Ē:"E",Ḗ:"E",Ḕ:"E",Ę:"E",Ɇ:"E",Ẽ:"E",Ḛ:"E",Ꝫ:"ET",Ḟ:"F",Ƒ:"F",Ǵ:"G",Ğ:"G",Ǧ:"G",Ģ:"G",Ĝ:"G",Ġ:"G",Ɠ:"G",Ḡ:"G",Ǥ:"G",Ḫ:"H",Ȟ:"H",Ḩ:"H",Ĥ:"H",Ⱨ:"H",Ḧ:"H",Ḣ:"H",Ḥ:"H",Ħ:"H",Í:"I",Ĭ:"I",Ǐ:"I",Î:"I",Ï:"I",Ḯ:"I",İ:"I",Ị:"I",Ȉ:"I",Ì:"I",Ỉ:"I",Ȋ:"I",Ī:"I",Į:"I",Ɨ:"I",Ĩ:"I",Ḭ:"I",Ꝺ:"D",Ꝼ:"F",Ᵹ:"G",Ꞃ:"R",Ꞅ:"S",Ꞇ:"T",Ꝭ:"IS",Ĵ:"J",Ɉ:"J",Ḱ:"K",Ǩ:"K",Ķ:"K",Ⱪ:"K",Ꝃ:"K",Ḳ:"K",Ƙ:"K",Ḵ:"K",Ꝁ:"K",Ꝅ:"K",Ĺ:"L",Ƚ:"L",Ľ:"L",Ļ:"L",Ḽ:"L",Ḷ:"L",Ḹ:"L",Ⱡ:"L",Ꝉ:"L",Ḻ:"L",Ŀ:"L",Ɫ:"L",ǈ:"L",Ł:"L",Ǉ:"LJ",Ḿ:"M",Ṁ:"M",Ṃ:"M",Ɱ:"M",Ń:"N",Ň:"N",Ņ:"N",Ṋ:"N",Ṅ:"N",Ṇ:"N",Ǹ:"N",Ɲ:"N",Ṉ:"N",Ƞ:"N",ǋ:"N",Ñ:"N",Ǌ:"NJ",Ó:"O",Ŏ:"O",Ǒ:"O",Ô:"O",Ố:"O",Ộ:"O",Ồ:"O",Ổ:"O",Ỗ:"O",Ö:"O",Ȫ:"O",Ȯ:"O",Ȱ:"O",Ọ:"O",Ő:"O",Ȍ:"O",Ò:"O",Ỏ:"O",Ơ:"O",Ớ:"O",Ợ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ȏ:"O",Ꝋ:"O",Ꝍ:"O",Ō:"O",Ṓ:"O",Ṑ:"O",Ɵ:"O",Ǫ:"O",Ǭ:"O",Ø:"O",Ǿ:"O",Õ:"O",Ṍ:"O",Ṏ:"O",Ȭ:"O",Ƣ:"OI",Ꝏ:"OO",Ɛ:"E",Ɔ:"O",Ȣ:"OU",Ṕ:"P",Ṗ:"P",Ꝓ:"P",Ƥ:"P",Ꝕ:"P",Ᵽ:"P",Ꝑ:"P",Ꝙ:"Q",Ꝗ:"Q",Ŕ:"R",Ř:"R",Ŗ:"R",Ṙ:"R",Ṛ:"R",Ṝ:"R",Ȑ:"R",Ȓ:"R",Ṟ:"R",Ɍ:"R",Ɽ:"R",Ꜿ:"C",Ǝ:"E",Ś:"S",Ṥ:"S",Š:"S",Ṧ:"S",Ş:"S",Ŝ:"S",Ș:"S",Ṡ:"S",Ṣ:"S",Ṩ:"S",Ť:"T",Ţ:"T",Ṱ:"T",Ț:"T",Ⱦ:"T",Ṫ:"T",Ṭ:"T",Ƭ:"T",Ṯ:"T",Ʈ:"T",Ŧ:"T",Ɐ:"A",Ꞁ:"L",Ɯ:"M",Ʌ:"V",Ꜩ:"TZ",Ú:"U",Ŭ:"U",Ǔ:"U",Û:"U",Ṷ:"U",Ü:"U",Ǘ:"U",Ǚ:"U",Ǜ:"U",Ǖ:"U",Ṳ:"U",Ụ:"U",Ű:"U",Ȕ:"U",Ù:"U",Ủ:"U",Ư:"U",Ứ:"U",Ự:"U",Ừ:"U",Ử:"U",Ữ:"U",Ȗ:"U",Ū:"U",Ṻ:"U",Ų:"U",Ů:"U",Ũ:"U",Ṹ:"U",Ṵ:"U",Ꝟ:"V",Ṿ:"V",Ʋ:"V",Ṽ:"V",Ꝡ:"VY",Ẃ:"W",Ŵ:"W",Ẅ:"W",Ẇ:"W",Ẉ:"W",Ẁ:"W",Ⱳ:"W",Ẍ:"X",Ẋ:"X",Ý:"Y",Ŷ:"Y",Ÿ:"Y",Ẏ:"Y",Ỵ:"Y",Ỳ:"Y",Ƴ:"Y",Ỷ:"Y",Ỿ:"Y",Ȳ:"Y",Ɏ:"Y",Ỹ:"Y",Ź:"Z",Ž:"Z",Ẑ:"Z",Ⱬ:"Z",Ż:"Z",Ẓ:"Z",Ȥ:"Z",Ẕ:"Z",Ƶ:"Z",Ĳ:"IJ",Œ:"OE",ᴀ:"A",ᴁ:"AE",ʙ:"B",ᴃ:"B",ᴄ:"C",ᴅ:"D",ᴇ:"E",ꜰ:"F",ɢ:"G",ʛ:"G",ʜ:"H",ɪ:"I",ʁ:"R",ᴊ:"J",ᴋ:"K",ʟ:"L",ᴌ:"L",ᴍ:"M",ɴ:"N",ᴏ:"O",ɶ:"OE",ᴐ:"O",ᴕ:"OU",ᴘ:"P",ʀ:"R",ᴎ:"N",ᴙ:"R",ꜱ:"S",ᴛ:"T",ⱻ:"E",ᴚ:"R",ᴜ:"U",ᴠ:"V",ᴡ:"W",ʏ:"Y",ᴢ:"Z",á:"a",ă:"a",ắ:"a",ặ:"a",ằ:"a",ẳ:"a",ẵ:"a",ǎ:"a",â:"a",ấ:"a",ậ:"a",ầ:"a",ẩ:"a",ẫ:"a",ä:"a",ǟ:"a",ȧ:"a",ǡ:"a",ạ:"a",ȁ:"a",à:"a",ả:"a",ȃ:"a",ā:"a",ą:"a",ᶏ:"a",ẚ:"a",å:"a",ǻ:"a",ḁ:"a",ⱥ:"a",ã:"a",ꜳ:"aa",æ:"ae",ǽ:"ae",ǣ:"ae",ꜵ:"ao",ꜷ:"au",ꜹ:"av",ꜻ:"av",ꜽ:"ay",ḃ:"b",ḅ:"b",ɓ:"b",ḇ:"b",ᵬ:"b",ᶀ:"b",ƀ:"b",ƃ:"b",ɵ:"o",ć:"c",č:"c",ç:"c",ḉ:"c",ĉ:"c",ɕ:"c",ċ:"c",ƈ:"c",ȼ:"c",ď:"d",ḑ:"d",ḓ:"d",ȡ:"d",ḋ:"d",ḍ:"d",ɗ:"d",ᶑ:"d",ḏ:"d",ᵭ:"d",ᶁ:"d",đ:"d",ɖ:"d",ƌ:"d",ı:"i",ȷ:"j",ɟ:"j",ʄ:"j",ǳ:"dz",ǆ:"dz",é:"e",ĕ:"e",ě:"e",ȩ:"e",ḝ:"e",ê:"e",ế:"e",ệ:"e",ề:"e",ể:"e",ễ:"e",ḙ:"e",ë:"e",ė:"e",ẹ:"e",ȅ:"e",è:"e",ẻ:"e",ȇ:"e",ē:"e",ḗ:"e",ḕ:"e",ⱸ:"e",ę:"e",ᶒ:"e",ɇ:"e",ẽ:"e",ḛ:"e",ꝫ:"et",ḟ:"f",ƒ:"f",ᵮ:"f",ᶂ:"f",ǵ:"g",ğ:"g",ǧ:"g",ģ:"g",ĝ:"g",ġ:"g",ɠ:"g",ḡ:"g",ᶃ:"g",ǥ:"g",ḫ:"h",ȟ:"h",ḩ:"h",ĥ:"h",ⱨ:"h",ḧ:"h",ḣ:"h",ḥ:"h",ɦ:"h",ẖ:"h",ħ:"h",ƕ:"hv",í:"i",ĭ:"i",ǐ:"i",î:"i",ï:"i",ḯ:"i",ị:"i",ȉ:"i",ì:"i",ỉ:"i",ȋ:"i",ī:"i",į:"i",ᶖ:"i",ɨ:"i",ĩ:"i",ḭ:"i",ꝺ:"d",ꝼ:"f",ᵹ:"g",ꞃ:"r",ꞅ:"s",ꞇ:"t",ꝭ:"is",ǰ:"j",ĵ:"j",ʝ:"j",ɉ:"j",ḱ:"k",ǩ:"k",ķ:"k",ⱪ:"k",ꝃ:"k",ḳ:"k",ƙ:"k",ḵ:"k",ᶄ:"k",ꝁ:"k",ꝅ:"k",ĺ:"l",ƚ:"l",ɬ:"l",ľ:"l",ļ:"l",ḽ:"l",ȴ:"l",ḷ:"l",ḹ:"l",ⱡ:"l",ꝉ:"l",ḻ:"l",ŀ:"l",ɫ:"l",ᶅ:"l",ɭ:"l",ł:"l",ǉ:"lj",ſ:"s",ẜ:"s",ẛ:"s",ẝ:"s",ḿ:"m",ṁ:"m",ṃ:"m",ɱ:"m",ᵯ:"m",ᶆ:"m",ń:"n",ň:"n",ņ:"n",ṋ:"n",ȵ:"n",ṅ:"n",ṇ:"n",ǹ:"n",ɲ:"n",ṉ:"n",ƞ:"n",ᵰ:"n",ᶇ:"n",ɳ:"n",ñ:"n",ǌ:"nj",ó:"o",ŏ:"o",ǒ:"o",ô:"o",ố:"o",ộ:"o",ồ:"o",ổ:"o",ỗ:"o",ö:"o",ȫ:"o",ȯ:"o",ȱ:"o",ọ:"o",ő:"o",ȍ:"o",ò:"o",ỏ:"o",ơ:"o",ớ:"o",ợ:"o",ờ:"o",ở:"o",ỡ:"o",ȏ:"o",ꝋ:"o",ꝍ:"o",ⱺ:"o",ō:"o",ṓ:"o",ṑ:"o",ǫ:"o",ǭ:"o",ø:"o",ǿ:"o",õ:"o",ṍ:"o",ṏ:"o",ȭ:"o",ƣ:"oi",ꝏ:"oo",ɛ:"e",ᶓ:"e",ɔ:"o",ᶗ:"o",ȣ:"ou",ṕ:"p",ṗ:"p",ꝓ:"p",ƥ:"p",ᵱ:"p",ᶈ:"p",ꝕ:"p",ᵽ:"p",ꝑ:"p",ꝙ:"q",ʠ:"q",ɋ:"q",ꝗ:"q",ŕ:"r",ř:"r",ŗ:"r",ṙ:"r",ṛ:"r",ṝ:"r",ȑ:"r",ɾ:"r",ᵳ:"r",ȓ:"r",ṟ:"r",ɼ:"r",ᵲ:"r",ᶉ:"r",ɍ:"r",ɽ:"r",ↄ:"c",ꜿ:"c",ɘ:"e",ɿ:"r",ś:"s",ṥ:"s",š:"s",ṧ:"s",ş:"s",ŝ:"s",ș:"s",ṡ:"s",ṣ:"s",ṩ:"s",ʂ:"s",ᵴ:"s",ᶊ:"s",ȿ:"s",ɡ:"g",ᴑ:"o",ᴓ:"o",ᴝ:"u",ť:"t",ţ:"t",ṱ:"t",ț:"t",ȶ:"t",ẗ:"t",ⱦ:"t",ṫ:"t",ṭ:"t",ƭ:"t",ṯ:"t",ᵵ:"t",ƫ:"t",ʈ:"t",ŧ:"t",ᵺ:"th",ɐ:"a",ᴂ:"ae",ǝ:"e",ᵷ:"g",ɥ:"h",ʮ:"h",ʯ:"h",ᴉ:"i",ʞ:"k",ꞁ:"l",ɯ:"m",ɰ:"m",ᴔ:"oe",ɹ:"r",ɻ:"r",ɺ:"r",ⱹ:"r",ʇ:"t",ʌ:"v",ʍ:"w",ʎ:"y",ꜩ:"tz",ú:"u",ŭ:"u",ǔ:"u",û:"u",ṷ:"u",ü:"u",ǘ:"u",ǚ:"u",ǜ:"u",ǖ:"u",ṳ:"u",ụ:"u",ű:"u",ȕ:"u",ù:"u",ủ:"u",ư:"u",ứ:"u",ự:"u",ừ:"u",ử:"u",ữ:"u",ȗ:"u",ū:"u",ṻ:"u",ų:"u",ᶙ:"u",ů:"u",ũ:"u",ṹ:"u",ṵ:"u",ᵫ:"ue",ꝸ:"um",ⱴ:"v",ꝟ:"v",ṿ:"v",ʋ:"v",ᶌ:"v",ⱱ:"v",ṽ:"v",ꝡ:"vy",ẃ:"w",ŵ:"w",ẅ:"w",ẇ:"w",ẉ:"w",ẁ:"w",ⱳ:"w",ẘ:"w",ẍ:"x",ẋ:"x",ᶍ:"x",ý:"y",ŷ:"y",ÿ:"y",ẏ:"y",ỵ:"y",ỳ:"y",ƴ:"y",ỷ:"y",ỿ:"y",ȳ:"y",ẙ:"y",ɏ:"y",ỹ:"y",ź:"z",ž:"z",ẑ:"z",ʑ:"z",ⱬ:"z",ż:"z",ẓ:"z",ȥ:"z",ẕ:"z",ᵶ:"z",ᶎ:"z",ʐ:"z",ƶ:"z",ɀ:"z",ﬀ:"ff",ﬃ:"ffi",ﬄ:"ffl",ﬁ:"fi",ﬂ:"fl",ĳ:"ij",œ:"oe",ﬆ:"st",ₐ:"a",ₑ:"e",ᵢ:"i",ⱼ:"j",ₒ:"o",ᵣ:"r",ᵤ:"u",ᵥ:"v",ₓ:"x"};if(!e.normalize)return e;const n=e.replace(/[^A-Za-z0-9[\] ]/g,(function(e){return t[e]||e}));return e.normalize("NFC").length==n.normalize("NFC").length?n:""},escapeRegExp:e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),match(e,t,n){const r=new RegExp(t,n?"g":"gi"),o=[];let i=r.exec(e);for(;null!=i;)o.push([i.index,i[0].length]),i=r.exec(e);return o},exec:(e,t)=>new RegExp(e).exec(t)}}function M(e){const t=new FileReader;return new Promise(((n,r)=>{t.onerror=e=>{r(new Error(e))},t.onload=e=>{n(new Uint8Array(e.target?.result))},t.readAsArrayBuffer(e)}))}const J="text",R="documentA",L="documentB",U="result",z="Maui_Android",W="Maui_iOS",V="Maui_MacCatalyst",q="Maui_Windows",K="FlutterForWeb",G="Electron",Z="cms",H="unableToShape",Y="requiresMoreFonts";async function X(e){try{const n=await fetch(e).catch((n=>{throw new t(`Error fetching dynamic fonts file ${e}. ${n}`)}));if(200!==n.status)throw new t(`Error fetching dynamic fonts file ${e}. Status code: ${n.status}`);return n}catch(e){throw e}}function Q(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent??"";return e.indexOf("Trident/")>-1?"trident":e.indexOf("Edge/")>-1?"edge":e.indexOf("Chrome/")>-1?"blink":e.indexOf("AppleWebKit/")>-1?"webkit":e.indexOf("Gecko/")>-1?"gecko":"unknown"}function ee(e,t){const n=new RegExp(` ${t}/(\\d+)\\.*`);let r;return(r=e.match(n))?Number(r[1]):0}function te(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent??"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis.navigator?.platform??"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:globalThis.navigator?.maxTouchPoints??0;return t.indexOf("MacIntel")>-1&&n>1?"ios":e.indexOf("Win")>-1?"windows":e.indexOf("iPhone")>-1||e.indexOf("iPad")>-1||"iPad"===t?"ios":e.indexOf("Mac")>-1?"macos":e.indexOf("Android")>-1?"android":e.indexOf("Linux")>-1?"linux":"unknown"}Q();const ne=te(),re=(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent;switch(Q(e)){case"trident":return ee(e,"Trident");case"edge":return ee(e,"Edge");case"blink":return ee(e,"Chrome");case"webkit":return ee(e,"Version");case"gecko":return ee(e,"Firefox");default:;}}(),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q();"ios"===e||"android"===e||oe(t)}(),"ios"===ne||oe(),"ios"===ne);function oe(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q();return("undefined"==typeof window||!window.PSPDFKIT_PLAYWRIGHT_TEST)&&("webkit"===e&&"undefined"!=typeof TouchEvent)}let ie;"undefined"!=typeof window&&(window.addEventListener("mousemove",(function e(){ie=!1,window.removeEventListener("mousemove",e)})),window.addEventListener("pointermove",(function e(t){"mouse"!==t.pointerType&&"pen"!==t.pointerType||(ie=!1),window.removeEventListener("pointermove",e)})));/Mac/i.test(globalThis.navigator?.platform);function ae(){return"object"==typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron/")>=0}const se="/create.pdf",ce="/save.pdf",le="/embedded.pdf",ue="WebAssembly module not loaded.";let de=null,pe=!1,fe=!1,me=null;const ge={password:void 0,initialPageIndex:void 0};let he=[],ye=ge,be=null,we=null;const ve=new Set,_e="/fonts";let Oe;const Fe=new Set;let Se=!1;var xe=function(e){return e[e.Daemon=0]="Daemon",e[e.WASM=1]="WASM",e}(xe||{});const Ie=[];function Ee(e){return null!=e&&null!=e.length&&0===e.length}const Ae=["configurePDFJavaScriptSupport","closeDocument","setFormValues","openDocument","saveDocument","importXFDF","importInstantDocumentJSON"],je={annotationsAndForms:"annotations_and_forms",assemble:"assemble",extract:"extract",extractAccessibility:"extract_accessibility",fillForms:"fill_forms",modification:"modification",printHighQuality:"print_high_quality",printing:"printing"};function De(e){let t;r(de,"WebAssembly module not loaded."),Se&&(t=performance.now());for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];const a=o.map((e=>Ee(e)?JSON.stringify(e):e));Ae.includes(e)&&Ue();const s=de[e](...a)||'{ "success": true }',c=JSON.parse(s);if(Se&&Ie.push({type:xe.WASM,name:e,args:a,time:performance.now()-t}),!c.success)throw new Error(c.error);return c}function Pe(e){let t;r(de,"WebAssembly module not loaded."),Se&&(t=performance.now());for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];Ae.includes(e)&&Ue();const a=o.map((e=>Ee(e)?JSON.stringify(e):e)),s=de[e](...a);return Se&&Ie.push({type:xe.WASM,name:e,args:a,time:performance.now()-t}),s}const ke=["run_pdf_formatting_scripts","run_pdf_javascript","set_form_values_get_script_changes","edit_document","prepare_sign","sign","on_keystroke_event","save_document","update_annotation"];function Ce(e){const t=new ArrayBuffer(2*e.length),n=new Uint16Array(t);for(let t=0,r=e.length;t<r;t++)n[t]=e.charCodeAt(t);return t}function Te(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(de,"WebAssembly module not loaded.");const o=JSON.stringify({type:e,...t});let i,a;if(Se&&(i=performance.now()),ke.includes(e)&&Ue(),n)try{const e="string"==typeof n?new Uint8Array(Ce(n)):new Uint8Array(n),t=de.allocateMemory(e.byteLength);try{t.view.set(e);const n=de.memoryHandleToVector(t);try{a=de.dispatchCommandWithBinary(o,n)}finally{n.delete()}}finally{t.delete()}}catch(e){throw e}else a=de.dispatchCommand(o);if(a.hasError()){const t=new Error(a.getErrorMessage()||"There was an error while executing the command: "+e);throw a.delete(),t}const s=[];for(let e=0;e<a.getRepliesCount();e++)a.hasJSONReply(e)&&s.push(JSON.parse(a.getJSONReply(e))),a.hasBinaryReply(e)&&s.push(a.getBinaryReply(e).slice(0));return Se&&Ie.push({type:xe.Daemon,name:e,args:[o],time:performance.now()-i}),a.delete(),s}function Ne(e,t){return`${e}/${t}.pdf`}async function Be(e,t){const n=Math.random().toString(36).slice(-5),o=await Promise.all(Object.entries(t).map((async t=>{let[r,o]=t;return e.forEach((e=>{"document"in e&&e.document===r&&"importDocument"===e.type?(!1===e.treatImportedDocumentAsOnePage&&(e.treatImportedDocumentAsOnePage=void 0),e.document=Ne(n,r)):("dataFilePath"in e&&e.dataFilePath===r&&"applyInstantJson"===e.type||"dataFilePath"in e&&e.dataFilePath===r&&"applyXfdf"===e.type)&&(e.dataFilePath=Ne(n,r))})),{basename:r,buffer:await M(o)}})));return o.forEach((e=>{!function(e,t,n){r(de,ue),de.FS.analyzePath(e).exists||de.FS.mkdir(e),de.FS.writeFile(Ne(e,t),n)}(n,e.basename,e.buffer)})),function(){o.forEach((e=>{!function(e,t){r(de,ue),de.FS.unlink(Ne(e,t))}(n,e.basename)}))}}function $e(){fe=!0;const e=De("configurePDFJavaScriptSupport",!0);return r(e.success,"An error occurred while executing the document level JavaScript."),e.changes||[]}function Me(e,t){let n;try{n=Te("edit_document",{save_path:t,operations:e})}catch(e){throw new Error(`Error applying operations to document: ${e.message}`)}return n}let Je,Re=null,Le=!1;function Ue(){Le||(Re=null,Je=!1)}function ze(e){Le&&!e&&(Le=!1,Re&&Ve(Re)),Le=e}function We(){return null===Re&&(Re=Te("read_form_json_objects",{include_line_height_factor:!1}),Je=!1),Re}function Ve(e){if(Le){if(null===e)throw Le=!1,new Error("Error enqueuing form JSON objects: form fields JSON is null.");return Re&&!Je&&(Je=!0),void(Re=e)}if(Je||!Le)try{Ue(),Te("apply_form_json_objects",{form_fields_with_widgets:e})}catch(e){throw new Error("Error applying form JSON objects to /create.pdf: "+e.message)}}function qe(e,t,n,o,i,a){const s=Ge(i,{width:n,height:o});if(r("number"==typeof e.pageIndex,"Annotation must have a pageIndex"),!ve.has(e.pdfObjectId))try{r("number"==typeof e.pdfObjectId,"Cannot call renderAnnotation() for an annotation without pdfObjectId.");const t=Te("render_annotation",{page:e.pageIndex,annotation_id:e.pdfObjectId,format:s,bitmap_width:n,bitmap_height:o,appearance_stream_type:a||"normal"});if("bitmap"===s)return t[0];{const e=new Blob([t[0]],{type:`image/${s}`});return URL.createObjectURL(e)}}catch(e){return void 0}}const Ke={SharePoint:"SPO",Salesforce:"SF",[W]:"MauiIOS",[z]:"MauiAndroid",[V]:"MauiMacCatalyst",[q]:"MauiWindows",[K]:"FlutterForWeb",NodeJS:"NodeJS",Electron:"Electron"};function Ge(e,t){let{width:n,height:r}=t;return"webp"===e&&(n>16383||r>16383)||re&&n*r>16777216&&"bitmap"===e?"png":e}function Ze(e){const t=[];for(let n=0;n<e.length;n++)t.push(je[e[n]]);return t}function He(e,t){return e.pdfObjectId===t.pdfObjectId}const Ye=new class{constructor(){l(this,"_pdfObjectIdsForIds",{}),l(this,"comparisonDocuments",{}),l(this,"lastOpenedComparisonDocument",null),l(this,"persistedOpenDocument",null),l(this,"persistedOpenDocumentConfiguration",ge),l(this,"files",new Map),l(this,"fileDescriptor",0)}loadNativeModule(e,t){let{disableWebAssemblyStreaming:n,enableAutomaticLinkExtraction:r,overrideMemoryLimit:i}=t;return be=Date.now(),pe=r,me=i,function(e,t){let n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:globalThis;return new Promise((async a=>{n=B(e,t),"string"==typeof e&&(r=await k(`${e}${T}`)),o("Using WASM method"),$(i),a({nativeModule:await r(n)})}))}(e,n).then((e=>{let{nativeModule:t}=e;de=t;const n=De("PSPDFKitVersion").version;if(1!==n)throw new Error(`Native version mismatch. Please update the dependencies. Expected 1 but got ${n}.`)}))}corePBProtoBridge(e,t,n){const r=de.allocateMemory(n.byteLength);let o=null,i=null;try{r.view.set(n),o=de.corePBProtoBridge(e,t,r),i=o.result;const a={success:o.success,result:i.view.slice()};return Promise.resolve(a)}finally{r.delete(),o?.result?.delete(),o?.delete(),i?.delete()}}async writeFile(e,t){r(de,ue),de.FS.writeFile(e,new Uint8Array(t))}async readFile(e){return r(de,ue),de.FS.readFile(e)}async renderLinearized(e){const t=de.allocateMemory(e.byteLength);let n=null;try{return t.view.set(e),n=de.renderLinearized(t),Promise.resolve(n.view.slice())}finally{t.delete(),n?.delete()}}async load(e,t,n){let i,{mainThreadOrigin:a,customFonts:s,dynamicFonts:c,productId:l}=n;r(de,ue);try{if(de.FS.analyzePath(_e).exists||de.FS.mkdir(_e),Oe=s?"/fonts/custom-fonts":"",s&&!de.FS.analyzePath(Oe).exists&&function(e,t){r(de,ue),de.FS.analyzePath(t).exists||de.FS.mkdir(t),de.FS.mount(de.FS.filesystems.WORKERFS,{blobs:e},t)}(s,Oe),ae()&&(r(null==l||l===G,`PSPDFKit is running on Electron, but has been setup to be used with ${l}Please contact support to resolve the issue.`),l=G),i=de.initPSPDFKit(null==t?"":t,a,_e,"",l&&void 0!==Ke[l]?Ke[l]:""),c){const e=await X(c);Te("dynamic_font_loading/set_metadata_file",void 0,await e.arrayBuffer()),we=c.split("/").slice(0,-1).join("/")}}catch(e){throw e}const u=JSON.parse(i);if(be&&o(`Native initialization complete, took: ${Date.now()-be}ms`),!u.success)throw new Error("Failed to initialize PSPDFKit: "+u.error);return u}getNativeModule(){return de}async openDocument(e,t){try{return r(de,ue),null!==me&&De("overrideMemoryLimit",me),de.FS.writeFile(se,new Uint8Array(e)),this.openAndReturnDocumentInfo(t??ge)}finally{this._pdfObjectIdsForIds={}}}async openCreateFilePathDocument(e){try{return null!==me&&De("overrideMemoryLimit",me),this.openAndReturnDocumentInfo(e??ge)}finally{this._pdfObjectIdsForIds={}}}async getSuggestedLineHeightFactor(e,t){return Te("get_suggested_line_height_factor",{annotation_ids:[e],page:t})[0].line_height_factors[e.toString()]}async getAvailableFontFaces(e){if(!e)return[];return Te("get_available_font_faces")[0]?.font_faces.reduce(((t,n)=>{const r=e.find((e=>e.name===n.path.replace(`${Oe}/`,"")));return r?t.concat({readableName:n.fullName,name:r.name}):t}),[])}async setFontSubstitutions(e){try{Te("set_font_substitutions",{substitutions:e})}catch(e){throw new Error("Error setting fonts substitutions: "+e.message)}}async getClosestSnapPoint(e,t){return Te("get_closest_snap",{q:[e,t]})[0]}async configureSnapper(e){Te("configure_snapper",{page:e})}async reloadDocument(){try{return ze(!1),De("closeDocument"),this.openAndReturnDocumentInfo(ye)}finally{this._pdfObjectIdsForIds={}}}async openAndReturnDocumentInfo(e){ye=e,De("openDocument",se,JSON.stringify(e)),fe&&$e(),De("automaticLinkExtraction",pe);const t=De("getDocumentInfo").documentInfo;if(t.pageCount<=0)return t;let n=[];if("number"==typeof e?.initialPageIndex){const r=await this.getPageInfo(e.initialPageIndex);for(let o=0;o<t.pageCount;o++){const t={...r,pageIndex:o,pageLabel:e.initialPageIndex===o?r.pageLabel:String(o+1)};n.push(t)}}else n=await this.getAllPageInfos(t.pageCount);return t.pages=n,t}async getPageInfo(e){try{const t=Te("page_info",{query:"page_info",page:e});r(1===t.length,"expected page_info result to return 1 result when specifying index.");const n=(new TextDecoder).decode(t[0]);return JSON.parse(n).pageInfo}catch(t){return a(`Dimensional information for page ${e} unavailable, page will not be displayed.`),{height:0,matrix:[0,0,0,0,0,0],pageLabel:"",reverseMatrix:[0,0,0,0,0,0],transformedBBox:[0,0,0,0],untransformedBBox:[0,0,0,0],width:0,pageIndex:e,rawPdfBoxes:{bleedBox:null,cropBox:null,mediaBox:null,trimBox:null}}}}async preflightRenderAnnotationText(e,t,n,o){const i=()=>"number"==typeof t&&"number"==typeof n?Te("dynamic_font_loading/preflight_annotation",{text:e,annotation_id:t,page:n})[0]:(r(o,`annotation not set: ${o}`),Te("dynamic_font_loading/preflight_annotation",{text:e,annotation_json:o})[0]);try{let e=i();e.preflight_result===Y&&(await this.addDynamicFonts(e.required_fonts),e=i()),"number"==typeof t&&(e.preflight_result===H?ve.add(t):ve.delete(t))}catch(n){a(`There was an error testing rendering for annotation ${t} of this text: ${e}: ${n.message}`)}}async addDynamicFonts(e){let t=[];try{t=e.filter((e=>!Fe.has(e)));!function(e){r(de,ue),e.forEach((e=>{de.FS.analyzePath(`/fonts/${e.name}`).exists||de.FS.writeFile(`/fonts/${e.name}`,new Uint8Array(e.data))}))}((await Promise.all(t.map((e=>fetch(`${we}/${e}`).then((e=>e.arrayBuffer())))))).map(((e,n)=>({name:t[n],data:e})))),t.forEach((e=>{Fe.add(e)}))}catch(e){t.forEach((e=>{Fe.delete(e)})),a(`There was an error loading a font: ${e}`)}await Te("dynamic_font_loading/notify_fonts_added",{added_fonts:t.map((e=>({remote_file_path:e,local_file_name:e})))})}async getAllPageInfos(e){const t=[];try{const n=Te("page_info",{query:"page_info",page:"all"});r(n.length===e,"expected the same length of page info response to page count.");for(let e=0;e<n.length;e++){const r=(new TextDecoder).decode(n[e]),o=JSON.parse(r);t.push(o.pageInfo)}}catch(n){a("There was an error retrieving page information for all pages from core. Reverting to individual queries.");for(let n=0;n<e;n++)try{const e=Te("page_info",{query:"page_info",page:n});r(1===e.length,"expected page_info result to return 1 result when specifying index.");const o=(new TextDecoder).decode(e[0]),i=JSON.parse(o);t.push(i.pageInfo)}catch(e){a(`Dimensional information for page ${n} unavailable, page will not be displayed.`);const r={height:0,matrix:[0,0,0,0,0,0],pageLabel:"",reverseMatrix:[0,0,0,0,0,0],transformedBBox:[0,0,0,0],untransformedBBox:[0,0,0,0],width:0,pageIndex:n,rawPdfBoxes:{bleedBox:null,cropBox:null,mediaBox:null,trimBox:null}};t.push(r)}}return t}async enablePDFJavaScriptSupport(){return $e()}async runPDFFormattingScripts(e,t){let n;r(fe,"PDF Formatting Scripts can only run after JavaScript is enabled.");try{n=Te("run_pdf_formatting_scripts",{form_fqns:e,only_if_no_ap_stream:t})}catch(e){throw new Error("An error occurred while executing the document level JavaScript formatting.\n\n"+e.message)}return n[0].changes||[]}async openDocumentAt(){throw new Error("Should never be called")}async getBookmarks(){const e=Te("get_bookmarks");return r(1===e.length,"expected only one response for getBookmarks"),e[0].bookmarks||[]}async getFormJSON(){return De("getFormJSON").formJSON}async getFormValues(){return De("getFormValues").formValues}async setFormValues(e){const t=We();let n=!1;const r=[],o=t.map((t=>{const o=e.find((e=>e.name===t.formField.name));return o?o.value===t.value||Array.isArray(o.value)&&Array.isArray(t.value)&&o.value.every(((e,n)=>e===t.value[n]))?t:("string"==typeof o.value&&o.value.length>0?r.push({widgets:t.widgets,text:o.value}):t.widgets.forEach((e=>{"number"==typeof e.pdfObjectId&&ve.delete(e.pdfObjectId)})),n=!0,{...t,value:o.value}):t}));if(n){if(we)for(let e=0;e<r.length;e++){const t=r[e].widgets;for(let n=0;n<t.length;n++)await this.preflightWidgetAnnotation(t[n],t[n].pdfObjectId,r[e].text)}Ve(o)}}async setFormFieldValue(e){await this.setFormValues([e])}async applyOperations(e,t){const n=await Be(e,t);Me(e,se),n()}async exportPDFWithOperations(e,t){const n=await Be(e,t);let o;r(de,ue);try{Me(e,ce),o=de.FS.readFile(ce).buffer}catch(e){throw new Error("Error applying operations: "+e.message)}return n(),o}async getSignaturesInfo(){try{return Te("get_signatures",{certificate_check_time:"current_time"})[0]}catch(e){throw new Error(`Error getting signatures info: ${e.message}`)}}async getComments(){try{return Te("get_comments")}catch(e){throw new Error(`Error getting comments: ${e.message}`)}}async applyComments(e){try{return Te("apply_comments",{comments:e})}catch(e){throw new Error(`Error applying comments: ${e.message}`)}}async prepareSign(e,t,n,o,i,a){let s;try{s=Te("get_signatures",{certificate_check_time:"current_time"})[0];const e="not_signed"!==s.status;De("saveDocument",ce,!1,e,false,false,"pdf",!1),De("openDocument",ce,JSON.stringify(ye))}catch(e){throw new Error(`Error saving document backup for invisible signing: ${e}`)}try{const e=!n&&"not_signed"!==s.status;De("saveDocument",se,n,e,false,false,"pdf",!1),n&&(De("openDocument",se,JSON.stringify(ye)),fe&&$e())}catch(e){throw new Error(`Error saving document for invisible signing: ${e}`)}let c;try{c=Te("prepare_sign",{signer_data_source:{...e,field_name:o,position:i,appearance:a,type:"pspdfkit/signer-data-source"},signature_metadata:{...t,type:"pspdfkit/signature-metadata"}})}catch(e){throw new Error(`Error preparing document for signing: ${e}`)}r(de,ue);let l=null;return e&&e?.signatureType!==Z||(l=de.FS.readFile(c[0].result.file_contents).buffer),de.FS.unlink(c[0].result.file_contents),{file:c[0].result.file,hash:c[0].result.hash,signatureFormFieldName:c[0].result.signature_form_fqn,dataToBeSigned:c[0].result.data_to_be_signed,fileContents:l}}async getTimestampRequest(e,t){try{const[n]=Te("get_timestamp_http_request",{timestamp_authority_info:{url:t.url,authenticationInfo:{username:t.username,password:t.password}},data_to_timestamp:e});return{url:n.url,method:n.method,requestData:n.request_data,contentType:n.content_type,token:n.token,username:n.user,password:n.password}}catch(e){throw new Error(`Error getting timestamp request: ${e.message}`)}}async getRevocationRequests(e){try{return Te("get_revocation_http_requests",{signing_certificates:e})}catch(e){throw new Error(`Error getting revocations requests: ${e.message}`)}}async setSignaturesLTV(e){try{const t=Te("get_signatures",{certificate_check_time:"current_time",revocation_responses:e})[0];return De("saveDocument",se,!1,!0,!1,!1,"pdf",!1),t}catch(e){throw new Error(`Error getting signatures info: ${e.message}`)}}async sign(e,t,n,r,o,i,a,s,c){let l;try{let u;const d=F(o).buffer,p=a?"pkcs7"===a:function(e){try{const t=new Uint8Array(e);return!!t.length&&48===t[0]}catch(e){throw new Error(`Error checking if the signature is in PKCS#7 format: ${e.message}`)}}(d);p||(u=Te("create_signature",{signatureType:r,hash:n,signed_data:o,certificates:i,timestamp_response:s})),l=Te("sign",{password:ye?.password,save_path:se,file_path:e,signature_form_fqn:t,pkcs7_container:u?.[0].result||o,revocation_responses:c??[]}),De("openDocument",se,JSON.stringify(ye)),fe&&$e()}catch(e){throw new Error(`Error signing document: ${e}`)}return l[0].result}async restoreToOriginalState(){try{De("openDocument",ce,JSON.stringify(ye)),fe&&$e();const e="not_signed"!==Te("get_signatures",{certificate_check_time:"current_time"})[0].status;De("saveDocument",se,!1,e,!1,!1,"pdf",!1),De("openDocument",se,JSON.stringify(ye)),fe&&$e()}catch(e){throw new Error(`Could not restore backup document: ${e}`)}}async evalFormValuesActions(e){const t=We().reduce(((t,n)=>e.find((e=>{let{name:t}=e;return n.formField.name===t}))&&"pspdfkit/form-field/text"===n.formField.type?t.concat(n):t),[]);if(we)for(let n=0;n<t.length;n++){const r=t[n].widgets;for(let n=0;n<r.length;n++)await this.preflightWidgetAnnotation(r[n],r[n].pdfObjectId,e.find((e=>t[n].formField.name===e.name))?.value)}const n=Te("set_form_values_get_script_changes",{form_values:e});return r(1===n.length,"expected only one response for evalFormValuesActions"),this._processChanges(n[0].changes)}async readFormJSONObjects(){return We()}async setFormJSONUpdateBatchMode(e){await ze(e)}_processChanges(e){return e.map((e=>"pspdfkit/javascript/effects/importIcon"===e.object.type?{...e,object:{...e.object,id:Object.entries(this._pdfObjectIdsForIds).find((t=>{let[,n]=t;return n===e.object.annotationID}))?.[0]}}:e))}async evalScript(e,t,n){const o=Te("run_pdf_javascript",{pdf_javascript_contents:e,pdf_javascript_trigger_event:t,pdf_javascript_form_fqn:n});return r(1===o.length,"expected only one response for evalScript"),this._processChanges(o[0].changes)}async closeDocument(){try{return he=[],ye={password:void 0,initialPageIndex:void 0},fe=!1,Re=null,Le=!1,Je=!1,De("closeDocument")}finally{this._pdfObjectIdsForIds={}}}async renderAnnotation(e,t,n,r,o,i){return qe({...e,pdfObjectId:this._getPdfObjectIdForObject(e)},t&&await M(t),n,r,o,i)}async renderPageAnnotations(e,t,n,r,o){return t.map(((t,i)=>qe({pdfObjectId:t,pageIndex:e},0,n[i],r[i],Ge(o,{width:n[i],height:r[i]}))))}async renderDetachedAnnotation(e,t,n,o,i){r(de,ue),!we||"pspdfkit/stamp"!==e.type&&"pspdfkit/text"!==e.type||await this.preflightAnnotation(e,e.pdfObjectId);const a=t?await M(t):null,s=t?t.type:null;let c,l;const u=new de.VectorUint8;try{if(null!=a)for(let e=0;e<a.byteLength;e++)u.push_back(a[e]);if(l=Pe("renderDetachedAnnotation",i,JSON.stringify({...e,pdfObjectId:this._getPdfObjectIdForObject(e),pageIndex:0}),0,n,o,u,s||""),l.hasError()||1!==l.getRepliesCount()){const e=l.getErrorMessage(),t=l.getErrorReason();throw new Error(t+": "+e)}c=l.getBinaryReply(0).slice(0)}finally{u&&u.delete(),l&&l.delete()}return c}async loadCertificates(e){if(Te("load_certificates",{certificates:e}).length>0)throw new t("Internal error while loading certificates")}async getAttachment(e){let t,n,r;const o=(await this.getEmbeddedFilesList())?.map((e=>e.id)),i=o?.includes(e);try{if(i)Te("extract_embedded_file",{id:e,file_path:le}),t=de.FS.readFile(le).buffer;else{if(n=Pe("getAnnotationAttachment",e),n.hasError()||1!==n.getRepliesCount()){const e=n.getErrorMessage(),t=n.getErrorReason();throw new Error("Error fetching attachment: "+t+", "+e)}r=JSON.parse(n.getJSONReply(0)).encoding,t=n.getBinaryReply(0).slice(0)}}finally{de.FS.analyzePath(le)?.exists&&de.FS.unlink(le),n&&n.delete()}return[t,r]}async annotationsForPageIndex(e){const t=De("annotationsForPageIndex",e);return["rollover","down"].forEach((e=>{t.apstream_variants?.[e]?.forEach((n=>{const r=t.annotations.find((e=>e.pdfObjectId===n));r?r[e]=!0:t.annotations.push({pdfObjectId:n,[e]:!0})}))})),t.annotations}async getTabOrder(e){return Te("get_annotation_tab_order",{page:e})[0].order.map((e=>({id:Object.entries(this._pdfObjectIdsForIds).find((t=>{let[n,r]=t;return r===e}))?.[0]||String(e),pdfObjectId:e})))}async setTabOrder(e,t){Te("set_annotation_tab_order",{page:e,order:t.map((e=>this._getPdfObjectIdForObject(e)))})}async createAnnotation(e,t){r(de,ue),r("number"==typeof e.pageIndex,"Annotation must have a pageIndex");const n=e.pdfObjectId,o=t?await M(t):null;let i;const a=new de.VectorUint8;try{if(null!=o)for(let e=0;e<o.byteLength;e++)a.push_back(o[e]);"pspdfkit/widget"===e.type&&(ze(!1),Ue());const t=("pspdfkit/shape/ellipse"===e.type||"pspdfkit/shape/rectangle"===e.type)&&e.measurementScale?{...e,measurementBBox:e.bbox}:e;i=De("createAnnotation",JSON.stringify({...t,pdfObjectId:null}),a)}finally{a&&a.delete()}return"number"==typeof n&&"number"==typeof this._pdfObjectIdsForIds[n.toString()]&&delete this._pdfObjectIdsForIds[n.toString()],this._pdfObjectIdsForIds[e.id||i.pdfObjectId.toString()]=i.pdfObjectId,e&&this.canPreflightAnnotation(e)&&we&&await this.preflightAnnotation(e,i.pdfObjectId),i.pdfObjectId}async updateAnnotation(e){const t={...e,pdfObjectId:this._getPdfObjectIdForObject(e)};if(r(t.id,"Annotation must have an ID"),r("number"==typeof t.pageIndex,"Annotation must have a pageIndex"),"pspdfkit/widget"===t.type){const e=We(),n="number"==typeof t.pdfObjectId?t.pdfObjectId.toString():t.id;Ve(e.map((e=>e.formField.annotationIds.includes(n)||e.formField.annotationIds.includes(String(t.pdfObjectId))?{...e,widgets:e.widgets.map((e=>e.id===n||String(e.pdfObjectId)===n?t:e))}:e)))}else e&&this.canPreflightAnnotation(e)&&we&&await this.preflightAnnotation(e,t.pdfObjectId),De("updateAnnotation",JSON.stringify(t),0,0)}async updateButtonIcon(e,t,n){Te("update_annotation",{annotation:{...e,pdfObjectId:this._getPdfObjectIdForObject(e),contentType:n}},t)}async deleteAnnotation(e){if(e.APStreamCache&&await this.updateAnnotation(e),"pspdfkit/widget"===e.type)try{const t=e.id;Ve(We().map((n=>{if(n.formField.annotationIds.includes(t)||n.formField.annotationIds.includes(String(e.pdfObjectId))){let r,o=n.value;if(("pspdfkit/form-field/checkbox"===n.formField.type||"pspdfkit/form-field/radio"===n.formField.type)&&n.formField.options.length===n.formField.annotationIds.length){const i=n.formField.annotationIds.includes(t)?n.formField.annotationIds.indexOf(t):n.formField.annotationIds.indexOf(String(e.pdfObjectId));r=n.formField.options.filter(((e,t)=>t!==i)),r.some((e=>e.value===o))||(o="")}const i=n.widgets.filter((e=>t!==e.id)),a=n.formField.annotationIds.filter((n=>n!==t&&n!==String(e.pdfObjectId)));return i.length>0&&a.length>0?{...n,formField:{...n.formField,annotationIds:a,...r?{options:r}:null},widgets:i,value:o}:null}return n})).filter(Boolean))}catch(e){throw e}else try{Te("remove_annotations",{annotation_ids:[this._getPdfObjectIdForObject(e)]})}catch(t){i(`Removing annotation failed for annotation: ${JSON.stringify(e)}`)}delete this._pdfObjectIdsForIds[e.id||e.pdfObjectId?.toString()],ve.delete(e.pdfObjectId)}async createFormField(e,t){const n=We().concat([{type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t}]),r=Le;Le&&ze(!1),Ve(n);const o=We();r&&ze(!0);const i=o.find((t=>t.formField.name===e.name));if(!i)throw new Error(`Error creating new form field in /create.pdf: created form field not found. ${JSON.stringify({type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t})}`);const a=i.widgets.map((e=>(this._pdfObjectIdsForIds[e.id]=e.pdfObjectId,e.pdfObjectId)));return"number"==typeof e.pdfObjectId&&"number"==typeof this._pdfObjectIdsForIds[e.pdfObjectId.toString()]&&delete this._pdfObjectIdsForIds[e.pdfObjectId.toString()],this._pdfObjectIdsForIds[e.id||i.formField.pdfObjectId.toString()]=i.formField.pdfObjectId,a}async updateFormField(e,t){let n={...e,pdfObjectId:this._pdfObjectIdsForIds[e.id]||this._getPdfObjectIdForObject(e)};const r=We(),o=r.find((t=>He(t.formField,n)||t.formField.name===e.name));if(!o)throw new Error(`Error updating form field with name "${e.name}" in /create.pdf: form field not found. ${JSON.stringify(r)}`);const i=o.formField.name!==e.name;let a=null;if(i&&"pspdfkit/form-field/radio"===n.type){const o=e.name,i=r.find((e=>e.formField.name===o));i&&"pspdfkit/form-field/radio"===i.formField.type&&(n={...n,annotationIds:[...n.annotationIds,...i.formField.annotationIds],options:[...n.options,...i.formField.options]},t=[...t,...i.widgets],a=i)}Ve(r.map((r=>{if(a&&a.formField.pdfObjectId===r.formField.pdfObjectId)return null;if(He(r.formField,n)||r.formField.name===e.name){const o={type:"pspdfkit/form-field-with-widgets",formField:{...r.formField,...n},widgets:t.reduce(((e,t)=>[...e,{...r.widgets.find((e=>e.id===t.id||e.pdfObjectId===t.pdfObjectId)),...t}]),[]),...void 0!==r.value?{value:r.value}:null};return e.flags||delete o.formField.flags,o}return r})).filter(Boolean));const s=We().find((t=>t.formField.name===e.name));if(!s)throw new Error(`Error updating form field "${e.name}" in /create.pdf: updated form field not found. ${JSON.stringify({type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t})}`);s.formField.pdfObjectId!==n.pdfObjectId&&(this._pdfObjectIdsForIds[n.id]=s.formField.pdfObjectId),o?.widgets.forEach((e=>{t.some((t=>t.id===e.id))||delete this._pdfObjectIdsForIds[e.id]}))}async deleteFormField(e){const t=We(),n=t.find((t=>t.formField.name===e.name));if(!n)return;Ve(t.filter((t=>t.formField.name!==e.name)));const r=We().find((t=>t.formField.name===e.name));if(r)throw new Error(`Error deleting form field with name "${e.name}" in /create.pdf: form field still present. ${JSON.stringify(r)}`);n.widgets.forEach((e=>{delete this._pdfObjectIdsForIds[e.id]}))}async deleteFormFieldValue(e){Ve(We().map((t=>t.formField.name===e?{formField:t.formField,widgets:t.widgets,type:t.type}:t)))}canPreflightAnnotation(e){return"pspdfkit/text"===e.type||"pspdfkit/stamp"===e.type}async preflightAnnotation(e,t){if(!this.canPreflightAnnotation(e))return;const n="pspdfkit/stamp"===e.type?e.title:e.text?.value;"number"!=typeof t||n?n&&await this.preflightRenderAnnotationText(n,t,e.pageIndex,{...e,pdfObjectId:this._getPdfObjectIdForObject(e),pageIndex:0}):ve.delete(t)}async preflightWidgetAnnotation(e,t,n){n||"number"!=typeof t?n&&await this.preflightRenderAnnotationText(n,t,e.pageIndex):ve.delete(t)}async createBookmark(e){try{Te("append_bookmarks",{bookmarks:[e]})}catch(e){throw new Error(`Error creating new bookmark in /create.pdf: ${e.message}`)}}async updateBookmark(e){try{Te("remove_bookmarks",{bookmarkIds:[e.id??e.pdfBookmarkId]}),Te("append_bookmarks",{bookmarks:[e]})}catch(e){throw new Error(`Error updating bookmark in /create.pdf: ${e.message}`)}}async deleteBookmark(e){try{Te("remove_bookmarks",{bookmarkIds:[e]})}catch(e){throw new Error(`Error deleting bookmark in /create.pdf: ${e.message}`)}}async getTextFromRects(e,t){return De("getTextFromRects",e,JSON.stringify(t.map((e=>[e.left,e.top,e.width,e.height])))).text}async search(e,t,n,r){return Te("search",{query:e,start_index:t,limit:n,case_sensitive:r,search_type:arguments.length>4&&void 0!==arguments[4]?arguments[4]:J})}async parseXFDF(e,t){try{return function(e){try{const t=(new TextDecoder).decode(e);return JSON.parse(t)}catch(e){throw e}}(Te("convert_xfdf_to_instant_json",{ignore_page_rotation:t},e)[0])}catch(e){throw e}}async getEmbeddedFilesList(){return Te("list_embedded_files")[0].embeddedFiles||null}async getMeasurementSnappingPoints(e){const t=Te("get_snapping_points",{page:e});return t[0].snappingPoints?function(e){const t=[...e.endpoints,...e.intersections,...e.midpoints],n=[];for(let e=0;e<t.length;e+=2){const r=[t[e],t[e+1]];n.push(r)}return n.sort(((e,t)=>e[0]==t[0]?e[1]-t[1]:e[0]-t[0]))}(t[0].snappingPoints):null}async getSecondaryMeasurementUnit(){try{return Te("get_secondary_measurement_unit")[0]||null}catch(e){throw new Error(`Error getting secondary measurement: ${e.message}`)}}async compareDocuments(e){try{r(e.originalDocument.arrayBuffer,"Original document arrayBuffer is missing"),r(e.changedDocument.arrayBuffer,"Changed document arrayBuffer is missing"),de.FS.writeFile("documentA.pdf",new Uint8Array(e.originalDocument.arrayBuffer)),de.FS.writeFile("documentB.pdf",new Uint8Array(e.changedDocument.arrayBuffer));const t={originalDocument:{...e.originalDocument,filePath:"documentA.pdf"},changedDocument:{...e.changedDocument,filePath:"documentB.pdf"},comparisons:[e.comparisonOperation]};return Te("document_comparison",t)||null}catch(e){throw new Error(`Error comparing documents: ${e.message}`)}}async setSecondaryMeasurementUnit(e){try{Te("set_secondary_measurement_unit",e?{unitTo:e?.unitTo,precision:e?.precision}:null)}catch(e){i("Error setting secondary measurement unit")}}async getMeasurementScales(){try{return Te("get_measurement_content_formats")[0]||null}catch(e){throw new Error(`Error getting measurement scales: ${e.message}`)}}async removeMeasurementScale(e){try{Te("remove_measurement_content_format",{measurementContentFormat:S(e)})}catch(t){throw new Error(`Error removing ${e.name} measurement scale: ${t.message}`)}}async addMeasurementScale(e){try{Te("add_measurement_content_format",{measurementContentFormat:S(e)})}catch(t){throw new Error(`Error adding ${e.name} measurement scale: ${t.message}`)}}async getAnnotationsByScale(e){try{return Te("get_annotations_for_measurement_content_format",{measurementContentFormat:S(e)})||null}catch(e){throw e}}async exportFile(e,t,n,o,i,a,s,c){let l,u={mimeType:"application/pdf",extension:"pdf"};try{const r=!1;l=Te("save_document",{file_path:se,format:o,flatten_annotations:e,save_incrementally:t,apply_redactions:r,save_for_printing:n,remove_all_annotations:i,preserve_change_tracker_state:a,...s?{user_password:s.userPassword,owner_password:s.ownerPassword,document_permissions:Ze(s.documentPermissions)}:null}),c&&(c.forEach((e=>{let{pageIndex:t,id:n,pdfObjectId:r}=e;Te("electronic_signatures/flatten",{page:t,annotation_id:this._getPdfObjectIdForObject({id:n,pdfObjectId:r})})})),l=Te("save_document",{file_path:ce,format:o,flatten_annotations:e,save_incrementally:t,apply_redactions:r,save_for_printing:n,remove_all_annotations:i,preserve_change_tracker_state:a,...s?{user_password:s.userPassword,owner_password:s.ownerPassword,document_permissions:Ze(s.documentPermissions)}:null}))}catch(e){throw new Error(`Error saving to /save.pdf: ${e.message}`)}const d=l[0];d.format&&(u={mimeType:d.format.mime_type,extension:d.format.extension}),r(de,ue);const p=de.FS.readFile(c?ce:se).buffer;return c&&(De("closeDocument"),De("openDocument",se,JSON.stringify(ye))),[p,u]}async importXFDF(e,t,n){he.push({source:e,keepCurrentAnnotations:t}),t||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.push("pspdfkit/widget"),De("removeAllAnnotations",JSON.stringify(e),JSON.stringify(t))}(),De("importXFDF",e,n)}async exportXFDF(e){return De("exportXFDF",[],[],e).XFDF}async importInstantJSON(e){De("importInstantDocumentJSON",JSON.stringify(e))}async exportInstantJSON(e){const t=Le;Le&&ze(!1);const n=De("exportInstantDocumentJSON","number"==typeof e?e:-1);return t&&ze(!0),JSON.parse(n.InstantDocumentJSON)}async getDocumentOutline(){const e=Te("get_outline");return r(1===e.length,"expected only one response for getDocumentOutline"),e[0].outline||[]}async setDocumentOutline(e){return Te("set_outline",{outline:e})}async getPageGlyphs(e){return Te("get_glyphs",{page:e})[0].glyphs||[]}async onKeystrokeEvent(e){const t=Te("on_keystroke_event",{pdf_javascript_event:e});r(1===t.length,"expected only one response for onKeystrokeEvent");const n=t[0].changes||[];return r(n.length>0&&4===n[0].change_type,"expected onKeystrokeEvent to return at least one JavaScript Event change."),n}async version(){return De("PSPDFKitVersion").version}getImportedXFDF(){return he}async applyRedactions(){try{De("saveDocument",se,!1,!1,!0,!1,"pdf",!1)}catch(e){throw new Error(`Error applying redactions and saving to /create.pdf: ${e.message}`)}}async clearAPStreamCache(){De("clearAPStreamCache")}async setComparisonDocument(e,t){this.comparisonDocuments[e]=t||(await this.exportFile(!1,!1,!1,"pdf",!1,!1))[0]}async openComparisonDocument(e){return r(this.comparisonDocuments[e]),e===this.lastOpenedComparisonDocument||e===R&&null===this.lastOpenedComparisonDocument&&this.persistedOpenDocument instanceof ArrayBuffer?null:(this.lastOpenedComparisonDocument=e,this.openDocument(this.comparisonDocuments[e],this.persistedOpenDocument===e?this.persistedOpenDocumentConfiguration:ge))}async documentCompareAndOpen(e){r(this.comparisonDocuments[R]&&this.comparisonDocuments[L],"One or both comparison input documents are missing.");const t=`${R}.pdf`,n=`${L}.pdf`;de.FS.writeFile(t,new Uint8Array(this.comparisonDocuments[R])),de.FS.writeFile(n,new Uint8Array(this.comparisonDocuments[L]));return Te("comparison",{documentA:{strokeColor:e.documentA.strokeColor,pageIndex:e.documentA.pageIndex,filePath:t},documentB:{strokeColor:e.documentB.strokeColor,pageIndex:e.documentB.pageIndex,filePath:n},options:{...e.options,outputFilePath:"output.pdf"}}),await this.closeDocument(),this.comparisonDocuments[U]=de.FS.readFile("output.pdf").buffer,this.lastOpenedComparisonDocument=U,this.openDocument(this.comparisonDocuments[U],ge)}async persistOpenDocument(e){if(e)this.persistedOpenDocument=e;else try{this.persistedOpenDocument=(await this.exportFile(!1,!1,!1,"pdf",!1,!0))[0]}catch(e){throw new Error(`Error trying to persist a copy of the currently opened document: ${e.message}`)}this.persistedOpenDocumentConfiguration=ye}async cleanupDocumentComparison(){r(this.persistedOpenDocument,"No persisted previous document key or array buffer is available.");const e=this.persistedOpenDocument instanceof ArrayBuffer?this.persistedOpenDocument:this.comparisonDocuments[this.persistedOpenDocument];r(e,"No persisted previous array buffer is available.");try{await this.closeDocument()}catch(e){throw new Error(`Error trying to close the current document: ${e.message}`)}const t=this.openDocument(e,this.persistedOpenDocumentConfiguration);return this.persistedOpenDocument=null,this.persistedOpenDocumentConfiguration=ge,this.lastOpenedComparisonDocument=null,this.comparisonDocuments={},t}_getPdfObjectIdForObject(e){return"number"==typeof e.pdfObjectId?e.pdfObjectId:this._pdfObjectIdsForIds[e.id]}async getOCGs(){return Te("ocg/get_ocgs")[0].ocgs}async getOCGVisibilityState(){return{visibleLayerIds:Te("ocg/get_visibility_state")[0].state.visible}}async setOCGVisibilityState(e){return Te("ocg/set_visibility_state",{state:{visible:e.visibleLayerIds}})}async dispatchCommand(e){let t,n=null;Se&&(t=performance.now());try{if(n=await de.dispatchCommand(e),null===n)throw Error("dispatchCommand returned null");if(n.hasError())return{success:!1,message:n.getErrorMessage(),code:n.getErrorReason()};const r={success:!0,values:[]};for(let e=0;e<n.getRepliesCount();e++)n.hasJSONReply(e)&&r.values.push(JSON.parse(n.getJSONReply(e))),n.hasBinaryReply(e)&&r.values.push(n.getBinaryReply(e).slice(0));if(Se){const n=JSON.parse(e);Ie.push({type:xe.Daemon,name:n.type,args:n,time:performance.now()-t})}return r}finally{n&&n.delete()}}async getCoreCallRecording(){return Ie}async enableCoreCallRecording(e){Se=e}openFile(e,t){const n=de.FS.open(e,t);return this.files.set(this.fileDescriptor,n),Promise.resolve(this.fileDescriptor++)}closeFile(e){const t=this.files.get(e);return t&&(de.FS.close(t),this.files.delete(e)),Promise.resolve()}writeData(e,t,n){const r=this.files.get(e);if(!r)throw Error(`Trying to write to a file with descriptor ${e}, but the file is not open.`);return de.FS.write(r,new Uint8Array(t),0,t.byteLength,n),Promise.resolve()}recycle(){}destroy(){}},Xe=self;Xe.global=Xe,Xe.module={},Xe.onmessage=async e=>{let t,n,{data:r}=e;try{const e=await Ye[r.action](...r.args);if(t={id:r.id,result:e},Array.isArray(e)){const t=e.filter((e=>e instanceof ArrayBuffer));t.length>0&&(n=t)}e instanceof ArrayBuffer&&(n=[e])}catch(e){const o=[...r.args].filter((e=>e instanceof ArrayBuffer));o.length>0&&(n=o),t={id:r.id,error:e.message||e.toString(),callArgs:r.args}}Xe.postMessage(t,n)}})(),{}})()));