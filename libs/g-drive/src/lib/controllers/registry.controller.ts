import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  CreateCollaboratorDto,
  CreateCollaboratorsDto,
  CreateRegistryDto,
  PaginatedRegistryCollaboratorResponseDto,
  PaginatedRegistryResponseDto,
  QueryCollaboratorDto,
  QueryRegistryDto,
  RegistryCollaboratorResponseDto,
  RegistryResponseDto,
  UpdateRegistryDto,
} from '../dtos';
import { RegistryCollaboratorService, RegistryService } from '../services';
import { JwtAuthGuard } from '@igov/auth';
import { ApiBearerAuth, ApiResponse } from '@nestjs/swagger';

@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('registries')
export class RegistryController {
  constructor(
    private readonly registryService: RegistryService,
    private readonly collaboratorService: RegistryCollaboratorService,
  ) {}

  @ApiResponse({ status: 200, type: RegistryResponseDto })
  @Post('/')
  createRegistry(@Body() body: CreateRegistryDto): Promise<RegistryResponseDto> {
    return this.registryService.createRegistry(body);
  }

  @ApiResponse({ status: 200, type: PaginatedRegistryResponseDto })
  @Get('/')
  getRegistries(@Query() query: QueryRegistryDto): Promise<PaginatedRegistryResponseDto> {
    return this.registryService.getRegistries(query);
  }

  @ApiResponse({ status: 200, type: RegistryResponseDto })
  @Get('/:id')
  getRegistry(@Param('id') id: string): Promise<RegistryResponseDto> {
    return this.registryService.getRegistry({ registryId: id });
  }

  @ApiResponse({ status: 200, type: RegistryResponseDto })
  @Patch('/:id')
  updateRegistry(
    @Param('id') id: string,
    @Body() update: UpdateRegistryDto,
  ): Promise<RegistryResponseDto> {
    return this.registryService.updateRegistry(id, update);
  }

  @ApiResponse({ status: 200 })
  @Delete('/:id')
  deleteRegistry(@Param('id') id: string): Promise<void> {
    return this.registryService.deleteRegistry(id);
  }

  @ApiResponse({ status: 200, type: RegistryCollaboratorResponseDto })
  @Post('/:registryId/collaborators')
  addCollaborator(
    @Param('registryId') registryId: string,
    @Body() dto: CreateCollaboratorDto,
  ): Promise<RegistryCollaboratorResponseDto> {
    return this.collaboratorService.createCollaborator(registryId, dto.employeeId);
  }

  @ApiResponse({ status: 200, type: [RegistryCollaboratorResponseDto] })
  @Post('/:registryId/collaborators/batch')
  addCollaborators(
    @Param('registryId') registryId: string,
    @Body() dto: CreateCollaboratorsDto,
  ): Promise<RegistryCollaboratorResponseDto[]> {
    return this.collaboratorService.createCollaborators(registryId, dto.employeeIds);
  }

  @ApiResponse({ status: 200, type: PaginatedRegistryCollaboratorResponseDto })
  @Get('/:registryId/collaborators')
  getCollaborators(
    @Param('registryId') registryId: string,
    @Query() query: QueryCollaboratorDto,
  ): Promise<PaginatedRegistryCollaboratorResponseDto> {
    return this.collaboratorService.getCollaborators({ ...query, registryId });
  }

  @Delete('/:registryId/collaborators/:employeeId')
  removeCollaborator(
    @Param('registryId') registryId: string,
    @Param('employeeId') employeeId: string,
  ): Promise<void> {
    return this.collaboratorService.removeCollaborator(registryId, employeeId);
  }

  @Post('/:registryId/collaborators/delete-batch')
  removeCollaborators(
    @Param('registryId') registryId: string,
    @Body() dto: CreateCollaboratorsDto,
  ): Promise<void> {
    return this.collaboratorService.removeCollaborators(registryId, dto.employeeIds);
  }
}
