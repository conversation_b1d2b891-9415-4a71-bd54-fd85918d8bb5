import { useState, useCallback, useEffect } from 'react';
import { konn3ctApiService } from '@/services/api/konn3ctService';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch } from '@/store/hooks';
import { showMessage } from '@fuse/core/FuseMessage/fuseMessageSlice';
import { useRouter } from 'next/navigation';
import {
  Room,
  RoomWithStatus,
  RoomStatus,
  CreateRoomDto,
  JoinRoomDto,
  StartRoomDto,
  ListRoomsResponseDto,
  CreateRoomResponseDto,
  StartRoomResponseDto,
  JoinRoomResponseDto,
  getRoomStatus,
  StartMeetingData,
  JoinMeetingData,
  DetailedRoom,
  RoomDetailsResponseDto,
} from '@/services/api/types/konn3ct.types';

interface UseMeetingsPaginationState {
  pageIndex: number;
  pageSize: number;
}

interface UseMeetingsState {
  rooms: RoomWithStatus[];
  loading: boolean;
  error: string | null;
  pagination: UseMeetingsPaginationState;
  globalFilter: string;
  creating: boolean;
  starting: boolean;
  joining: boolean;
  startingMeeting: boolean;
  joiningMeeting: boolean;
  fetchingRoomDetails: boolean;
  deleting: boolean;
}

interface UseMeetingsActions {
  setPagination: (
    updaterOrValue:
      | UseMeetingsPaginationState
      | ((old: UseMeetingsPaginationState) => UseMeetingsPaginationState),
  ) => void;
  setGlobalFilter: (filter: string) => void;
  refetch: () => Promise<void>;
  createRoom: (data: CreateRoomDto) => Promise<CreateRoomResponseDto>;
  startRoom: (roomId: number, roomName: string) => Promise<StartRoomResponseDto>;
  joinRoom: (
    roomId: number,
    roomName: string,
    role?: 'moderator' | 'participant',
  ) => Promise<string>;
  startMeeting: (roomId: number, roomName: string, meetingData: StartMeetingData) => Promise<void>;
  getRoomDetails: (roomId: number) => Promise<DetailedRoom | null>;
  joinMeeting: (roomId: number, roomName: string, meetingData: JoinMeetingData) => Promise<void>;
  deleteRoom: (roomId: number, roomName: string) => Promise<void>;
}

// Helper function to fetch room status from API
async function fetchRoomStatus(
  roomId: number,
): Promise<{ status: RoomStatus; roomActive?: boolean; attendee?: any }> {
  try {
    const response = await konn3ctApiService.getRoomStatus(roomId);
    if (response.success) {
      return {
        status: getRoomStatus(response.roomActive),
        roomActive: response.roomActive,
        attendee:
          response.data && !Array.isArray(response.data) ? response.data.attendee : undefined,
      };
    }
    return { status: 'unknown' };
  } catch (error) {
    console.error(`Failed to fetch status for room ${roomId}:`, error);
    return { status: 'unknown' };
  }
}

export function useMeetings(): UseMeetingsState & UseMeetingsActions {
  const dispatch = useAppDispatch();
  const { employeeDetails } = useAuth();
  const router = useRouter();

  const [state, setState] = useState<UseMeetingsState>({
    rooms: [],
    loading: true,
    error: null,
    pagination: { pageIndex: 0, pageSize: 10 },
    globalFilter: '',
    creating: false,
    starting: false,
    joining: false,
    startingMeeting: false,
    joiningMeeting: false,
    fetchingRoomDetails: false,
    deleting: false,
  });

  const fetchRooms = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response: ListRoomsResponseDto = await konn3ctApiService.listRooms();

      if (response.success && response.data) {
        // Fetch status for each room
        const roomsWithStatus: RoomWithStatus[] = await Promise.all(
          response.data.map(async (room: Room) => {
            const statusData = await fetchRoomStatus(room.id);
            return {
              ...room,
              status: statusData.status,
              roomActive: statusData.roomActive,
              attendee: statusData.attendee,
            };
          }),
        );

        setState((prev) => ({
          ...prev,
          rooms: roomsWithStatus,
          loading: false,
          error: null,
        }));
      } else {
        setState((prev) => ({
          ...prev,
          rooms: [],
          loading: false,
          error: response.message || 'Failed to fetch rooms',
        }));
      }
    } catch (error: any) {
      console.error('Failed to fetch rooms:', error);
      setState((prev) => ({
        ...prev,
        rooms: [],
        loading: false,
        error: error.message || 'Failed to fetch rooms',
      }));
    }
  }, []);

  const createRoom = useCallback(
    async (data: CreateRoomDto): Promise<CreateRoomResponseDto> => {
      setState((prev) => ({ ...prev, creating: true }));

      try {
        const response = await konn3ctApiService.createRoom(data);

        if (response.success) {
          dispatch(
            showMessage({
              message: 'Meeting room created successfully',
              variant: 'success',
            }),
          );

          // Refresh rooms list
          await fetchRooms();
        } else {
          dispatch(
            showMessage({
              message: response.message || 'Failed to create room',
              variant: 'error',
            }),
          );
        }

        return response;
      } catch (error: any) {
        console.error('Failed to create room:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to create room',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, creating: false }));
      }
    },
    [dispatch, fetchRooms],
  );

  const startRoom = useCallback(
    async (roomId: number, roomName: string): Promise<StartRoomResponseDto> => {
      setState((prev) => ({ ...prev, starting: true }));

      try {
        const startData: StartRoomDto = {
          id: roomId,
          name: roomName,
          logout_url: konn3ctApiService.generateLogoutUrl(roomId),
          message: `Welcome to ${roomName}`,
          started_by: employeeDetails?.firstName || 'Moderator',
          keyword: `meeting-${roomId}-${Date.now()}`,
        };

        const response = await konn3ctApiService.startRoom(startData);

        if (response.success) {
          dispatch(
            showMessage({
              message: 'Meeting started successfully',
              variant: 'success',
            }),
          );

          // Refresh rooms list to update status
          await fetchRooms();
        } else {
          dispatch(
            showMessage({
              message: response.message || 'Failed to start meeting',
              variant: 'error',
            }),
          );
        }

        return response;
      } catch (error: any) {
        console.error('Failed to start room:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to start meeting',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, starting: false }));
      }
    },
    [dispatch, employeeDetails, fetchRooms],
  );

  const joinRoom = useCallback(
    async (
      roomId: number,
      roomName: string,
      role: 'moderator' | 'participant' = 'participant',
    ): Promise<string> => {
      setState((prev) => ({ ...prev, joining: true }));

      try {
        const joinData: JoinRoomDto = {
          id: roomId,
          name: employeeDetails?.firstName || 'User',
          email: employeeDetails?.email || '<EMAIL>',
          role: role === 'participant' ? 'viewer' : role, // Map 'participant' to 'viewer' for API
        };

        const response: JoinRoomResponseDto = await konn3ctApiService.joinRoom(joinData);

        if (response.success && response.data) {
          dispatch(
            showMessage({
              message: 'Joining meeting...',
              variant: 'success',
            }),
          );

          return response.data; // data is now a string URL
        } else {
          dispatch(
            showMessage({
              message: response.message || 'Failed to join meeting',
              variant: 'error',
            }),
          );
          throw new Error(response.message || 'Failed to join meeting');
        }
      } catch (error: any) {
        console.error('Failed to join room:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to join meeting',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, joining: false }));
      }
    },
    [dispatch, employeeDetails],
  );

  const setPagination = useCallback(
    (
      updaterOrValue:
        | UseMeetingsPaginationState
        | ((old: UseMeetingsPaginationState) => UseMeetingsPaginationState),
    ) => {
      setState((prev) => ({
        ...prev,
        pagination:
          typeof updaterOrValue === 'function' ? updaterOrValue(prev.pagination) : updaterOrValue,
      }));
    },
    [],
  );

  const setGlobalFilter = useCallback((filter: string) => {
    setState((prev) => ({ ...prev, globalFilter: filter }));
  }, []);

  const refetch = useCallback(() => fetchRooms(), [fetchRooms]);

  const getRoomDetails = useCallback(
    async (roomId: number): Promise<DetailedRoom | null> => {
      setState((prev) => ({ ...prev, fetchingRoomDetails: true }));

      try {
        const response: RoomDetailsResponseDto = await konn3ctApiService.getRoomDetails(roomId);

        if (response.success && response.data) {
          return response.data;
        } else {
          // Room not started scenario
          dispatch(
            showMessage({
              message:
                response.message || 'Meeting room is not active. Please start the meeting first.',
              variant: 'warning',
            }),
          );
          return null;
        }
      } catch (error: any) {
        console.error('Failed to get room details:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to get meeting details',
            variant: 'error',
          }),
        );
        return null;
      } finally {
        setState((prev) => ({ ...prev, fetchingRoomDetails: false }));
      }
    },
    [dispatch],
  );

  const joinMeeting = useCallback(
    async (roomId: number, roomName: string, meetingData: JoinMeetingData): Promise<void> => {
      setState((prev) => ({ ...prev, joiningMeeting: true }));

      try {
        // Prepare join room data
        const joinData: JoinRoomDto = {
          id: roomId,
          name:
            employeeDetails?.firstName && employeeDetails?.lastName
              ? `${employeeDetails.firstName} ${employeeDetails.lastName}`
              : 'Unknown User',
          email: employeeDetails?.email || '<EMAIL>',
          role: 'moderator',
          ...(meetingData.accessCode && { access_code: meetingData.accessCode }),
        };

        const joinResponse = await konn3ctApiService.joinRoom(joinData);

        if (!joinResponse.success || !joinResponse.data) {
          throw new Error(joinResponse.message || 'Failed to join meeting');
        }

        // Store URL and navigate to iframe viewer
        localStorage.setItem(`meeting_url_${roomId}`, joinResponse.data);
        localStorage.setItem(`meeting_room_name_${roomId}`, roomName);
        router.push(`/portal/e-office/e-meeting/room/${roomId}`);

        // Show success message
        dispatch(
          showMessage({
            message: `Successfully joined meeting in "${roomName}". Opening meeting room...`,
            variant: 'success',
          }),
        );

        // Refresh rooms list after a slight delay
        setTimeout(async () => {
          await fetchRooms();
        }, 2000); // 2 second delay to allow join to process
      } catch (error: any) {
        console.error('Failed to join meeting:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to join meeting',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, joiningMeeting: false }));
      }
    },
    [dispatch, employeeDetails, fetchRooms],
  );

  const deleteRoom = useCallback(
    async (roomId: number, roomName: string): Promise<void> => {
      setState((prev) => ({ ...prev, deleting: true }));

      try {
        const response = await konn3ctApiService.deleteRoom(roomId);

        if (response.success) {
          dispatch(
            showMessage({
              message: `Meeting room "${roomName}" deleted successfully`,
              variant: 'success',
            }),
          );

          // Refresh rooms list
          await fetchRooms();
        } else {
          dispatch(
            showMessage({
              message: response.message || 'Failed to delete room',
              variant: 'error',
            }),
          );
        }
      } catch (error: any) {
        console.error('Failed to delete room:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to delete room',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, deleting: false }));
      }
    },
    [dispatch, fetchRooms],
  );

  const startMeeting = useCallback(
    async (roomId: number, roomName: string, meetingData: StartMeetingData): Promise<void> => {
      setState((prev) => ({ ...prev, startingMeeting: true }));

      try {
        // First, check if room is available
        const statusResponse = await konn3ctApiService.getRoomStatus(roomId);

        if (statusResponse.success && statusResponse.roomActive) {
          throw new Error('This room is currently occupied. Please try again later.');
        }

        // Generate logout URL dynamically
        const logoutUrl = konn3ctApiService.getCurrentDomain() + '/portal/e-office/e-meeting/room/logout';

        // Prepare start room data
        const startData: StartRoomDto = {
          id: roomId,
          name: meetingData.meetingName,
          logout_url: logoutUrl,
          message: meetingData.welcomeMessage,
          started_by:
            employeeDetails?.firstName && employeeDetails?.lastName
              ? `${employeeDetails.firstName} ${employeeDetails.lastName}`
              : 'Unknown User',
          keyword: meetingData.meetingName.toLowerCase().replace(/\s+/g, '_'),
          ...(meetingData.accessCode && { access_code: meetingData.accessCode }),
        };

        // Step 1: Start the meeting
        const startResponse = await konn3ctApiService.startRoom(startData);

        if (!startResponse.success) {
          throw new Error(startResponse.message || 'Failed to start meeting');
        }

        // Step 2: Update loading state to joining
        setState((prev) => ({ ...prev, startingMeeting: false, joiningMeeting: true }));

        // Step 3: Auto-join as moderator
        const joinData: JoinRoomDto = {
          id: roomId,
          name:
            employeeDetails?.firstName && employeeDetails?.lastName
              ? `${employeeDetails.firstName} ${employeeDetails.lastName}`
              : 'Unknown User',
          email: employeeDetails?.email || '<EMAIL>',
          role: 'moderator',
          ...(meetingData.accessCode && { access_code: meetingData.accessCode }),
        };

        const joinResponse = await konn3ctApiService.joinRoom(joinData);

        if (!joinResponse.success || !joinResponse.data) {
          throw new Error(joinResponse.message || 'Failed to join meeting');
        }

        // Step 4: Store URL and navigate to iframe viewer
        localStorage.setItem(`meeting_url_${roomId}`, joinResponse.data);
        localStorage.setItem(`meeting_room_name_${roomId}`, meetingData.meetingName);
        router.push(`/portal/e-office/e-meeting/room/${roomId}`);

        // Step 5: Show success message
        dispatch(
          showMessage({
            message: `Meeting "${meetingData.meetingName}" started successfully. Opening meeting room...`,
            variant: 'success',
          }),
        );

        // Step 6: Refresh rooms list after a slight delay
        setTimeout(async () => {
          await fetchRooms();
        }, 2000); // 2 second delay to allow join to process
      } catch (error: any) {
        console.error('Failed to start meeting:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to start meeting',
            variant: 'error',
          }),
        );
        throw error;
      } finally {
        setState((prev) => ({ ...prev, startingMeeting: false, joiningMeeting: false }));
      }
    },
    [dispatch, employeeDetails, fetchRooms],
  );

  // Initial fetch
  useEffect(() => {
    fetchRooms();
  }, [fetchRooms]);

  // Polling disabled - users can manually refresh if needed
  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     fetchRooms();
  //   }, 60000); // 1 minute
  //   return () => clearInterval(intervalId);
  // }, [fetchRooms]);

  return {
    ...state,
    setPagination,
    setGlobalFilter,
    refetch,
    createRoom,
    startRoom,
    joinRoom,
    startMeeting,
    getRoomDetails,
    joinMeeting,
    deleteRoom,
  };
}
