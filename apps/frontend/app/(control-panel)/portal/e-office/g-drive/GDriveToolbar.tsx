'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Box,
  TextField,
  IconButton,
  Button,
  Menu,
  MenuItem,
  Divider,
  Typography,
  Breadcrumbs,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
} from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import Link from '@/@fuse/core/Link';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useDropzone } from 'react-dropzone';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { showMessage } from '@/@fuse/core/FuseMessage/fuseMessageSlice';
import {
  selectDisplayMode,
  selectSearchQuery,
  toggleDisplayMode,
  setSearchQuery,
  toggleLeftSidebar,
} from './gDriveSlice';
import { useGDriveUpload } from './hooks/useGDriveUpload';
import {
  acceptStyle,
  baseStyle,
  focusedStyle,
  img,
  rejectStyle,
  thumb,
  thumbInner,
  thumbsContainer,
} from './upload-styles';
import { GDriveFolderResponseDto } from '@/services/api/types/g-drive.types';

interface GDriveToolbarProps {
  path: GDriveFolderResponseDto[];
  createFolder: (folderName: string) => Promise<void>;
  refetch: () => void;
  currentFolderId: string | null;
}

const StyledBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const SearchField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: alpha(theme.palette.text.primary, 0.05),
    '&:hover': {
      backgroundColor: alpha(theme.palette.text.primary, 0.08),
    },
    '&.Mui-focused': {
      backgroundColor: alpha(theme.palette.text.primary, 0.08),
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: theme.palette.primary.main,
      },
    },
  },
}));

/**
 * GDrive Toolbar component with search, view toggle, breadcrumbs, and New button
 */
function GDriveToolbar({ path, createFolder, refetch, currentFolderId }: GDriveToolbarProps) {
  const dispatch = useAppDispatch();
  const displayMode = useAppSelector(selectDisplayMode);
  const searchQuery = useAppSelector(selectSearchQuery);

  const baseGDrivePath = '/portal/e-office/g-drive';

  // New button menu state
  const [newMenuAnchor, setNewMenuAnchor] = useState<null | HTMLElement>(null);
  const isNewMenuOpen = Boolean(newMenuAnchor);

  // Create folder dialog state
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [folderName, setFolderName] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // File upload dialog state
  const [isUploadOpen, setIsUploadOpen] = useState(false);

  // Use the upload hook
  const {
    uploadedFiles,
    validationErrors,
    isUploading,
    handleFileChange,
    removeFile,
    uploadFiles,
    reset: resetUpload,
    formatFileSize,
  } = useGDriveUpload();

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  // Handle New button menu
  const handleNewMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setNewMenuAnchor(event.currentTarget);
  };

  const handleNewMenuClose = () => {
    setNewMenuAnchor(null);
  };

  // Handle create folder
  const handleCreateFolderClick = () => {
    setNewMenuAnchor(null);
    setIsCreateFolderOpen(true);
  };

  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      dispatch(
        showMessage({
          message: 'Please enter a folder name',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
      return;
    }

    setIsCreating(true);
    try {
      await createFolder(folderName.trim());
      setIsCreateFolderOpen(false);
      setFolderName('');
      dispatch(
        showMessage({
          message: 'Folder created successfully',
          variant: 'success',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    } catch (error: any) {
      dispatch(
        showMessage({
          message: error.message || 'Failed to create folder',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    } finally {
      setIsCreating(false);
    }
  };

  const handleCancelCreateFolder = () => {
    setIsCreateFolderOpen(false);
    setFolderName('');
    setIsCreating(false);
  };

  // Handle file upload
  const handleUploadClick = () => {
    setNewMenuAnchor(null);
    setIsUploadOpen(true);
  };

  // Dropzone configuration
  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.png', '.jpg', '.svg'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
    },
    maxFiles: 10,
    maxSize: 10485760, // 10MB
    onDrop: (acceptedFiles, fileRejections) => {
      handleFileChange(acceptedFiles, fileRejections);
    },
  });

  const dropzoneStyle = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject],
  );

  const handleUploadFiles = async () => {
    if (uploadedFiles.length === 0) {
      dispatch(
        showMessage({
          message: 'Please select files to upload',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
      return;
    }

    try {
      const success = await uploadFiles(currentFolderId || undefined);

      if (success) {
        setIsUploadOpen(false);
        resetUpload();
        await refetch();
        dispatch(
          showMessage({
            message: `Files uploaded successfully`,
            variant: 'success',
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'right',
            },
          }),
        );
      }
    } catch (error: any) {
      dispatch(
        showMessage({
          message: error.message || 'Failed to upload files',
          variant: 'error',
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'right',
          },
        }),
      );
    }
  };

  const handleCancelUpload = () => {
    setIsUploadOpen(false);
    resetUpload();
  };

  return (
    <>
      <StyledBox className="flex flex-col">
        {/* Top section with breadcrumbs */}
        <Box className="px-6 pt-4 pb-2">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Breadcrumbs
              aria-label="breadcrumb"
              separator={<NavigateNextIcon fontSize="small" />}
              className="mb-2"
            >
              {path && path.length > 0 && (
                <Link href={baseGDrivePath} className="text-sm hover:underline">
                  My Drive
                </Link>
              )}
              {(!path || path.length === 0) && (
                <Typography color="text.secondary" className="text-sm">
                  My Drive
                </Typography>
              )}
              {path?.map((item, index) =>
                index + 1 === path.length ? (
                  <Typography key={index} color="text.primary" className="text-sm">
                    {item?.folderName}
                  </Typography>
                ) : (
                  <Link
                    key={index}
                    href={`${baseGDrivePath}/${item?.id}`}
                    className="text-sm hover:underline"
                  >
                    {item?.folderName}
                  </Link>
                ),
              )}
            </Breadcrumbs>
          </motion.div>
        </Box>

        {/* Main toolbar */}
        <Box className="flex items-center justify-between px-6 py-3">
          {/* Left section: Menu button + Back button + Search */}
          <Box className="flex items-center gap-2">
            <IconButton onClick={() => dispatch(toggleLeftSidebar())} size="medium">
              <FuseSvgIcon>heroicons-outline:bars-3</FuseSvgIcon>
            </IconButton>

            {/* Back to home button - only show when in a folder */}
            {currentFolderId && (
              <IconButton onClick={() => window.history.back()} size="medium" title="Back">
                <FuseSvgIcon>heroicons-outline:arrow-left</FuseSvgIcon>
              </IconButton>
            )}

            <SearchField
              size="small"
              placeholder="Search in Drive"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <FuseSvgIcon className="text-gray-400 mr-2" size={20}>
                    heroicons-outline:magnifying-glass
                  </FuseSvgIcon>
                ),
              }}
              sx={{ width: 320 }}
            />
          </Box>

          {/* Right section: View toggle + New button */}
          <Box className="flex items-center gap-2">
            <IconButton
              onClick={() => dispatch(toggleDisplayMode())}
              size="medium"
              title={`Switch to ${displayMode === 'list' ? 'grid' : 'list'} view`}
            >
              <FuseSvgIcon>
                {displayMode === 'list'
                  ? 'heroicons-outline:squares-2x2'
                  : 'heroicons-outline:list-bullet'}
              </FuseSvgIcon>
            </IconButton>

            <Button
              variant="contained"
              startIcon={<FuseSvgIcon>heroicons-outline:plus</FuseSvgIcon>}
              onClick={handleNewMenuClick}
            >
              New
            </Button>
          </Box>
        </Box>
      </StyledBox>

      {/* New Button Menu */}
      <Menu
        anchorEl={newMenuAnchor}
        open={isNewMenuOpen}
        onClose={handleNewMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { minWidth: 200 },
        }}
      >
        <MenuItem onClick={handleCreateFolderClick}>
          <FuseSvgIcon className="mr-3">heroicons-outline:folder-plus</FuseSvgIcon>
          New folder
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleUploadClick}>
          <FuseSvgIcon className="mr-3">heroicons-outline:arrow-up-tray</FuseSvgIcon>
          File upload
        </MenuItem>
      </Menu>

      {/* Create Folder Dialog */}
      <Dialog
        open={isCreateFolderOpen}
        onClose={handleCancelCreateFolder}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { minWidth: 400 },
        }}
      >
        <DialogTitle className="pb-2">
          <Typography variant="h6" component="div" className="font-semibold">
            Create New Folder
          </Typography>
        </DialogTitle>
        <DialogContent className="pb-4">
          <TextField
            autoFocus
            margin="normal"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isCreating) {
                handleCreateFolder();
              }
            }}
            disabled={isCreating}
            placeholder="Enter folder name..."
          />
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button onClick={handleCancelCreateFolder} disabled={isCreating} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleCreateFolder}
            variant="contained"
            disabled={isCreating || !folderName.trim()}
            startIcon={isCreating ? <CircularProgress size={16} /> : null}
            className="ml-2"
          >
            {isCreating ? 'Creating...' : 'Create Folder'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Files Dialog */}
      <Dialog
        open={isUploadOpen}
        onClose={handleCancelUpload}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { minWidth: 400 },
        }}
      >
        <DialogTitle className="pb-2">
          <Typography variant="h6" component="div" className="font-semibold">
            Upload Files
          </Typography>
        </DialogTitle>
        <DialogContent>
          <div className="mt-2 mb-4">
            <div {...getRootProps({ style: dropzoneStyle })}>
              <input {...getInputProps()} />
              <p>Drag 'n' drop files here, or click to select files</p>
              <small>
                Max 10 files, 10MB each. Allowed: images, PDF, documents, spreadsheets,
                presentations.
              </small>
            </div>
            {validationErrors && validationErrors.length > 0 && (
              <Alert severity="warning" className="mt-2">
                <AlertTitle>File Issues</AlertTitle>
                <ul>
                  {validationErrors.map((err, index) => (
                    <li key={index}>
                      {err.fileName}: {err.message}
                    </li>
                  ))}
                </ul>
              </Alert>
            )}
            <aside style={{ ...thumbsContainer, marginTop: '16px' }}>
              {uploadedFiles.map((upFile) => (
                <div
                  className="flex items-center gap-3 mb-2 p-2 border rounded"
                  key={upFile.id}
                  style={{ width: '100%' }}
                >
                  <div style={thumb}>
                    <div style={thumbInner}>
                      {upFile.file.type === 'application/pdf' ? (
                        <img
                          src="/assets/images/icons/pdf-icon.png"
                          alt="PDF file"
                          style={{ ...img, objectFit: 'contain' as const }}
                        />
                      ) : upFile.preview ? (
                        <img src={upFile.preview} style={img} alt={upFile.file.name} />
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-gray-100 text-gray-500 text-xs">
                          {upFile.file.name.split('.').pop()?.toUpperCase() || 'FILE'}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="file-info flex flex-col flex-grow">
                    <Typography variant="subtitle1" className="">
                      {upFile.file.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {upFile.file.type || 'Unknown type'} • {formatFileSize(upFile.file.size)}
                    </Typography>
                    {upFile.error && (
                      <Typography variant="caption" color="error">
                        Error: {upFile.error}
                      </Typography>
                    )}
                    {upFile.status === 'uploading' && (
                      <Typography variant="caption" color="textSecondary">
                        Uploading: {upFile.progress}%
                      </Typography>
                    )}
                    {upFile.status === 'completed' && (
                      <Typography variant="caption" color="success.main">
                        Uploaded
                      </Typography>
                    )}
                  </div>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => removeFile(upFile.id)}
                    disabled={isUploading || upFile.status === 'uploading'}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </aside>
          </div>
        </DialogContent>
        <DialogActions className="px-6 pb-4">
          <Button onClick={handleCancelUpload} disabled={isUploading} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleUploadFiles}
            variant="contained"
            disabled={isUploading || uploadedFiles.length === 0}
            startIcon={isUploading ? <CircularProgress size={16} /> : null}
            className="ml-2"
          >
            {isUploading ? 'Uploading...' : `Upload ${uploadedFiles.length} file(s)`}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default GDriveToolbar;
