//Indonesian , Indonesia
RTE_DefaultConfig.text_language = "bahasa";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "membatalkan";	//"Cancel"
RTE_DefaultConfig.text_normal = "normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Judul 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Berita utama 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Judul 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Berita utama 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Judul utama 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Judul 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Berita utama 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "menutup";	//"Close"
RTE_DefaultConfig.text_bold = "bold";	//"Bold"
RTE_DefaultConfig.text_italic = "miring";	//"Italic"
RTE_DefaultConfig.text_underline = "menggarisbawahi";	//"Underline"
RTE_DefaultConfig.text_strike = "Garis mogok";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Huruf besar";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Huruf kecil";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Hapus format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Masukkan link";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Buka tautan";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Edit link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Hapus tautan";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Tinggi garis";	//"Line Height"
RTE_DefaultConfig.text_indent = "indent";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdentasi";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Penawaran blok";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Daftar memerintahkan";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Daftar unordered";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Masukkan horizontal Rule";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Masukkan tanggal";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Sisipkan tabel";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Sisipkan gambar";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Masukkan video";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Sisipkan kode";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Buat PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Sisipkan EMOJI";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Karakter khusus";	//"Special characters"
RTE_DefaultConfig.text_characters = "karakter";	//"Characters"
RTE_DefaultConfig.text_fontname = "font";	//"Font"
RTE_DefaultConfig.text_fontsize = "ukuran";	//"Size"
RTE_DefaultConfig.text_forecolor = "Warna teks";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Kembali warna";	//"Back Color"
RTE_DefaultConfig.text_justify = "membenarkan";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Membenarkan waktu";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Membenarkan hak";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Pusat pembenaran";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Membenarkan kendali";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Tidak membenarkan";	//"Justify None"
RTE_DefaultConfig.text_delete = "menghapus";	//"Delete"
RTE_DefaultConfig.text_save = "Simpan file";	//"Save file"
RTE_DefaultConfig.text_selectall = "Pilih Semua";	//"Select All"
RTE_DefaultConfig.text_code = "Kode HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "tinjauan";	//"Preview"
RTE_DefaultConfig.text_print = "cetak";	//"Print"
RTE_DefaultConfig.text_undo = "membatalkan";	//"Undo"
RTE_DefaultConfig.text_redo = "mengulang";	//"Redo"
RTE_DefaultConfig.text_more = "Lebih...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Doc baru";	//"New Doc"
RTE_DefaultConfig.text_help = "membantu";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Cocok untuk jendela";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Keluar dari layar penuh";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor gambar";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Gaya gambar";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Gaya inline";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Gaya paragraf";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Gaya tautan";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "gaya";	//"Styles"
RTE_DefaultConfig.text_cssclass = "CSS kelas";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Dengan URL";	//"By Url"
RTE_DefaultConfig.text_upload = "meng";	//"Upload"
RTE_DefaultConfig.text_size = "ukuran";	//"Size"
RTE_DefaultConfig.text_text = "teks";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Buka di tab baru";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "menyisipkan";	//"Insert"
RTE_DefaultConfig.text_update = "update";	//"Update"
RTE_DefaultConfig.text_find = "Cari & Ganti";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "menemukan";	//"Find"
RTE_DefaultConfig.text_replacewith = "ganti";	//"Replace"
RTE_DefaultConfig.text_findnext = "berikutnya";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ganti";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Ganti semua";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Kasus pertandingan";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Cocokkan kata";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Pindah ke bawah";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Naik";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Ukuran otomatis";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% lebar";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% lebar";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% lebar";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "lebar 25%";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Ukuran set";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Teks Alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "membenarkan";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Keterangan gambar";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Gabung sel";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Sel Split vertikal";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Sel Split horisontal";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Warna teks sel";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Sel kembali warna";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Sisipkan baris di atas";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Sisipkan baris di bawah ini";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Masukkan kolom ke kiri";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Masukkan kolom kanan";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Hapus kolom";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Hapus baris";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Hapus tabel";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Ukuran otomatis";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Header tabel";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Menambahkan paragraf baru";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "pasta";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "pasta";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Tempel teks";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Tempel sebagai HTML";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Tempel kata";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Silakan gunakan CTRL + V untuk Menempelkan konten ke dalam kotak di bawah ini. \R\nkonten akan dibersihkan secara otomatis.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Naik";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Pindah ke bawah";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplikat";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "menghapus";	//"Delete"
RTE_DefaultConfig.text_pmore = "Lebih..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Lebih..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Beralih perbatasan";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "memotong";	//"Cut"
RTE_DefaultConfig.text_copy = "salinan";	//"Copy"
RTE_DefaultConfig.text_copied = "disalin";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Sisipkan Galeri";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Sisipkan dokumen";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Sisipkan template";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "tinjauan";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "Tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "tabel";	//"Table"
RTE_DefaultConfig.text_tablecell = "Tabel sel";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Baris tabel";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Kolom tabel";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "otomatis";	//"Automatic"
RTE_DefaultConfig.text_colormore = "lebih";	//"More"
RTE_DefaultConfig.text_colorpicker = "Pemilih warna";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Palet web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Warna bernama";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "dasar";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "penambahan";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Drag dan drop";	//"Drag and drop"
RTE_DefaultConfig.text_or = "atau";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klik untuk mengunggah";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Default gambar caption";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "cari";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Teks yang akan ditambahkan telah mencapai batas karakter untuk bidang ini.";	//"The text to be added has reached the character limit for this field."
