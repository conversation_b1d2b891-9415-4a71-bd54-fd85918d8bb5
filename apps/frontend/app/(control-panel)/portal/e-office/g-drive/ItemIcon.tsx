'use client';

import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import { amber, blue, green, grey, red } from '@mui/material/colors';
import React from 'react';

// TypeBadge styled component, exactly as per user template structure
const TypeBadge = styled(Box)((props: { color: string }) => ({
  backgroundColor:
    {
      PDF: red[600],
      DOC: blue[600],
      XLS: green[600],
      TXT: grey[600],
      JPG: amber[600],
      // Add other types from GDrive data if they need distinct badges
      // For example, if 'PPT' was needed:
      // PPT: orange[600],
    }[props.color] || grey[500], // Default if color prop doesn't match
}));

type ItemIconProps = {
  type: string; // This is our GDriveItem.type, e.g., "application/pdf", "folder", "google-apps.document"
};

// Helper function to map GDriveItem.type to the simple badge string (PDF, DOC, etc.)
// This string is used for BOTH the TypeBadge's 'color' prop and its displayed text.
const getBadgeTypeFromString = (gDriveType: string): string | null => {
  if (!gDriveType || typeof gDriveType !== 'string') return null;
  const lowerType = gDriveType.toLowerCase();

  if (lowerType === 'folder') return null; // Folders don't get a text badge in the template

  // Prioritize specific GDrive types if known, then general MIME types or extensions
  if (
    lowerType.includes('google-apps.document') ||
    lowerType.includes('msword') ||
    lowerType.includes('wordprocessingml')
  )
    return 'DOC';
  if (
    lowerType.includes('google-apps.spreadsheet') ||
    lowerType.includes('ms-excel') ||
    lowerType.includes('spreadsheetml')
  )
    return 'XLS';
  if (
    lowerType.includes('google-apps.presentation') ||
    lowerType.includes('ms-powerpoint') ||
    lowerType.includes('presentationml')
  )
    return 'PPT'; // Assuming PPT might be needed
  if (lowerType.includes('pdf')) return 'PDF';
  if (lowerType.includes('text') || lowerType.includes('txt')) return 'TXT';
  if (
    lowerType.includes('jpeg') ||
    lowerType.includes('jpg') ||
    lowerType.includes('png') ||
    lowerType.includes('gif') ||
    lowerType.startsWith('image/')
  )
    return 'JPG';

  // Fallback for other GDrive types if a generic badge is desired, e.g. for 'archive', 'video'
  // For now, returning null if no specific match, to align with template's limited badge types.
  return null;
};

/**
 * The item icon component, revised to match user's template.
 */
function ItemIcon(props: ItemIconProps) {
  const { type: gDriveItemType } = props; // Original type from GDriveItemModel

  if (gDriveItemType && gDriveItemType.toLowerCase() === 'folder') {
    return (
      <FuseSvgIcon size={56} color="disabled">
        heroicons-outline:folder
      </FuseSvgIcon>
    );
  }

  // Determine the badge type (e.g., "PDF", "DOC") from the gDriveItemType
  const badgeDisplayType = getBadgeTypeFromString(gDriveItemType);

  return (
    <div className="relative">
      {' '}
      {/* Wrapper for positioning the badge */}
      <FuseSvgIcon size={56} color="disabled">
        heroicons-outline:document {/* Base icon for all non-folders as per template */}
      </FuseSvgIcon>
      {badgeDisplayType && ( // Only render TypeBadge if a displayable type is derived
        <TypeBadge
          color={badgeDisplayType} // Pass "PDF", "DOC", etc. to determine background
          className="absolute left-0 bottom-0 px-1.5 rounded-sm text-md font-semibold leading-normal text-white"
        >
          {badgeDisplayType} {/* Display "PDF", "DOC", etc. as text */}
        </TypeBadge>
      )}
    </div>
  );
}

export default ItemIcon;
