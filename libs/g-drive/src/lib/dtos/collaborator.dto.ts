import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { QueryOptionsDto } from '@igov/common';
import { EmployeeResponseDto } from '@igov/employee';

export class QueryCollaboratorDto extends QueryOptionsDto {
  registryId!: string;
}

export class CreateCollaboratorDto {
  @ApiProperty()
  @IsUUID()
  employeeId!: string;
}

export class CreateCollaboratorsDto {
  @ApiProperty()
  @IsUUID('all', { each: true })
  employeeIds!: string[];
}

export class RegistryCollaboratorResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  registryId!: string;

  @ApiProperty()
  employeeId!: string;

  @ApiProperty({ required: false, type: () => EmployeeResponseDto })
  employee?: EmployeeResponseDto;

  @ApiProperty()
  createdAt?: Date;

  @ApiProperty()
  updatedAt?: Date;
}

export class PaginatedRegistryCollaboratorResponseDto {
  @ApiProperty({ type: [RegistryCollaboratorResponseDto] })
  data!: RegistryCollaboratorResponseDto[];

  @ApiProperty()
  total!: number;
}
