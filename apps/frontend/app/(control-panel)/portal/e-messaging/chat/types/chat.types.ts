// Frontend chat types for UI components

export type ContactStatusType = 'online' | 'away' | 'do-not-disturb' | 'offline';

// Message type that matches API ChatMessageResponseDto exactly
export type Message = {
  id: string;
  senderId: string;          // ✅ Matches API
  receiverId: string;        // ✅ Matches API
  content: string;           // ✅ Matches API (was 'value')
  deliveredAt?: Date;        // ✅ Matches API
  readAt?: Date;             // ✅ Matches API
  isRead?: boolean;          // ✅ Matches API
  isEdited?: boolean;        // ✅ Matches API
  createdAt?: string;        // ✅ Matches API
  updatedAt?: string;        // ✅ Matches API
};

// Chat conversation type (derived from messages)
export type Chat = {
  id: string;                // Will use receiverId as chat ID
  contactIds: string[];      // [receiverId, currentUserId]
  unreadCount: number;       // Calculated from isRead flags
  muted: boolean;            // Not in API yet, default false
  lastMessage: string;       // From latest message content
  lastMessageAt: string;     // From latest message createdAt
  messages: Message[];       // Array of API messages
};

// Contact information (will need to integrate with user/employee API)
export type Contact = {
  id: string;                // User ID
  avatar?: string | null;    // User avatar
  name: string;              // User display name
  about: string;             // User bio/description
  details: {
    emails: {
      email: string;
      label: string;
    }[];
    phoneNumbers: {
      country: string;
      phoneNumber: string;
      label: string;
    }[];
    title?: string;          // Job title
    company: string;         // Department/ministry
    birthday: string;
    address: string;
  };
  attachments: {
    media: string[];
    docs: string[];
    links: string[];
  };
  status: ContactStatusType; // Online status
};

export type Task = {
  id: string;
  type: string;
  title: string;
  notes: string;
  completed: boolean;
  dueDate?: string | null;
  priority: number;
  tags: string[];
  assignedTo?: string;
  subTasks: {
    id: string;
    title: string;
    completed: boolean;
  }[];
  order: number;
};

export type Profile = {
  id: string;
  name: string;
  email: string;
  avatar: string;
  about: string;
  status: ContactStatusType;
};