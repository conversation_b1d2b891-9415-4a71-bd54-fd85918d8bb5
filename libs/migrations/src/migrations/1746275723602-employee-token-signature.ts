import { MigrationInterface, QueryRunner } from 'typeorm';

export class EmployeeTokenSignature1746275723602 implements MigrationInterface {
  name = 'EmployeeTokenSignature1746275723602';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "employees" ADD "e_token" character varying`);
    await queryRunner.query(`ALTER TABLE "employees" ADD "e_signature" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "employees" DROP COLUMN "e_signature"`);
    await queryRunner.query(`ALTER TABLE "employees" DROP COLUMN "e_token"`);
  }
}
