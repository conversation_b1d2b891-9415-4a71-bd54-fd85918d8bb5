# Development Dockerfile for NestJS Websocket

# Use Node 20 Alpine image
FROM node:20-alpine

# Install necessary dependencies
RUN apk add --no-cache bash

# Set working directory
WORKDIR /app

# Copy project configuration files
COPY package*.json nx.json tsconfig*.json ./

# Install NX globally
RUN npm install -g nx

# Install project dependencies with legacy peer deps to resolve conflicts
RUN npm install --legacy-peer-deps

# Copy the entire project
COPY . .

# Expose the port the app runs on
EXPOSE 3002

# Default command (can be overridden in docker-compose)
CMD ["npx", "nx", "run", "websocket:serve:development"]
