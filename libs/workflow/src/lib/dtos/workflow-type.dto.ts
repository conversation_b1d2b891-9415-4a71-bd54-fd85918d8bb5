import { ApiProperty } from '@nestjs/swagger';
import { IsDefined, IsNotEmpty, IsString } from 'class-validator';

export class createWorkflowTypeDto {
  @ApiProperty({ description: 'Workflow type name' })
  @IsDefined({ message: 'Workflow type name is required' })
  @IsNotEmpty({ message: 'Workflow type name cannot be empty' })
  @IsString({ message: 'Workflow type name must be a string' })
  workflowType!: string;
}

export class updateWorkflowTypeDto {
  @ApiProperty({ description: 'Workflow type name' })
  @IsDefined({ message: 'Workflow type name is required' })
  @IsNotEmpty({ message: 'Workflow type name cannot be empty' })
  @IsString({ message: 'Workflow type name must be a string' })
  workflowType!: string;
}

export class deleteWorkflowTypeDto {
  @ApiProperty({ description: 'Workflow type ID' })
  @IsDefined({ message: 'Workflow type ID is required' })
  @IsNotEmpty({ message: 'Workflow type ID must be a string' })
  id!: string;
}

export class WorkflowTypeResponseDto {
  @ApiProperty({ description: 'Workflow type ID' })
  id!: string;

  @ApiProperty({ description: 'Workflow type name' })
  workflowType!: string;
}
