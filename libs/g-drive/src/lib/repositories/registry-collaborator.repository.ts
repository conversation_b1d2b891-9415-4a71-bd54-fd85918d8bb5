import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository, IPagination } from '@igov/common';
import { RegistryCollaboratorEntity } from '../entities';
import { IRegistryCollaborator } from '../interfaces';

@Injectable()
export class RegistryCollaboratorRepository extends BaseRepository<RegistryCollaboratorEntity> {
  override entityClassName = RegistryCollaboratorEntity;
  private readonly logger = new Logger(RegistryCollaboratorRepository.name);

  async findAll(
    query: Partial<IRegistryCollaborator>,
    pagination: IPagination,
  ): Promise<{ data: RegistryCollaboratorEntity[]; total: number }> {
    const { registryId, employeeId } = query;
    const { limit, skip } = pagination;

    const queryBuilder = this.repository
      .createQueryBuilder('registryCollaborator')
      .leftJoin('registryCollaborator.employee', 'employee')
      .addSelect(['employee.userId', 'employee.firstName', 'employee.lastName'])
      .leftJoinAndSelect('registryCollaborator.registry', 'registry');

    if (registryId)
      queryBuilder.andWhere('registryCollaborator.registryId = :id', { id: registryId });

    if (employeeId)
      queryBuilder.andWhere('registryCollaborator.employeeId = :employeeId', {
        employeeId: employeeId,
      });

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total Registry Collaborators found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const registryCollaborators = await queryBuilder.getMany();

    return { data: registryCollaborators, total: totalCount };
  }

  async findByRegistryId(registryId: string): Promise<RegistryCollaboratorEntity[]> {
    return this.repository.find({ where: { registryId } });
  }

  async findByEmployeeId(employeeId: string): Promise<RegistryCollaboratorEntity[]> {
    return this.repository.find({ where: { employeeId } });
  }

  async findByRegistryIdAndEmployeeId(
    registryId: string,
    employeeId: string,
  ): Promise<RegistryCollaboratorEntity | null> {
    return this.repository.findOne({ where: { registryId, employeeId } });
  }

  async saveCollaborator(
    data: Partial<IRegistryCollaborator>[],
  ): Promise<RegistryCollaboratorEntity[]> {
    return this.repository.save(data);
  }
}
