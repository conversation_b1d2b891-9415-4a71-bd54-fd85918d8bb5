import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Unit } from './unit.entity';
import { IDepartment, IUnit } from '../interfaces';
import { BaseEntity } from '@igov/common';

@Entity('departments')
export class Department extends BaseEntity implements IDepartment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ length: 255 })
  code!: string;

  @Column({ length: 255 })
  name!: string;

  @Column({ type: 'text', nullable: true })
  commRef?: string;

  @OneToMany(() => Unit, (unit) => unit.department, {
    eager: false,
    cascade: [],
  })
  units!: IUnit[];
}
