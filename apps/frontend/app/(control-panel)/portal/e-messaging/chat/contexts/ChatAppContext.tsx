import { createContext } from 'react';

export type ChatAppContextType = {
  setMainSidebarOpen: (isOpen?: boolean) => void;
  setContactSidebarOpen: (contactId?: string) => void;
  setUserSidebarOpen: (isOpen?: boolean) => void;
  contactSidebarOpen?: string;
};

const ChatAppContext = createContext<ChatAppContextType>({
  setMainSidebarOpen: () => {},
  setContactSidebarOpen: (_T: string) => {},
  setUserSidebarOpen: () => {},
  contactSidebarOpen: null,
});

export default ChatAppContext;
