'use client';

import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { styled } from '@mui/material/styles';
import FusePageSimple from '@/@fuse/core/FusePageSimple';
import NewWorkflowHeader from './NewWorkflowHeader';
import Card from '@mui/material/Card';
import { motion } from 'motion/react';
import {
  Alert,
  AlertTitle,
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { useEffect, useMemo, useRef, useState } from 'react';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import { useDropzone } from 'react-dropzone';
import {
  acceptStyle,
  baseStyle,
  focusedStyle,
  img,
  rejectStyle,
  thumb,
  thumbInner,
  thumbsContainer,
} from './drop-zone-styles';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { generateReferenceNumber } from '@/app/(control-panel)/portal/shared/utils/referenceGenerator';
import { useWorkflowCreation } from '../useWorkflowCreation';
import { workflowTypeApiService } from '@/services/api/workflowTypeService';
import { WorkflowTypeResponseDto } from '@/services/api/types/workflow.types';
import { useEmployees } from '@/app/(control-panel)/portal/shared/hooks/useEmployees';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.text.primary,
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

interface WorkflowFormData {
  title: string;
  workflowTypeId: string;
  amount: string;
  description: string;
  authorizingUser: string;
}

const workflowSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters long.'),
  workflowTypeId: z.string().min(1, 'Workflow type is required.'),
  amount: z.string().min(1, 'Amount is required.'),
  description: z.string().min(10, 'Description must be at least 10 characters long.'),
  authorizingUser: z.string().min(1, 'Authorizing user is required.'),
});

/**
 * NewWorkflow component for creating a new workflow.
 * It handles form input, validation, file attachments, and submission to the backend API.
 */
function NewWorkflow() {
  const editorRef = useRef<RTEEditorRef>(null);
  const { employeeDetails } = useAuth();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset: resetForm,
    formState: { errors, isSubmitting: isRHFSubmitting },
  } = useForm<WorkflowFormData>({
    resolver: zodResolver(workflowSchema),
    defaultValues: {
      title: '',
      workflowTypeId: '',
      amount: '',
      description: '',
      authorizingUser: '',
    },
  });

  const [workflowTypes, setWorkflowTypes] = useState<WorkflowTypeResponseDto[]>([]);
  const [isLoadingWorkflowTypes, setIsLoadingWorkflowTypes] = useState(true);
  const [workflowTypesError, setWorkflowTypesError] = useState<string | null>(null);

  const {
    createWorkflow: submitWorkflowToApi,
    isSubmitting: isApiSubmitting,
    submissionError: apiSubmissionError,
    uploadedFiles,
    handleFileChange,
    removeFile,
    attachmentValidationErrors,
    reset: resetWorkflowCreationHook,
  } = useWorkflowCreation();

  const {
    groupedEmployeesByDepartment,
    isLoading: isLoadingEmployees,
    error: employeesError,
  } = useEmployees();
  // Fetch workflow types on mount
  useEffect(() => {
    const fetchWorkflowTypes = async () => {
      try {
        setIsLoadingWorkflowTypes(true);
        const types = await workflowTypeApiService.getWorkflowTypes();
        setWorkflowTypes(types);
        setWorkflowTypesError(null);
      } catch (error: any) {
        console.error('Failed to fetch workflow types:', error);
        setWorkflowTypesError(error.message || 'Failed to load workflow types');
      } finally {
        setIsLoadingWorkflowTypes(false);
      }
    };
    fetchWorkflowTypes();
  }, []);

  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
      'image/jpeg': ['.jpeg', '.jpg'],
      'image/png': ['.png'],
      'image/svg+xml': ['.svg'],
      'image/webp': ['.webp'],
      'image/gif': ['.gif'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    onDrop: (acceptedFiles, fileRejections) => {
      handleFileChange(acceptedFiles, fileRejections);
    },
  });

  const dropzoneStyle = useMemo(
    () => ({
      ...baseStyle,
      ...(isFocused ? focusedStyle : {}),
      ...(isDragAccept ? acceptStyle : {}),
      ...(isDragReject ? rejectStyle : {}),
    }),
    [isFocused, isDragAccept, isDragReject],
  );

  const formatFileSize = (sizeInBytes: number) => {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} bytes`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  // Define smaller thumbnail styles
  const smallerThumb = {
    ...thumb,
    width: 60,
    height: 60,
  };

  const smallerThumbInner = {
    ...thumbInner,
    width: 60,
    height: 60,
  };

  const displayedThumbs = uploadedFiles.map((upFile) => (
    <div
      className="flex items-center gap-3 mb-2 p-2 border rounded"
      key={upFile.id}
      style={{ width: '100%' }}
    >
      <div style={smallerThumb}>
        <div style={smallerThumbInner}>
          {upFile.file.type === 'application/pdf' ? (
            <img
              src="/assets/images/icons/pdf-icon.png"
              alt="PDF file"
              style={{ ...img, objectFit: 'contain' as const }}
            />
          ) : upFile.preview ? (
            <img src={upFile.preview} style={img} alt={upFile.file.name} />
          ) : (
            <img
              src="/assets/images/icons/pdf-icon.png"
              alt="Document file"
              style={{ ...img, objectFit: 'contain' as const }}
            />
          )}
        </div>
      </div>
      <div className="file-info flex flex-col flex-grow">
        <Typography variant="subtitle1" className="">
          {upFile.file.name}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {upFile.file.type || 'Unknown type'} • {formatFileSize(upFile.file.size)}
        </Typography>
        {upFile.error && (
          <Typography variant="caption" color="error">
            Error: {upFile.error}
          </Typography>
        )}
        {upFile.status === 'uploading' && (
          <Typography variant="caption" color="textSecondary">
            Uploading: {upFile.progress}%
          </Typography>
        )}
        {upFile.status === 'completed' && (
          <Typography variant="caption" color="success.main">
            Uploaded
          </Typography>
        )}
      </div>
      <Button
        size="small"
        color="error"
        onClick={() => removeFile(upFile.id)}
        disabled={isApiSubmitting || upFile.status === 'uploading'}
      >
        Remove
      </Button>
    </div>
  ));

  const handleEditorChange = (content: string) => {
    setValue('description', content, { shouldValidate: true });
  };

  const onSubmit = async (data: WorkflowFormData) => {
    const success = await submitWorkflowToApi({
      title: data.title,
      description: data.description,
      amount: parseFloat(data.amount),
      workflowTypeId: data.workflowTypeId,
      authorizingUser: data.authorizingUser,
    });

    if (success) {
      resetForm();
      resetWorkflowCreationHook();
      // Note: Editor content is now cleared automatically via the value prop when form resets
    }
  };

  return (
    <Root
      header={<NewWorkflowHeader />}
      content={
        <motion.div
          className="md:px-16 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, transition: { delay: 0 } }}
        >
          <Card className="p-4 sm:p-6 max-w-5-xl mb-20">
            {isLoadingEmployees || isLoadingWorkflowTypes ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                <Typography>Loading form data...</Typography>
              </Box>
            ) : employeesError || workflowTypesError ? (
              <Box sx={{ padding: 2 }}>
                <Typography color="error">
                  Error loading form data: {employeesError || workflowTypesError}
                </Typography>
              </Box>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                {apiSubmissionError && (
                  <Alert severity="error" className="mb-4">
                    <AlertTitle>Workflow Creation Failed</AlertTitle>
                    {apiSubmissionError}
                  </Alert>
                )}
                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Title"
                      id="title"
                      variant="outlined"
                      fullWidth
                      error={!!errors.title}
                      helperText={errors.title?.message}
                      disabled={isApiSubmitting}
                    />
                  )}
                />

                <div className="flex gap-4">
                  <FormControl className="mt-2 mb-4 flex-1" error={!!errors.workflowTypeId}>
                    <InputLabel htmlFor="workflow-type" required>
                      Workflow Type
                    </InputLabel>
                    <Controller
                      name="workflowTypeId"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          id="workflow-type"
                          label="Workflow Type"
                          disabled={isLoadingWorkflowTypes || isApiSubmitting}
                        >
                          {isLoadingWorkflowTypes && (
                            <MenuItem disabled>
                              <CircularProgress size={20} /> Loading Types...
                            </MenuItem>
                          )}
                          {workflowTypesError && (
                            <MenuItem disabled>
                              <Typography color="error">
                                Error loading types: {workflowTypesError}
                              </Typography>
                            </MenuItem>
                          )}
                          {!isLoadingWorkflowTypes &&
                            !workflowTypesError &&
                            workflowTypes.length === 0 && (
                              <MenuItem disabled>No workflow types found.</MenuItem>
                            )}
                          {workflowTypes.map((type) => (
                            <MenuItem key={type.id} value={type.id}>
                              {type.workflowType}
                            </MenuItem>
                          ))}
                        </Select>
                      )}
                    />
                    {errors.workflowTypeId && (
                      <FormHelperText>{errors.workflowTypeId.message}</FormHelperText>
                    )}
                  </FormControl>

                  <Controller
                    name="amount"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        className="mt-2 mb-4 flex-1"
                        required
                        label="Amount"
                        id="amount"
                        variant="outlined"
                        type="number"
                        error={!!errors.amount}
                        helperText={errors.amount?.message}
                        disabled={isApiSubmitting}
                        InputProps={{
                          startAdornment: <Typography className="mr-2">₦</Typography>,
                        }}
                      />
                    )}
                  />
                </div>

                <div className="mt-2 mb-4">
                  <InputLabel required error={!!errors.description} sx={{ mb: 1 }}>
                    Description
                  </InputLabel>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <RTEEditor
                        ref={editorRef}
                        onChange={handleEditorChange}
                        value={field.value || ''}
                      />
                    )}
                  />
                  {errors.description && (
                    <FormHelperText error>{errors.description.message}</FormHelperText>
                  )}
                </div>

                <FormControl
                  className="mt-2 mb-4"
                  sx={{ width: '100%' }}
                  error={!!errors.authorizingUser}
                >
                  <InputLabel htmlFor="authorizing-user" required>
                    Authorizing User
                  </InputLabel>
                  <Controller
                    name="authorizingUser"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        id="authorizing-user"
                        label="Authorizing User"
                        disabled={isLoadingEmployees || isApiSubmitting}
                      >
                        {isLoadingEmployees && (
                          <MenuItem disabled>
                            <CircularProgress size={20} /> Loading Employees...
                          </MenuItem>
                        )}
                        {employeesError && (
                          <MenuItem disabled>
                            <Typography color="error">
                              Error loading employees: {employeesError}
                            </Typography>
                          </MenuItem>
                        )}
                        {!isLoadingEmployees &&
                          !employeesError &&
                          groupedEmployeesByDepartment.length === 0 && (
                            <MenuItem disabled>No employees found.</MenuItem>
                          )}
                        {groupedEmployeesByDepartment.map((group) => [
                          <ListSubheader key={group.departmentId} sx={{ fontWeight: 'bold' }}>
                            {group.departmentName}
                          </ListSubheader>,
                          ...group.employees
                            .filter((employee) => employee.value !== employeeDetails?.userId) // Exclude current user
                            .map((employee) => (
                            <MenuItem key={employee.value} value={employee.value}>
                              {employee.label}
                            </MenuItem>
                          )),
                        ])}
                      </Select>
                    )}
                  />
                  {errors.authorizingUser && (
                    <FormHelperText>{errors.authorizingUser.message}</FormHelperText>
                  )}
                </FormControl>

                <div className="mt-2 mb-4">
                  <Typography variant="subtitle1" gutterBottom>
                    Attachments
                  </Typography>
                  <div {...getRootProps({ style: dropzoneStyle })}>
                    <input {...getInputProps()} />
                    <p>Drag 'n' drop some files here, or click to select files</p>
                    <small>Max 10 files, 10MB each. Allowed: documents, images.</small>
                  </div>
                  {attachmentValidationErrors && attachmentValidationErrors.length > 0 && (
                    <Alert severity="warning" className="mt-2">
                      <AlertTitle>File Issues</AlertTitle>
                      <ul>
                        {attachmentValidationErrors.map((err, index) => (
                          <li key={index}>
                            {err.fileName}: {err.message}
                          </li>
                        ))}
                      </ul>
                    </Alert>
                  )}
                  <aside style={{ ...thumbsContainer, marginTop: '16px' }}>{displayedThumbs}</aside>
                </div>

                <div className="flex items-center justify-end space-x-2 mt-10">
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={
                      isRHFSubmitting ||
                      isApiSubmitting ||
                      isLoadingEmployees ||
                      isLoadingWorkflowTypes
                    }
                  >
                    {isApiSubmitting ? (
                      <CircularProgress size={15} color="inherit" />
                    ) : (
                      'Create Workflow'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </Card>
        </motion.div>
      }
    />
  );
}

export default NewWorkflow;
