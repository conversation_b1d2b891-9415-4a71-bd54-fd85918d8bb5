import { IAttachment } from './memo.interface';
import { QueryStatusEnums } from '../enums';
import { IPagination } from '@igov/common';

export interface IQuery {
  id?: string;
  reference: string;
  title: string;
  message: string;
  attachments?: IAttachment[];
  employeeId: string;
  draftedBy: string;
  issuedBy: string;
  status: QueryStatusEnums;
  dueDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}
export interface IQueryFilters extends IPagination {
  skip?: number;
  limit?: number;
  search?: string;
  status?: QueryStatusEnums[];
  employeeId?: string;
  draftedById?: string;
  issuedById?: string;
  fromDate?: Date | string;
  toDate?: Date | string;
}
