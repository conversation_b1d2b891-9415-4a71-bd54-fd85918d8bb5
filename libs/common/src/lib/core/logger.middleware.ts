import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggerMiddleware.name);

  use(req: Request, res: Response, next: NextFunction): void {
    this.logEndpoint(req);
    next();
  }

  private logEndpoint(req: Request): void {
    const { baseUrl, method, body } = req;
    this.logger.log(`HTTP Request: ${method} '${baseUrl}' with payload: ${JSON.stringify(body)}`);
  }
}
