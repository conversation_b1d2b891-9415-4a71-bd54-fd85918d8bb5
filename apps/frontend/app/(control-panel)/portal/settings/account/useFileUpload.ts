import { useState, useCallback, useRef, useEffect } from 'react';
import { UploadedFile, UploadFolderType, UPLOAD_CONFIG } from '@/app/types/upload.types';
import { getS3KeyFromUrl, uploadToS3 } from '@/app/lib/s3Client';

interface UseFileUploadOptions {
  /** The folder to upload files to */
  folder: UploadFolderType;
  /** Existing file URL to replace (for updates) */
  existingFileUrl?: string | null;
  /** Callback when upload starts */
  onStart?: () => void;
  /** Callback when upload completes successfully */
  onSuccess?: (file: UploadedFile) => void;
  /** Callback when upload fails */
  onError?: (error: Error) => void;
}

interface UseFileUploadResult {
  /** Function to trigger file selection */
  uploadFile: () => void;
  /** Whether an upload is in progress */
  isUploading: boolean;
  /** Error message if upload failed */
  error: string | null;
  /** Resets the upload state */
  reset: () => void;
}

/**
 * Hook for handling file uploads to S3
 * Currently specific to the account settings avatar upload
 */
export function useFileUpload({
  folder,
  existingFileUrl,
  onStart,
  onSuccess,
  onError,
}: UseFileUploadOptions): UseFileUploadResult {
  // Use refs to avoid recreating callbacks when they're not needed
  const callbacksRef = useRef({ onStart, onSuccess, onError });

  // Update the ref when callbacks change
  useEffect(() => {
    callbacksRef.current = { onStart, onSuccess, onError };
  }, [onStart, onSuccess, onError]);

  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Only update existingFileKey when the URL actually changes
  const fileKeyRef = useRef<string | null>(null);
  useEffect(() => {
    fileKeyRef.current = getS3KeyFromUrl(existingFileUrl);
  }, [existingFileUrl]);

  const handleFileUpload = useCallback(
    async (file: File) => {
      if (!file) return null;

      setIsUploading(true);
      setError(null);
      callbacksRef.current.onStart?.();

      try {
        const result = await uploadToS3(file, folder, fileKeyRef.current || undefined);
        callbacksRef.current.onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Upload failed');
        setError(error.message);
        callbacksRef.current.onError?.(error);
        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    [folder],
  );

  const triggerFileInput = useCallback(() => {
    if (isUploading) return;

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = UPLOAD_CONFIG[folder]?.allowedMimeTypes.join(',') || '';

    // Clean up the input element after use
    const cleanup = () => {
      input.remove();
      input.onchange = null;
      input.oncancel = null;
    };

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileUpload(file)
          .catch(() => {
            // Error is already handled in handleFileUpload
          })
          .finally(cleanup);
      } else {
        cleanup();
      }
    };

    // Handle case where user cancels the file dialog
    input.oncancel = cleanup;

    // Add to document and trigger click
    document.body.appendChild(input);
    input.click();
  }, [folder, handleFileUpload, isUploading]);

  const reset = useCallback(() => {
    setIsUploading(false);
    setError(null);
  }, []);

  return {
    uploadFile: triggerFileInput,
    isUploading,
    error,
    reset,
  };
}
