{"name": "consumer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/consumer/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "consumer:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "consumer:build:development"}, "production": {"buildTarget": "consumer:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}