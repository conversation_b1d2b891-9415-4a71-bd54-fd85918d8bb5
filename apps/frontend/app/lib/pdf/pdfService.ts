import { pdf } from '@react-pdf/renderer';
import { checkFileExists, downloadFromS3, uploadBlobToS3 } from '@/app/lib/s3Client';
import { UploadedFile } from '@/app/types/upload.types';

export interface PDFGenerationOptions {
  /**
   * The S3 key where the PDF should be stored
   * Example: 'workflow/123.pdf', 'memo/456.pdf'
   */
  s3Key: string;
  
  /**
   * React PDF Document component to render
   */
  documentComponent: React.ReactElement;
  
  /**
   * Additional metadata to store with the PDF
   */
  metadata?: Record<string, string>;
  
  /**
   * Whether to force regeneration even if PDF exists
   */
  forceRegenerate?: boolean;
}

export interface PDFResult {
  /**
   * The S3 URL of the PDF
   */
  url: string;
  
  /**
   * The S3 key of the PDF
   */
  key: string;
  
  /**
   * Whether the PDF was newly generated or retrieved from cache
   */
  wasGenerated: boolean;
  
  /**
   * The PDF as a Blob (for immediate viewing)
   */
  blob: Blob;
}

/**
 * Generic PDF service that handles generation, caching, and retrieval
 * Works with any module (workflows, memos, circulars, etc.)
 */
export class PDFService {
  /**
   * Generates or retrieves a PDF from S3
   * 
   * @param options - PDF generation options
   * @returns Promise<PDFResult> - The PDF result with URL and blob
   */
  async getOrGeneratePDF(options: PDFGenerationOptions): Promise<PDFResult> {
    const { s3Key, documentComponent, metadata, forceRegenerate = false } = options;

    try {
      // Check if PDF already exists in S3 (unless forcing regeneration)
      if (!forceRegenerate && await checkFileExists(s3Key)) {
        console.log(`PDF found in S3: ${s3Key}`);
        
        // Download existing PDF
        const blob = await downloadFromS3(s3Key);
        const url = `https://${process.env.NEXT_PUBLIC_AWS_S3_BUCKET_NAME}.s3.${process.env.NEXT_PUBLIC_AWS_REGION}.amazonaws.com/${s3Key}`;
        
        return {
          url,
          key: s3Key,
          wasGenerated: false,
          blob,
        };
      }

      // Generate PDF
      const blob = await this.generatePDFBlob(documentComponent);
      
      // Upload to S3
      const uploadResult = await uploadBlobToS3(
        blob,
        s3Key,
        'application/pdf',
        {
          'document-type': s3Key.split('/')[0], // workflow, memo, etc.
          'generated-at': new Date().toISOString(),
          ...metadata,
        }
      );

      return {
        url: uploadResult.url,
        key: uploadResult.key,
        wasGenerated: true,
        blob,
      };
    } catch (error: any) {
      console.error('PDF service error:', error);
      throw new Error(`Failed to generate PDF: ${error.message}`);
    }
  }

  /**
   * Generates a PDF blob from a React PDF document component
   * 
   * @param documentComponent - React PDF Document component
   * @returns Promise<Blob> - The generated PDF as a blob
   */
  private async generatePDFBlob(documentComponent: React.ReactElement): Promise<Blob> {
    try {
      // Generate PDF using @react-pdf/renderer
      const pdfBlob = await pdf(documentComponent).toBlob();
      return pdfBlob;
    } catch (error: any) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF blob: ${error.message}`);
    }
  }

  /**
   * Opens a PDF in a new browser tab using S3 URL (preferred) or blob URL (fallback)
   * 
   * @param result - The PDF result containing URL and blob
   * @param filename - Optional filename for the PDF
   */
  openPDFInNewTab(result: PDFResult, filename?: string): void {
    try {
      // Prefer S3 URL over blob URL for better performance and caching
      let urlToOpen: string;
      let shouldCleanup = false;

      if (result.url && result.url.startsWith('https://')) {
        // Use S3 URL directly
        urlToOpen = result.url;
      } else {
        // Fallback to blob URL
        urlToOpen = URL.createObjectURL(result.blob);
        shouldCleanup = true;
      }

      const newTab = window.open(urlToOpen, '_blank');
      
      if (!newTab) {
        if (shouldCleanup) {
          URL.revokeObjectURL(urlToOpen);
        }
        throw new Error('Failed to open new tab. Please check your popup blocker.');
      }

      // Clean up blob URL after a delay (only if we created one)
      if (shouldCleanup) {
        setTimeout(() => {
          URL.revokeObjectURL(urlToOpen);
        }, 1000);
      }
    } catch (error: any) {
      console.error('Error opening PDF:', error);
      throw new Error(`Failed to open PDF: ${error.message}`);
    }
  }

  /**
   * Prints a PDF by opening it in a new window that auto-prints and closes
   * Uses blob URLs exclusively to avoid cross-origin issues
   * 
   * @param result - The PDF result containing URL and blob
   * @param filename - Optional filename for the PDF
   */
  printPDF(result: PDFResult, filename?: string): void {
    try {
      // Always use blob URL to avoid cross-origin issues
      const pdfUrl = URL.createObjectURL(result.blob);

      // Open PDF in a new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
      
      if (!printWindow) {
        URL.revokeObjectURL(pdfUrl);
        throw new Error('Failed to open print window. Please check your popup blocker and try again.');
      }

      // Write a simple HTML page that embeds the PDF
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Print ${filename || 'Document'}</title>
          <style>
            body { 
              margin: 0; 
              padding: 0; 
              background: #525659;
            }
            embed { 
              width: 100%; 
              height: 100vh; 
              border: none;
            }
          </style>
        </head>
        <body>
          <embed src="${pdfUrl}" type="application/pdf" />
          <script>
            window.onload = function() {
              setTimeout(function() {
                try {
                  window.print();
                  // Auto-close after print (optional)
                  setTimeout(function() {
                    try {
                      window.close();
                    } catch(e) {}
                  }, 1000);
                } catch(e) {
                  console.error('Print error:', e);
                }
              }, 1500);
            };
          </script>
        </body>
        </html>
      `);
      
      printWindow.document.close();

      // Clean up blob URL after delay
      setTimeout(() => {
        URL.revokeObjectURL(pdfUrl);
      }, 15000);

    } catch (error: any) {
      console.error('Error printing PDF:', error);
      throw new Error(`Failed to print PDF: ${error.message}`);
    }
  }

  /**
   * Downloads a PDF file
   * 
   * @param blob - The PDF blob to download
   * @param filename - The filename for the download
   */
  downloadPDF(blob: Blob, filename: string): void {
    try {
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the object URL
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
    } catch (error: any) {
      console.error('Error downloading PDF:', error);
      throw new Error(`Failed to download PDF: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const pdfService = new PDFService();