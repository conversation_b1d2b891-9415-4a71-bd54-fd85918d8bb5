import {
  AnnouncementFilterDto,
  AnnouncementResponseDto,
  PaginatedAnnouncementResponseDto,
} from '../dtos';
import { Controller, Get, Param, ParseUUIDPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { AnnouncementService } from '../services/announcement.service';
import { AnnouncementEnums } from '../enums';

@ApiTags('Announcement - Self-Service')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('self-service-announcement')
export class AnnouncementSelfServiceController {
  constructor(private readonly announcementService: AnnouncementService) {}

  @Get(':id')
  @ApiOperation({ summary: 'Get one announcement record' })
  @ApiResponse({ status: 200, type: AnnouncementResponseDto })
  async getRecord(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<AnnouncementResponseDto> {
    return this.announcementService.getOne({ id, employeeId: jwtPayload.id });
  }

  @Get()
  @ApiOperation({ summary: 'Get all employee announcement records' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: AnnouncementResponseDto })
  async getAllAnnouncements(
    @Query() query: AnnouncementFilterDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<PaginatedAnnouncementResponseDto> {
    return this.announcementService.getAll({
      ...query,
      employeeId: jwtPayload.id,
      showReadStatus: true,
      status: [AnnouncementEnums.PUBLISHED],
    });
  }
}
