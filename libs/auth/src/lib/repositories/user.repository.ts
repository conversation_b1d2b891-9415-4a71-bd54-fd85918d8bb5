import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { User } from '../entities';
import { IUser, IUserFilters } from '../interfaces';
import { In } from 'typeorm';

@Injectable()
export class UserRepository extends BaseRepository<User> {
  private readonly logger = new Logger(UserRepository.name);
  override entityClassName = User;

  async getUsers(filters: IUserFilters): Promise<{ data: User[]; total: number }> {
    const { search, role, email, userId, skip = 1, limit = 10 } = filters;
    const queryBuilder = this.repository.createQueryBuilder('users');

    if (search) {
      queryBuilder.andWhere(
        'LOWER(users.firstName) LIKE LOWER(:search) OR LOWER(users.lastName) LIKE LOWER(:search) OR LOWER(users.email) LIKE LOWER(:search)',
        { search: `%${search}%` },
      );
    }

    if (role) {
      queryBuilder.andWhere('users.role = :role', { role: role });
    }

    if (email) {
      queryBuilder.andWhere('users.email = :email', { email });
    }

    if (userId) {
      queryBuilder.andWhere('users.id = :userId', { userId });
    }

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total users found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);

    if (limit) queryBuilder.take(limit);

    const users = await queryBuilder.getMany();

    return { data: users, total: totalCount };
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({ where: { email } });
  }

  findById(id: string): Promise<User | null> {
    return this.repository.findOne({ where: { id } });
  }

  findByIdList(ids: string[]): Promise<User[]> {
    return this.repository.find({
      where: {
        id: In(ids),
      },
    });
  }

  saveUser(user: Partial<IUser>): Promise<User> {
    return this.repository.save(user);
  }

  updateUser(user: User): Promise<User> {
    return this.repository.save(user);
  }
}
