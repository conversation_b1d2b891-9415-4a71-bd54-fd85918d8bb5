output "ec2_public_ip" {
  description = "The public IP address of the EC2 instance"
  value       = module.ec2.public_ip
}

output "s3_db_backup_bucket_arn" {
  description = "The ard of the DB backup S3 bucket"
  value       = module.s3.db_backup_bucket_arn
}

output "s3_file_storage_bucket_arn" {
  description = "The name of the file storage S3 bucket"
  value       = module.s3.file_storage_bucket_arn
}

# output "frontend_ecr_repository_url" {
#   description = "The URL of the frontend ECR repository"
#   value       = module.ecr.frontend_repository_url
# }

# output "api_ecr_repository_url" {
#   description = "The URL of the API ECR repository"
#   value       = module.ecr.api_repository_url
# }

output "consolidated_ecr_repository_url" {
  description = "The URL of the consolidated ECR repository"
  value       = module.ecr.consolidated_repository_url
}

output "ec2_ssh_private_key" {
  description = "The private key for SSH access to the EC2 instance"
  value       = module.ec2.private_key_pem
  sensitive   = true
}

