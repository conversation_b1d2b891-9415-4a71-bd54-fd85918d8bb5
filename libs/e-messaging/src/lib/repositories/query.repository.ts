import { BaseRepository } from '@igov/common';
import { QueryEntity } from '../entity';
import { Injectable, Logger } from '@nestjs/common';
import { IQuery } from '../interfaces';

@Injectable()
export class QueryRepository extends BaseRepository<QueryEntity> {
  private readonly logger = new Logger(QueryRepository.name);
  override entityClassName = QueryEntity;

  async findOne(params: {
    id: string;
    employeeId?: string;
    relations?: string[];
  }): Promise<QueryEntity | null> {
    const { id, employeeId } = params;

    const queryBuilder = this.repository.createQueryBuilder('queries');
    queryBuilder
      .leftJoinAndSelect('queries.employee', 'employee')
      .addSelect(['employee.userId', 'employee.firstName', 'employee.lastName']);
    queryBuilder
      .leftJoinAndSelect('queries.draftedByEmployee', 'draftedByEmployee')
      .addSelect([
        'draftedByEmployee.userId',
        'draftedByEmployee.firstName',
        'draftedByEmployee.lastName',
      ]);
    queryBuilder
      .leftJoinAndSelect('queries.issuedByEmployee', 'issuedByEmployee')
      .addSelect([
        'issuedByEmployee.userId',
        'issuedByEmployee.firstName',
        'issuedByEmployee.lastName',
      ]);
    queryBuilder
      .leftJoinAndSelect('queries.history', 'history')
      .leftJoinAndSelect('history.performedByEmployee', 'historyEmployee')
      .addSelect([
        'historyEmployee.userId',
        'historyEmployee.firstName',
        'historyEmployee.lastName',
      ]);

    queryBuilder
      .leftJoinAndSelect('queries.announcements', 'announcements')
      .leftJoinAndSelect('announcements.employee', 'announcementEmployee')
      .addSelect([
        'announcementEmployee.userId',
        'announcementEmployee.firstName',
        'announcementEmployee.lastName',
      ]);
    queryBuilder.where('queries.id = :id', { id });
    if (employeeId) {
      queryBuilder.andWhere(
        `(
        queries.employeeId = :employeeId OR
        queries.draftedBy = :employeeId OR
        queries.issuedBy = :employeeId
      )`,
        { employeeId },
      );
    }

    return queryBuilder.getOne();
  }

  async save(query: Partial<IQuery>): Promise<QueryEntity> {
    return this.repository.save(query);
  }
  async findOneById(id: string): Promise<QueryEntity | null> {
    return await this.repository.findOne({ where: { id } });
  }
  async findAll(query: {
    skip?: number;
    limit?: number;
    search?: string;
    status?: string;
    fromDate?: string;
    toDate?: string;
    employeeId?: string;
    draftedById?: string;
    issuedById?: string;
    useOrConditionForEmployees?: boolean; // <- NEW FLAG
  }): Promise<{ data: QueryEntity[]; total: number }> {
    const {
      skip,
      limit,
      search,
      status,
      employeeId,
      draftedById,
      issuedById,
      fromDate,
      toDate,
      useOrConditionForEmployees = false, // default to false
    } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('queries')
      .leftJoin('queries.employee', 'employee')
      .addSelect(['employee.userId', 'employee.firstName', 'employee.lastName'])
      .leftJoin('queries.draftedByEmployee', 'draftedByEmployee')
      .addSelect([
        'draftedByEmployee.userId',
        'draftedByEmployee.firstName',
        'draftedByEmployee.lastName',
      ])
      .leftJoin('queries.issuedByEmployee', 'issuedByEmployee')
      .addSelect([
        'issuedByEmployee.userId',
        'issuedByEmployee.firstName',
        'issuedByEmployee.lastName',
      ]);

    if (search) {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere('(queries.title ILIKE :search OR queries.message ILIKE :search)', {
        search: searchTerm,
      });
    }

    if (status?.length) {
      queryBuilder.andWhere('queries.status = :status', { status });
    }

    if (useOrConditionForEmployees) {
      // Group OR conditions
      const orConditions: string[] = [];
      const orParams: Record<string, string> = {};

      if (employeeId) {
        orConditions.push('queries.employeeId = :employeeId');
        orParams['employeeId'] = employeeId;
      }
      if (draftedById) {
        orConditions.push('queries.draftedBy = :draftedById');
        orParams['draftedById'] = draftedById;
      }
      if (issuedById) {
        orConditions.push('queries.issuedBy = :issuedById');
        orParams['issuedById'] = issuedById;
      }

      if (orConditions.length > 0) {
        queryBuilder.andWhere(`(${orConditions.join(' OR ')})`, orParams);
      }
    } else {
      // Use AND conditions individually
      if (employeeId) {
        queryBuilder.andWhere('queries.employeeId = :employeeId', { employeeId });
      }
      if (draftedById) {
        queryBuilder.andWhere('queries.draftedBy = :draftedById', { draftedById });
      }
      if (issuedById) {
        queryBuilder.andWhere('queries.issuedBy = :issuedById', { issuedById });
      }
    }

    if (fromDate) {
      queryBuilder.andWhere('queries.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      queryBuilder.andWhere('queries.createdAt <= :toDate', { toDate });
    }

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total queries found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const queries = await queryBuilder.getMany();

    return { data: queries, total: totalCount };
  }

  async findOneEmployeeById(id: string, employeeId: string): Promise<QueryEntity | null> {
    return await this.repository.findOne({
      where: [
        { id, issuedBy: employeeId },
        { id, draftedBy: employeeId },
        { id, employeeId },
      ],
    });
  }
}
