import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';
import { Employee } from '@igov/employee';
import { QueryEntity } from './query.entity';
import { QueryStatusEnums } from '../enums';

@Entity('query_histories')
export class QueryHistoryEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('uuid')
  queryId!: string;

  @ManyToOne(() => QueryEntity, (query) => query.id, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'query_id' })
  query!: QueryEntity;

  @Column('uuid')
  performedBy!: string;

  @ManyToOne(() => Employee, (employee) => employee.userId, { eager: false })
  @JoinColumn({ name: 'performed_by' })
  performedByEmployee!: Employee;

  @Column('enum', { enum: QueryStatusEnums })
  action!: QueryStatusEnums;

  @Column('jsonb', { nullable: true })
  snapshot?: object;

  @CreateDateColumn({ name: 'performed_at' })
  performedAt!: Date;
}
