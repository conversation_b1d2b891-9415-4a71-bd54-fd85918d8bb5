import { Module } from '@nestjs/common';
import { OrganizationStructureController } from './controller/org-structure.controller';
import { OrganizationStructureService } from './services/org-structure.service';
import {
  DepartmentRepository,
  MdaRepository,
  OrganizationProfileRepository,
  PositionRepository,
  UnitRepository,
} from './repositories';
import { DatabaseModule } from '@igov/common';
import { OrganizationProfile, Department, MDA, Unit, Position } from './entities';

@Module({
  imports: [DatabaseModule.forFeature([OrganizationProfile, Department, MDA, Unit, Position])],
  controllers: [OrganizationStructureController],
  providers: [
    OrganizationStructureService,
    DepartmentRepository,
    MdaRepository,
    UnitRepository,
    PositionRepository,
    OrganizationProfileRepository,
  ],
  exports: [
    OrganizationStructureService,
    DepartmentRepository,
    MdaRepository,
    UnitRepository,
    PositionRepository,
    OrganizationProfileRepository,
  ],
})
export class OrgStructureModule {}
