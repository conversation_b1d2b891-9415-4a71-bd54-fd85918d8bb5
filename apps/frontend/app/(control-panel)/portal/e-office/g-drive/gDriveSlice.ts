import { WithSlice, createSlice, PayloadAction } from '@reduxjs/toolkit';
import rootReducer from '@/store/rootReducer'; // Assuming this is the correct path to your rootReducer

// Define the shape of the state for this slice
interface GDriveAppState {
	selectedItemId: string | null;
	leftSidebarOpen: boolean;
	viewMode: 'personal' | 'shared-incoming' | 'shared-outgoing';
	displayMode: 'list' | 'grid';
	searchQuery: string;
}

const initialState: GDriveAppState = {
    selectedItemId: null,
    leftSidebarOpen: true,
    viewMode: 'personal',
    displayMode: 'list',
    searchQuery: '',
};

/**
 * The GDrive App slice.
 */
export const gDriveSlice = createSlice({
	name: 'gDriveApp', // Renamed for GDrive context
	initialState,
	reducers: {
		setSelectedItemId: (state, action: PayloadAction<string | null>) => {
			state.selectedItemId = action.payload;
		},
		resetSelectedItemId: (state) => {
			state.selectedItemId = initialState.selectedItemId;
		},
		setLeftSidebarOpen: (state, action: PayloadAction<boolean>) => {
			state.leftSidebarOpen = action.payload;
		},
		toggleLeftSidebar: (state) => {
			state.leftSidebarOpen = !state.leftSidebarOpen;
		},
		setViewMode: (state, action: PayloadAction<'personal' | 'shared-incoming' | 'shared-outgoing'>) => {
			state.viewMode = action.payload;
		},
		setDisplayMode: (state, action: PayloadAction<'list' | 'grid'>) => {
			state.displayMode = action.payload;
		},
		toggleDisplayMode: (state) => {
			state.displayMode = state.displayMode === 'list' ? 'grid' : 'list';
		},
		setSearchQuery: (state, action: PayloadAction<string>) => {
			state.searchQuery = action.payload;
		},
		resetState: (state) => {
			Object.assign(state, initialState);
		}
	},
	selectors: {
		selectSelectedItemId: (state): string | null => state.selectedItemId,
		selectLeftSidebarOpen: (state): boolean => state.leftSidebarOpen,
		selectViewMode: (state): 'personal' | 'shared-incoming' | 'shared-outgoing' => state.viewMode,
		selectDisplayMode: (state): 'list' | 'grid' => state.displayMode,
		selectSearchQuery: (state): string => state.searchQuery
	}
});

/**
 * Lazy load
 * This section injects the slice into the root reducer.
 * Ensure your rootReducer setup supports this injection pattern.
 */
rootReducer.inject(gDriveSlice); // Injects the gDriveSlice
const injectedSlice = gDriveSlice.injectInto(rootReducer);

// Augment the LazyLoadedSlices interface in your rootReducer module
declare module '@/store/rootReducer' {
	// eslint-disable-next-line @typescript-eslint/no-empty-interface
	export interface LazyLoadedSlices extends WithSlice<typeof gDriveSlice> {}
}

// Export actions from the original slice object
export const { 
	setSelectedItemId, 
	resetSelectedItemId,
	setLeftSidebarOpen,
	toggleLeftSidebar,
	setViewMode,
	setDisplayMode,
	toggleDisplayMode,
	setSearchQuery,
	resetState
} = gDriveSlice.actions;

// Export selectors from the injected slice, which are correctly typed
export const { 
	selectSelectedItemId,
	selectLeftSidebarOpen,
	selectViewMode,
	selectDisplayMode,
	selectSearchQuery
} = injectedSlice.selectors;

// Optional: Export the type of the slice for convenience if needed elsewhere
export type GDriveSliceType = typeof gDriveSlice;

// Export the reducer as the default export
export default gDriveSlice.reducer;
