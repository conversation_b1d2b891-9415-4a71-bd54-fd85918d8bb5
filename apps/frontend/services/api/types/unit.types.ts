/**
 * Data Transfer Objects (DTOs) for Unit entities.
 * These interfaces define the expected shape of data for unit-related API interactions,
 * mirroring the backend DTOs for consistency.
 */

/**
 * Defines the structure for creating a new unit.
 * Based on the backend CreateUnitDto.
 */
export interface CreateUnitDto {
  departmentCode: string;
  name: string;
  commRef?: string;
  departmentId: string;
}

/**
 * Defines the structure for updating an existing unit.
 * All fields are optional, allowing partial updates.
 * Based on the backend UpdateUnitDto.
 */
export interface UpdateUnitDto {
  departmentCode?: string;
  name?: string;
  commRef?: string;
  departmentId?: string;
}

/**
 * Defines the structure for a unit response from the API.
 * Includes all fields from CreateUnitDto plus system-generated fields like id, createdAt, and updatedAt.
 * Based on the backend UnitResponseDto.
 */
export interface UnitResponseDto {
  id: string;
  departmentCode: string;
  name: string;
  commRef?: string | null; // Adjusted to match potential null from backend/DB
  departmentId: string;
  createdAt?: string; // Typically ISO date strings from backend
  updatedAt?: string; // Typically ISO date strings from backend
}
