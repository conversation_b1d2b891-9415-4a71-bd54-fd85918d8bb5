import { useState, useCallback } from 'react';
import { uploadToS3, deleteFromS3 } from '@/app/lib/s3Client';
import { gDriveApiService } from '@/services/api/gDriveService';
import { CreateGDriveFileDto } from '@/services/api/types/g-drive.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for individual file wrapper used by the hook.
 */
export interface FileWrapper {
  id: string; // Unique identifier for the file in the list
  file: File;
  preview: string; // Data URL for image previews
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string; // S3 URL after successful upload
  s3Key?: string; // S3 key after successful upload
  error?: string; // Error message if upload fails or validation fails
}

/**
 * Interface for pre-upload validation errors.
 */
export interface UploadValidationError {
  fileName: string;
  message: string;
}

const MAX_FILES = 10;
const MAX_SIZE_BYTES = 10 * 1024 * 1024; // 10MB for documents
const ALLOWED_TYPES = {
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
  'image/svg+xml': ['.svg'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'text/plain': ['.txt'],
  'text/csv': ['.csv'],
  'application/vnd.ms-powerpoint': ['.ppt'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
};

/**
 * Custom hook for handling GDrive file uploads.
 */
export function useGDriveUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<FileWrapper[]>([]);
  const [validationErrors, setValidationErrors] = useState<UploadValidationError[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  /**
   * Handles file selection from dropzone
   */
  const handleFileChange = useCallback(
    (acceptedFiles: File[], fileRejections: any[] = []) => {
      setValidationErrors([]); // Clear previous validation errors
      const newValidationErrors: UploadValidationError[] = [];

      // Handle rejected files from Dropzone first
      fileRejections.forEach((rejection) => {
        rejection.errors.forEach((error: any) => {
          newValidationErrors.push({
            fileName: rejection.file.name,
            message: error.message,
          });
        });
      });

      // Check if adding these files would exceed MAX_FILES
      const currentTotalFiles = uploadedFiles.length + acceptedFiles.length;
      if (currentTotalFiles > MAX_FILES) {
        newValidationErrors.push({
          fileName: 'General',
          message: `Cannot upload more than ${MAX_FILES} files. Please remove some files first.`,
        });
        setValidationErrors(newValidationErrors);
        return;
      }

      const newFilesToProcess: FileWrapper[] = [];

      acceptedFiles.forEach((file) => {
        // Validate file size
        if (file.size > MAX_SIZE_BYTES) {
          newValidationErrors.push({
            fileName: file.name,
            message: `File is too large (max ${MAX_SIZE_BYTES / 1024 / 1024}MB).`,
          });
          return;
        }

        // Validate file type
        const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        const isValidType = Object.values(ALLOWED_TYPES).some((extensions) =>
          extensions.includes(fileExtension),
        );

        if (!isValidType) {
          const allowedExtensions = Object.values(ALLOWED_TYPES).flat().join(', ');
          newValidationErrors.push({
            fileName: file.name,
            message: `File type not allowed. Allowed types: ${allowedExtensions}`,
          });
          return;
        }

        // Create preview URL
        const preview = file.type.startsWith('image/') ? URL.createObjectURL(file) : '';

        // Create FileWrapper object
        const fileWrapper: FileWrapper = {
          id: uuidv4(),
          file,
          preview,
          progress: 0,
          status: 'pending',
        };

        newFilesToProcess.push(fileWrapper);
      });

      // Update states
      setValidationErrors(newValidationErrors);
      setUploadedFiles((prev) => [...prev, ...newFilesToProcess]);
    },
    [uploadedFiles.length],
  );

  /**
   * Removes a file from the uploaded files list
   */
  const removeFile = useCallback(
    async (fileId: string) => {
      const fileToRemove = uploadedFiles.find((f) => f.id === fileId);
      if (!fileToRemove) return;

      // If file was uploaded to S3, delete it
      if (fileToRemove.s3Key) {
        try {
          await deleteFromS3(fileToRemove.s3Key);
        } catch (error) {
          console.error('Failed to delete file from S3:', error);
          // Don't throw here - we still want to remove from UI
        }
      }

      // Revoke preview URL to free memory
      if (fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

      // Remove from state
      setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    },
    [uploadedFiles],
  );

  /**
   * Uploads all pending files to GDrive
   */
  const uploadFiles = useCallback(
    async (folderId?: string): Promise<boolean> => {
      const filesToUpload = uploadedFiles.filter((f) => f.status === 'pending');
      if (filesToUpload.length === 0) return true;

      setIsUploading(true);

      try {
        for (const fileWrapper of filesToUpload) {
          // Update status to uploading
          setUploadedFiles((prev) =>
            prev.map((f) =>
              f.id === fileWrapper.id ? { ...f, status: 'uploading' as const, progress: 0 } : f,
            ),
          );

          try {
            // Upload to S3
            const uploadResult = await uploadToS3(fileWrapper.file, 'documents');

            // Update file status to completed
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWrapper.id
                  ? {
                      ...f,
                      status: 'completed' as const,
                      progress: 100,
                      url: uploadResult.url,
                      s3Key: uploadResult.key,
                    }
                  : f,
              ),
            );

            // Create file record in database
            const fileData: CreateGDriveFileDto = {
              fileName: fileWrapper.file.name,
              folderId: folderId || undefined,
              archive: false,
              attachment: {
                name: uploadResult.fileName,
                url: uploadResult.url,
                type: uploadResult.type,
              },
            };

            await gDriveApiService.uploadFile(fileData);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Upload failed';

            // Update file status to error
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.id === fileWrapper.id ? { ...f, status: 'error' as const, error: errorMessage } : f,
              ),
            );

            throw new Error(`Failed to upload ${fileWrapper.file.name}: ${errorMessage}`);
          }
        }

        return true;
      } catch (error) {
        console.error('Upload failed:', error);
        return false;
      } finally {
        setIsUploading(false);
      }
    },
    [uploadedFiles],
  );

  /**
   * Resets the upload state
   */
  const reset = useCallback(() => {
    // Revoke all preview URLs to free memory
    uploadedFiles.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });

    setUploadedFiles([]);
    setValidationErrors([]);
    setIsUploading(false);
  }, [uploadedFiles]);

  const formatFileSize = (sizeInBytes: number) => {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} bytes`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  return {
    uploadedFiles,
    validationErrors,
    isUploading,
    handleFileChange,
    removeFile,
    uploadFiles,
    reset,
    formatFileSize,
  };
}