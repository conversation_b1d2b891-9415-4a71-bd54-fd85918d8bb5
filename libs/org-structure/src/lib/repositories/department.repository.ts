import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { Department } from '../entities';
import { IDepartment } from '../interfaces';
import { In } from 'typeorm';

@Injectable()
export class DepartmentRepository extends BaseRepository<Department> {
  private readonly logger = new Logger(DepartmentRepository.name);
  override entityClassName = Department;

  findOne(id: string): Promise<Department | null> {
    return this.repository.findOne({ where: { id } });
  }

  findAll(): Promise<Department[]> {
    return this.repository.find();
  }

  save(department: IDepartment): Promise<Department> {
    return this.repository.save(department);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  async findByIdsList(ids: string[]): Promise<Department[]> {
    if (!ids?.length) return [];

    return this.repository.find({
      where: { id: In(ids) },
    });
  }
}
