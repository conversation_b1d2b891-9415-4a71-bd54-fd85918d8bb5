import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DataSource, EntityManager } from 'typeorm';
import { EmployeeRepository } from '../repositories';
import { DepartmentRepository, UnitRepository } from '@igov/org-structure';
import { Employee } from '../entity';
import { User, UserRepository, UserRoles } from '@igov/auth';
import { EncryptionService } from '@igov/common';
import {
  CreateEmployeeDto,
  EmployeeResponseDto,
  ESignatureDto,
  ETokenDto,
  PaginatedEmployeeResponseDto,
  QueryEmployeeDto,
  UpdateEmployeeDto,
} from '../dtos';
import * as bcrypt from 'bcrypt';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly employeeRepository: EmployeeRepository,
    private readonly userRepository: UserRepository,
    private readonly departmentRepository: DepartmentRepository,
    private readonly unitRepository: UnitRepository,
  ) {}

  async getAll(query: QueryEmployeeDto): Promise<PaginatedEmployeeResponseDto> {
    this.logger.log('Fetching all employees...');
    try {
      const { data, total } = await this.employeeRepository.findAll(query);
      this.logger.log('Employees fetched successfully');
      const employees = data.map((employee) => plainToInstance(EmployeeResponseDto, employee));

      return { employees, total };
    } catch (error) {
      this.handleError('Get All', error);
      throw error;
    }
  }

  async getOne(id: string): Promise<EmployeeResponseDto> {
    this.logger.log('Fetching employee by ID...');
    try {
      const employee = await this.employeeRepository.findOne(id);
      if (!employee) {
        this.logger.warn(`Employee with ID ${id} not found`);
        throw new NotFoundException('Employee not found');
      }
      this.logger.log('Employee fetched successfully');
      return plainToInstance(EmployeeResponseDto, employee);
    } catch (error) {
      this.handleError('Getting one', error);
      throw error;
    }
  }

  async create(createEmployeeDto: CreateEmployeeDto): Promise<EmployeeResponseDto> {
    await this.ensureEmployeeDoesNotExist(createEmployeeDto.email);

    if (createEmployeeDto.departmentId) {
      await this.ensureDepartmentExists(createEmployeeDto.departmentId);
    }

    await this.ensureUnitExists(createEmployeeDto.unitId);

    const queryRunner = this.dataSource.createQueryRunner();
    try {
      this.logger.log('Saving employee...');

      await queryRunner.connect();

      await queryRunner.startTransaction();

      const user = await this.getOrCreateUser(createEmployeeDto, queryRunner.manager);

      const employee = queryRunner.manager.create(Employee, {
        ...createEmployeeDto,
        userId: user.id,
      });

      const savedEmployee = await queryRunner.manager.save(Employee, employee);

      await queryRunner.commitTransaction();
      this.logger.log('Employee saved successfully');
      return plainToInstance(EmployeeResponseDto, savedEmployee);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError('Creating Employee', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(userId: string, dto: UpdateEmployeeDto): Promise<EmployeeResponseDto> {
    await this.ensureEmployeeExists(userId);
    if (dto.departmentId) await this.ensureDepartmentExists(dto.departmentId);
    if (dto.unitId) await this.ensureUnitExists(dto.unitId);

    const { email, ...updateData } = dto;

    const runner = this.dataSource.createQueryRunner();
    await runner.connect();
    await runner.startTransaction();

    try {
      await runner.manager.update(Employee, userId, updateData);

      const userUpdates: Partial<User> = {};
      if (dto.firstName) userUpdates.firstName = dto.firstName;
      if (dto.lastName) userUpdates.lastName = dto.lastName;
      if (Object.keys(userUpdates).length > 0) {
        await runner.manager.update(User, userId, userUpdates);
      }

      await runner.commitTransaction();
      const updated = await this.employeeRepository.findOne(userId);
      return plainToInstance(EmployeeResponseDto, updated);
    } catch (error) {
      await runner.rollbackTransaction();
      this.handleError('updating employee', error);
    } finally {
      await runner.release();
    }
  }
  private handleError(context: string, error: unknown): never {
    this.logger.error(`Error during ${context}`, error);
    if (
      error instanceof ConflictException ||
      error instanceof NotFoundException ||
      error instanceof UnauthorizedException
    )
      throw error;
    throw new InternalServerErrorException(`Failed while ${context}`);
  }

  private async ensureEmployeeExists(userId: string): Promise<Employee> {
    const existing = await this.employeeRepository.findOne(userId);
    if (!existing) {
      this.logger.warn(`Employee with ID ${userId} not found`);
      throw new NotFoundException('Employee not found');
    }
    return existing;
  }

  private async ensureEmployeeDoesNotExist(email: string): Promise<void> {
    const existing = await this.employeeRepository.findOneByEmail(email);
    if (existing) {
      this.logger.warn(`Employee with email ${email} already exists`);
      throw new ConflictException('Employee already exists');
    }
  }

  private async getOrCreateUser(dto: CreateEmployeeDto, manager: EntityManager): Promise<User> {
    if (dto.userId) {
      const user = await this.userRepository.findById(dto.userId);
      if (!user) {
        this.logger.warn(`User with ID ${dto.userId} not found`);
        throw new NotFoundException('User not found');
      }
      return user;
    }

    const passwordHash = await bcrypt.hash('password', 10);
    const newUser = manager.create(User, {
      email: dto.email,
      passwordHash,
      firstName: dto.firstName,
      lastName: dto.lastName,
      role: UserRoles.MODERATOR,
    });

    return manager.save(User, newUser);
  }

  private async ensureDepartmentExists(departmentId: string): Promise<void> {
    const department = await this.departmentRepository.findOne(departmentId);
    if (!department) {
      this.logger.warn(`Department with ID ${departmentId} not found`);
      throw new NotFoundException('Department not found');
    }
  }

  private async ensureUnitExists(unitId: string): Promise<void> {
    const unit = await this.unitRepository.findOne(unitId);
    if (!unit) {
      this.logger.warn(`Unit with ID ${unitId} not found`);
      throw new NotFoundException('Unit not found');
    }
  }

  async updateESignature(eSignatureDto: ESignatureDto): Promise<EmployeeResponseDto> {
    this.logger.log('Update employee e-signature');
    try {
      const { userId, signatureUrl } = eSignatureDto;
      await this.ensureEmployeeExists(userId);

      await this.validatePassword(userId, eSignatureDto.passwordConfirmation);

      const updatedEmployee = await this.employeeRepository.save({
        userId,
        eSignature: signatureUrl,
      });
      this.logger.log('Employee e-signature updated');
      return plainToInstance(EmployeeResponseDto, updatedEmployee);
    } catch (error) {
      this.handleError('Updating E-Signature', error);
      throw error;
    }
  }

  async setToken(eToken: ETokenDto): Promise<EmployeeResponseDto> {
    this.logger.log('Update employee eToken');
    try {
      const { userId, token } = eToken;
      await this.ensureEmployeeExists(eToken.userId);

      await this.validatePassword(userId, eToken.passwordConfirmation);

      const encryptedToken = EncryptionService.encrypt(token);
      const updatedEmployee = await this.employeeRepository.save({
        userId,
        eToken: encryptedToken,
      });
      this.logger.log('Employee eToken updated');
      return plainToInstance(EmployeeResponseDto, updatedEmployee);
    } catch (error) {
      this.handleError('Setting Token', error);
      throw error;
    }
  }

  async validateEToken(eToken: ETokenDto): Promise<boolean> {
    this.logger.log('eToken validation');
    try {
      const employee = await this.ensureEmployeeExists(eToken.userId);
      const userId = eToken.userId;

      if (!employee.eToken) {
        this.logger.warn(`Token not set for  ${eToken.userId}`);
        throw new ConflictException('etoken not set for this employee');
      }

      await this.validatePassword(userId, eToken.passwordConfirmation);

      return EncryptionService.isMatch(eToken.token, employee.eToken);
    } catch (error) {
      this.handleError('Validating Token', error);
      throw error;
    }
  }

  private async validatePassword(userId: string, password: string): Promise<void> {
    const user = await this.userRepository.findById(userId);

    if (!user) {
      this.logger.warn(`User with ID ${userId} not found`);
      throw new NotFoundException('User not found');
    }

    const isValidPassword = await bcrypt.compare(password, user.passwordHash);

    if (!isValidPassword) {
      this.logger.warn('Invalid password confirmation for e-token update');
      throw new UnauthorizedException('Invalid credentials');
    }
  }
}
