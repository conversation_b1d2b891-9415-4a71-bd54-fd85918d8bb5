import { Authentication1744748380186 } from './migrations/1744748380186-authentication';
import { OrgStructure1744794457331 } from './migrations/1744794457331-org-structure';
import { UserRole1745090422982 } from './migrations/1745090422982-userRole';
import { HrSettings1745101766805 } from './migrations/1745101766805-hr-settings';
import { UpdateUser1745102790015 } from './migrations/1745102790015-update-user';
import { UpdateMdaUnit1745260024985 } from './migrations/1745260024985-update-mda-unit';
import { UpdateDepartment1745262056714 } from './migrations/1745262056714-update-department';
import { Employee1745535741195 } from './migrations/1745535741195-employee';
import { EmployeeTokenSignature1746275723602 } from './migrations/1746275723602-employee-token-signature';
import { ChatMssage1746351795635 } from './migrations/1746351795635-chat-mssage';
import { UpdateUserName1746561329202 } from './migrations/1746561329202-update-user-name';
import { Memo1747129434652 } from './migrations/1747129434652-memo';
import { Trainings1748186177045 } from './migrations/1748186177045-trainings';
import { Circular1747812067238 } from './migrations/1747812067238-circular';
import { UpdateCircular1749193816824 } from './migrations/1749193816824-update-circular';
import { Query1748679422640 } from './migrations/1748679422640-query';
import { Announcement1749196073478 } from './migrations/1749196073478-announcement';
import { UpdateAnnouncementHistory1749276302566 } from './migrations/1749276302566-update-announcement-history';
import { UpdateAnnouncementReadEntity1749276887042 } from './migrations/1749276887042-update-AnnouncementReadEntity';
import { UpdateQueryAnnouncementEntity1749277316386 } from './migrations/1749277316386-update-QueryAnnouncementEntity';
import { GDrive1749472246082 } from './migrations/1749472246082-gDrive';
import { UpdateAnnouncement1749456689423 } from './migrations/1749456689423-update-announcement';
import { AddReadByIdAnnouncementread1749471118488 } from './migrations/1749471118488-add-read-by-id-announcementread';
import { WorkflowType1750060786028 } from './migrations/1750060786028-workflow-type';
import { Workflow1750087744962 } from './migrations/1750087744962-workflow';
import { WorflowCommentActorsUpdate1750161261654 } from './migrations/1750161261654-worflow-comment-actors-update';
import { UpdateWorkflowentityRelationships1750250021158 } from './migrations/1750250021158-update-workflowentity-relationships';
import { UpdateEnumWorkflowComment1750404561344 } from './migrations/1750404561344-update-enum-workflow-comment';
import { UpdateWorkflowActors1750677532991 } from './migrations/1750677532991-update-workflow-actors';
import { WorkgroupEntities1751107948273 } from './migrations/1751107948273-workgroup-entities';
import { UpdateWorkgroupDoc1752676837473 } from './migrations/1752676837473-update-workgroup-doc';
import { Registry1752177476338 } from './migrations/1752177476338-registry';

export const migrations = [
  Authentication1744748380186,
  OrgStructure1744794457331,
  UserRole1745090422982,
  HrSettings1745101766805,
  UpdateUser1745102790015,
  UpdateMdaUnit1745260024985,
  UpdateDepartment1745262056714,
  Employee1745535741195,
  EmployeeTokenSignature1746275723602,
  ChatMssage1746351795635,
  UpdateUserName1746561329202,
  Memo1747129434652,
  Trainings1748186177045,
  Circular1747812067238,
  UpdateCircular1749193816824,
  Query1748679422640,
  Announcement1749196073478,
  UpdateAnnouncementHistory1749276302566,
  UpdateAnnouncementReadEntity1749276887042,
  UpdateQueryAnnouncementEntity1749277316386,
  UpdateAnnouncement1749456689423,
  AddReadByIdAnnouncementread1749471118488,
  GDrive1749472246082,
  WorkflowType1750060786028,
  Workflow1750087744962,
  WorflowCommentActorsUpdate1750161261654,
  UpdateWorkflowentityRelationships1750250021158,
  UpdateEnumWorkflowComment1750404561344,
  UpdateWorkflowActors1750677532991,
  WorkgroupEntities1751107948273,
  UpdateWorkgroupDoc1752676837473,
  Registry1752177476338,
];
