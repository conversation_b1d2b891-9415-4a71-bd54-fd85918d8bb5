'use client';

import React, { useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import { styled } from '@mui/material/styles';
import clsx from 'clsx';

// Simple styling approach - let RTE handle its own scrolling
const Root = styled('div')({
  '& .richtexteditor': {
    minHeight: '280px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontFamily: 'inherit',
  },
  // Basic Material-UI theme integration
  '& .richtexteditor .rte-toolbar': {
    borderBottom: '1px solid #ddd',
    backgroundColor: '#fafafa',
  },
});

export interface RTEEditorProps {
  className?: string;
  onChange: (content: string) => void;
  value?: string;
}

export interface RTEEditorRef {
  setContent: (content: string) => void;
  getContent: () => string;
}

const RTEEditor = forwardRef<RTEEditorRef, RTEEditorProps>((props, ref) => {
  const { onChange, className = '', value } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const rteInstance = useRef<any>(null);
  const isInitialized = useRef(false);
  const onChangeRef = useRef(onChange);
  const pollInterval = useRef<NodeJS.Timeout | null>(null);

  // Update onChange ref without causing re-render
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Setup change polling
  const setupChangePolling = () => {
    if (pollInterval.current) {
      clearInterval(pollInterval.current);
    }
    
    if (rteInstance.current && isInitialized.current) {
      let lastContent = rteInstance.current.getHTMLCode();
      
      pollInterval.current = setInterval(() => {
        if (rteInstance.current) {
          const currentContent = rteInstance.current.getHTMLCode();
          if (currentContent !== lastContent) {
            lastContent = currentContent;
            onChangeRef.current(currentContent);
          }
        }
      }, 1000); // Poll every second
    }
  };


  // Initialize RTE once
  useEffect(() => {
    const initializeEditor = () => {
      if (
        containerRef.current &&
        window.RichTextEditor &&
        !isInitialized.current
      ) {
        try {
          // Simple RTE Configuration - let it use defaults
          const rteConfig = {
            skin: "default",
            toolbar: "default", 
            enableDragDrop: true,
            enableObjectResizing: true,
            showFloatTableToolBar: true,
            showFloatImageToolBbar: true,
            showFloatLinkToolBar: true,
            allowScriptCode: false,
            editorResizeMode: "both", // Allow natural resizing
            showPlusButton: true,
            focusOnLoad: false,
            maxHTMLLength: 0,
            enterKeyTag: "p",
            url_base: "/richtexteditor"
          };

          // Merge config
          Object.assign(window.RTE_DefaultConfig, rteConfig);
          
          // Create editor
          rteInstance.current = new window.RichTextEditor(containerRef.current);
          isInitialized.current = true;

          // Set initial content
          if (value) {
            rteInstance.current.setHTMLCode(value);
          }

          // Setup change polling after initialization
          setTimeout(() => {
            setupChangePolling();
          }, 1000);

          console.log('RichTextEditor initialized successfully');

        } catch (error) {
          console.error('Failed to initialize RichTextEditor:', error);
        }
      }
    };

    // Check if scripts are loaded
    if (window.RichTextEditor && window.RTE_DefaultConfig) {
      initializeEditor();
    } else {
      // Wait for scripts to load
      let attempts = 0;
      const maxAttempts = 50;
      const checkRTE = () => {
        if (window.RichTextEditor && window.RTE_DefaultConfig) {
          initializeEditor();
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(checkRTE, 100);
        } else {
          console.error('RichTextEditor scripts failed to load');
        }
      };
      checkRTE();
    }

    return () => {
      // Clear polling interval
      if (pollInterval.current) {
        clearInterval(pollInterval.current);
      }
      
      // Clean up floating panels before destroying editor
      const floatPanels = document.querySelectorAll('rte-floatpanel, .rte-floatpanel, [class*="rte-float"]');
      floatPanels.forEach(panel => {
        if (panel.parentNode) {
          panel.parentNode.removeChild(panel);
        }
      });
      
      // Destroy RTE instance
      if (rteInstance.current) {
        try {
          if (rteInstance.current.destroy) {
            rteInstance.current.destroy();
          }
        } catch (e) {
          console.warn('Error destroying RTE instance:', e);
        }
      }
      
      // Additional cleanup for any remaining RTE elements
      setTimeout(() => {
        const remainingRTEElements = document.querySelectorAll('[class*="rte-"], rte-floatpanel');
        remainingRTEElements.forEach(element => {
          if (element.parentNode && !document.querySelector('.richtexteditor')) {
            element.parentNode.removeChild(element);
          }
        });
      }, 100);
      
      isInitialized.current = false;
    };
  }, []); // No dependencies to prevent re-initialization

  // Handle value changes from parent
  useEffect(() => {
    if (rteInstance.current && isInitialized.current && value !== undefined) {
      const currentContent = rteInstance.current.getHTMLCode();
      if (currentContent !== value) {
        rteInstance.current.setHTMLCode(value);
      }
    }
  }, [value]);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    setContent: (content: string) => {
      if (rteInstance.current && isInitialized.current) {
        rteInstance.current.setHTMLCode(content);
      }
    },
    getContent: () => {
      if (rteInstance.current && isInitialized.current) {
        return rteInstance.current.getHTMLCode();
      }
      return '';
    },
  }), []);

  return (
    <Root className={clsx('w-full overflow-hidden rounded-sm border-1', className)}>
      <div ref={containerRef} />
    </Root>
  );
});

RTEEditor.displayName = 'RTEEditor';

export default RTEEditor;