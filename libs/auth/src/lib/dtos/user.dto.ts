import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsUUID,
  Validate,
  IsEnum,
  MinLength,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { UserRoles } from '../enums';
import { Expose } from 'class-transformer';
import { QueryOptionsDto } from '@igov/common';

@ValidatorConstraint({ name: 'MatchPasswords', async: false })
class MatchPasswordsConstraint implements ValidatorConstraintInterface {
  validate(confirmPassword: string, args: ValidationArguments) {
    const object = args.object as ChangePasswordDto;
    return confirmPassword === object.newPassword;
  }

  defaultMessage(args: ValidationArguments) {
    return 'New password and confirm password do not match';
  }
}

export class ChangePasswordDto {
  @ApiProperty({ description: 'Existing User ID' })
  @IsUUID()
  userId!: string;

  @ApiProperty({ description: 'Current password' })
  @IsString()
  currentPassword!: string;

  @ApiProperty({ description: 'Choose new password' })
  @IsString()
  newPassword!: string;

  @ApiProperty({ description: 'Confirm new password' })
  @IsString()
  @Validate(MatchPasswordsConstraint)
  confirmPassword!: string;
}

export class RegisterDto {
  @IsEmail()
  @ApiProperty()
  email!: string;

  @IsString()
  @MinLength(6)
  @ApiProperty()
  password!: string;

  @IsString()
  @ApiProperty()
  firstName!: string;

  @IsString()
  @ApiProperty()
  lastName!: string;

  @IsEnum(UserRoles)
  @IsOptional()
  @ApiProperty({ enum: UserRoles, required: false })
  role?: UserRoles = UserRoles.ADMIN;
}

export class RegisterResponseDto {
  @IsString()
  @ApiProperty()
  id!: string;

  @IsString()
  @ApiProperty()
  email!: string;

  @IsString()
  @ApiProperty()
  firstName?: string;

  @IsString()
  @ApiProperty()
  lastName?: string;

  @IsString()
  @ApiProperty()
  createdAt!: Date;

  @IsString()
  @ApiProperty()
  updatedAt!: Date;

  @IsString()
  @ApiProperty()
  token!: string;

  @ApiProperty({ enum: UserRoles })
  @IsString()
  role!: UserRoles;
}

export class LoginDto {
  @IsEmail()
  @ApiProperty()
  email!: string;

  @IsString()
  @ApiProperty()
  password!: string;
}

export class LoginResponseDto {
  @IsString()
  @ApiProperty()
  id!: string;

  @IsString()
  @ApiProperty()
  email!: string;

  @IsString()
  @ApiProperty()
  createdAt!: Date;

  @IsString()
  @ApiProperty()
  updatedAt!: Date;

  @IsString()
  @ApiProperty()
  token!: string;

  @ApiProperty({ enum: UserRoles })
  @IsString()
  role!: UserRoles;
}

export class UserResponseDto {
  @IsString()
  @ApiProperty()
  id!: string;

  @IsString()
  @ApiProperty()
  firstName?: string;

  @IsString()
  @ApiProperty()
  lastName?: string;

  @IsString()
  @ApiProperty()
  email!: string;

  @IsString()
  @ApiProperty()
  createdAt!: Date;

  @IsString()
  @ApiProperty()
  updatedAt!: Date;

  @ApiProperty({ enum: UserRoles })
  @IsString()
  @Expose()
  role!: UserRoles;
}

export class QueryUserDto extends PartialType(QueryOptionsDto) {
  @ApiPropertyOptional({ description: 'Search keyword (email, first name, last name etc.)' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by user roles', enum: UserRoles })
  @IsOptional()
  @IsEnum(UserRoles)
  role?: UserRoles;

  @ApiPropertyOptional({ description: 'Filter by Email' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Filter by User ID' })
  @IsOptional()
  @IsUUID()
  userId?: string;
}

export class PaginatedUserResponseDto {
  @ApiProperty({ type: [UserResponseDto] })
  data!: UserResponseDto[];

  @ApiProperty({ description: 'Total number of users' })
  total!: number;
}
