import clsx from 'clsx';
import MemoStatus from '@/app/(control-panel)/portal/e-messaging/memo/MemoCategory';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import moment from 'moment';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';
import { darken } from '@mui/material/styles';
import Tooltip from '@mui/material/Tooltip';
import { SignatureIcon } from 'lucide-react';
import { MemoResponseDto, MemoStatusEnums } from '@/services/api/types/memo.types';

type MemoInfoProps = {
  memo: MemoResponseDto;
  className?: string;
};

function MemoInfo({ memo, className }: MemoInfoProps) {
  if (!memo) {
    return null;
  }

  return (
    <div className={clsx('w-full', className)}>
      <div className="flex items-center justify-between mb-4">
        <MemoStatus status={memo.status} />
        {/*<FuseSvgIcon className="text-green-600" size={18}>*/}
        {/*  heroicons-outline:check-badge*/}
        {/*</FuseSvgIcon>*/}
      </div>

      <Typography className="text-lg font-medium">{memo.title}</Typography>

      <Typography className="text-md mt-0.5 line-clamp-2" color="text.secondary">
        {memo.reference}
      </Typography>

      <Divider className="w-12 my-6 border-1" />

      <Typography className="flex items-center space-x-1.5 text-md" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          From: {memo.fromEmployee?.firstName} {memo.fromEmployee?.lastName}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:clock
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Posted: {moment(memo.createdAt).format('MMMM Do YYYY, h:mm a')}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <SignatureIcon color="#bdbdbd" className="font-light" />
        <span className="whitespace-nowrap leading-none">
          Signed By:{' '}
          {memo.status === MemoStatusEnums.Approved
            ? `${memo.fromEmployee?.firstName} ${memo.fromEmployee?.lastName}`
            : 'Not yet signed'}
        </span>
      </Typography>

      <div className="flex items-center mt-5 gap-2">
        <Typography className="flex items-center space-x-1.5 text-md " color="text.secondary">
          <FuseSvgIcon color="disabled" size={20}>
            heroicons-outline:users
          </FuseSvgIcon>
          <span className="whitespace-nowrap leading-none">To:</span>
        </Typography>
        <AvatarGroup max={3} className="justify-end">
          {memo.toEmployees?.map((employee) => (
            <Tooltip
              key={employee.userId}
              title={`${employee.firstName} ${employee.lastName}`}
              placement="top"
              arrow={true}
            >
              <Avatar
                key={employee.userId}
                sx={(theme) => ({
                  background: (theme) => darken(theme.palette.background.paper, 0.05),
                  color: theme.palette.text.secondary,
                })}
                className="avatar text-sm w-6 h-6"
                alt={`${employee.firstName} ${employee.lastName}`}
              >
                {employee.firstName[0]}
                {employee.lastName[0]}
              </Avatar>
            </Tooltip>
          ))}
        </AvatarGroup>
      </div>

      <div className="flex items-center mt-2 gap-2">
        <Typography className="flex items-center space-x-1.5 text-md " color="text.secondary">
          <FuseSvgIcon color="disabled" size={20}>
            heroicons-outline:arrow-path
          </FuseSvgIcon>
          <span className="whitespace-nowrap leading-none">Through:</span>
        </Typography>
        {memo.throughEmployees && memo.throughEmployees.length > 0 ? (
          <AvatarGroup max={3} className="justify-end">
            {memo.throughEmployees.map((employee) => (
              <Tooltip
                key={employee.userId}
                title={`${employee.firstName} ${employee.lastName}`}
                placement="top"
                arrow={true}
              >
                <Avatar
                  key={employee.userId}
                  sx={(theme) => ({
                    background: (theme) => darken(theme.palette.background.paper, 0.05),
                    color: theme.palette.text.secondary,
                  })}
                  className="avatar text-sm w-6 h-6"
                  alt={`${employee.firstName} ${employee.lastName}`}
                >
                  {employee.firstName[0]}
                  {employee.lastName[0]}
                </Avatar>
              </Tooltip>
            ))}
          </AvatarGroup>
        ) : (
          <Typography className="text-md" color="text.secondary">
            N/A
          </Typography>
        )}
      </div>
    </div>
  );
}

export default MemoInfo;
