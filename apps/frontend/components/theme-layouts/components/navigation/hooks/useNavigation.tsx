'use client';

import { useAppSelector } from '@/store/hooks';
import { useMemo, useEffect } from 'react';
import i18n from '@i18n';
import useI18n from '@i18n/useI18n';
import FuseUtils from '@fuse/utils';
import FuseNavigationHelper from '@fuse/utils/FuseNavigationHelper';
import { FuseNavItemType } from '@fuse/core/FuseNavigation/types/FuseNavItemType';
import { selectNavigationAll } from '../store/navigationSlice';
import { usePathname } from 'next/navigation';
import { useAppDispatch } from '@/store/hooks';
import { setNavigationBasedOnPath } from '../store/navigationSlice';

function useNavigation() {
  const userRole = [];
  const { languageId } = useI18n();
  const pathname = usePathname();
  const dispatch = useAppDispatch();

  // Determine if we're in an admin path
  const isAdminPath = useMemo(() => {
    return pathname ? pathname.startsWith('/admin') : false;
  }, [pathname]);

  // Update navigation when path changes between admin and non-admin
  useEffect(() => {
    dispatch(setNavigationBasedOnPath(isAdminPath));
  }, [dispatch, isAdminPath]);

  const navigationData = useAppSelector(selectNavigationAll);

  const navigation = useMemo(() => {
    const _navigation = FuseNavigationHelper.unflattenNavigation(navigationData);

    function setAdditionalData(data: FuseNavItemType[]): FuseNavItemType[] {
      return data?.map((item) => ({
        hasPermission: Boolean(FuseUtils.hasPermission(item?.auth, userRole)),
        ...item,
        ...(item?.translate && item?.title
          ? { title: i18n.t(`navigation:${item?.translate}`) }
          : {}),
        ...(item?.children ? { children: setAdditionalData(item?.children) } : {}),
      }));
    }

    const translatedValues = setAdditionalData(_navigation);

    return translatedValues;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigationData, userRole, languageId]);

  const flattenNavigation = useMemo(() => {
    return FuseNavigationHelper.flattenNavigation(navigation);
  }, [navigation]);

  return { navigation, flattenNavigation, isAdminPath };
}

export default useNavigation;
