import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsOptional, IsBoolean, IsObject } from 'class-validator';
import { EmployeeResponseDto } from '@igov/employee';
import { QueryOptionsDto, CreateAttachmentDto } from '@igov/common';

export class CreateGDriveFileDto {
  @ApiProperty()
  @IsNotEmpty()
  fileName!: string;

  employeeId!: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  folderId?: string;

  @ApiProperty({ required: false, type: 'string', format: 'date-time' })
  @IsOptional()
  readDate?: Date;

  @ApiProperty({ type: CreateAttachmentDto })
  @IsObject()
  attachment!: CreateAttachmentDto;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  archive!: boolean;
}

export class UpdateGDriveFileDto extends PartialType(CreateGDriveFileDto) {}

export class QueryGDriveFileDto extends QueryOptionsDto {
  employeeId!: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  folderId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  search?: string;
}

export class QueryGDriveFileByFolderDto extends OmitType(QueryGDriveFileDto, [
  'folderId',
] as const) {}

export class GDriveFileResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  fileName!: string;

  @ApiProperty()
  employeeId!: string;

  @ApiProperty({ required: false })
  folderId?: string;

  @ApiProperty({ required: false, type: 'string', format: 'date-time' })
  readDate?: Date;

  @ApiProperty({ type: CreateAttachmentDto })
  attachment!: CreateAttachmentDto;

  @ApiProperty({ type: () => EmployeeResponseDto, description: 'Employee' })
  employee!: EmployeeResponseDto;

  @ApiProperty()
  archive!: boolean;

  @ApiProperty()
  createdAt?: Date;

  @ApiProperty()
  updatedAt?: Date;
}

export class PaginatedGDriveFileResponseDto {
  @ApiProperty({ type: [GDriveFileResponseDto] })
  data!: GDriveFileResponseDto[];

  @ApiProperty()
  total!: number;
}
