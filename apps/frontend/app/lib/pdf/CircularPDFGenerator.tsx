import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image, Font } from '@react-pdf/renderer';
import { CircularResponseDto } from '@/services/api/types/circular.types';
import { OrganizationProfileResponseDto } from '@/services/api/types/organizationProfile.types';
import OrgProfilePDF from './components/OrgProfilePDF';

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontSize: 12,
    fontFamily: 'Helvetica',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333333',
  },
  detailsSection: {
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    width: 120,
    color: '#333333',
  },
  detailValue: {
    fontSize: 12,
    flex: 1,
    color: '#666666',
  },
  contentSection: {
    marginTop: 20,
  },
  contentTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  },
  contentText: {
    fontSize: 11,
    lineHeight: 1.6,
    color: '#444444',
    textAlign: 'justify',
    minPresenceAhead: 10,
  },
  longContentContainer: {
    minPresenceAhead: 50,
  },
  signatureSection: {
    marginTop: 20,
    paddingTop: 20,
    alignItems: 'flex-start',
  },
  signatureLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333333',
  },
  signatureName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333333',
    marginTop: 5,
  },
  signatureTitle: {
    fontSize: 10,
    color: '#666666',
    marginTop: 2,
  },
  signatureImage: {
    width: 120,
    height: 40,
    marginTop: 10,
    marginBottom: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 10,
    color: '#888888',
    borderTopWidth: 1,
    borderTopColor: '#eeeeee',
    paddingTop: 10,
  },
  // Rich content styles
  heading1: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    marginTop: 12,
    color: '#222222',
  },
  heading2: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 6,
    marginTop: 10,
    color: '#333333',
  },
  heading3: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 8,
    color: '#444444',
  },
  paragraph: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 8,
    color: '#444444',
  },
  listItem: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
    color: '#444444',
  },
  // Table styles with proper border alignment
  table: {
    marginTop: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#333333',
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableCell: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    color: '#444444',
    borderRightWidth: 1,
    borderRightColor: '#333333',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tableCellLast: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    color: '#444444',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tableCellHeader: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333333',
    borderRightWidth: 1,
    borderRightColor: '#333333',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tableCellHeaderLast: {
    flex: 1,
    padding: 8,
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333333',
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
});

interface CircularPDFGeneratorProps {
  circular: CircularResponseDto;
  orgProfile?: OrganizationProfileResponseDto | null;
  fromEmployeeSignature?: string | null;
}

// Interface for table data structure
interface TableData {
  headers: string[];
  rows: string[][];
}

// Function to parse inline styles like bold, italic, etc.
const parseInlineStyles = (content: string): React.ReactElement[] => {
  if (!content) return [];

  const elements: React.ReactElement[] = [];
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    if (!line.trim()) {
      elements.push(
        <Text key={index} style={styles.paragraph}>
          {' '}
        </Text>,
      );
      return;
    }

    // Handle inline bold styling
    const parts: (string | { text: string; bold: boolean })[] = [];
    let remaining = line;

    // Parse bold spans with style="font-weight: bold;"
    const boldStyleRegex = /<span[^>]*style="[^"]*font-weight:\s*bold[^"]*"[^>]*>(.*?)<\/span>/gi;
    let match: RegExpExecArray | null;
    let lastIndex = 0;

    while ((match = boldStyleRegex.exec(remaining)) !== null) {
      // Add text before the bold span
      if (match.index > lastIndex) {
        const beforeText = remaining.substring(lastIndex, match.index);
        if (beforeText) parts.push(beforeText);
      }

      // Add the bold text
      parts.push({ text: match[1], bold: true });
      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after last match
    if (lastIndex < remaining.length) {
      const afterText = remaining.substring(lastIndex);
      if (afterText) parts.push(afterText);
    }

    // If no matches, treat the whole line as normal text
    if (parts.length === 0) {
      parts.push(remaining);
    }

    // Clean up HTML tags from text parts and render
    const textElements: React.ReactElement[] = [];
    parts.forEach((part, partIndex) => {
      if (typeof part === 'string') {
        const cleanText = part.replace(/<[^>]*>/g, '').trim();
        if (cleanText) {
          textElements.push(
            <Text key={partIndex} style={styles.paragraph}>
              {cleanText}
            </Text>,
          );
        }
      } else {
        const cleanText = part.text.replace(/<[^>]*>/g, '').trim();
        if (cleanText) {
          textElements.push(
            <Text key={partIndex} style={[styles.paragraph, { fontWeight: 'bold' }]}>
              {cleanText}
            </Text>,
          );
        }
      }
    });

    if (textElements.length > 0) {
      elements.push(
        <Text key={index} style={styles.paragraph}>
          {textElements}
        </Text>,
      );
    }
  });

  return elements;
};

// Enhanced HTML parser that properly handles tables
const parseHtmlContent = (html: string): React.ReactElement[] => {
  if (!html) return [];

  const elements: React.ReactElement[] = [];
  let keyIndex = 0;

  // Clean up HTML entities first
  let cleanHtml = html
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'");

  // Step 1: Extract tables (both standalone and wrapped in p tags)
  const tableReplacements: { [key: string]: TableData } = {};
  let tableIndex = 0;

  // Handle tables wrapped in p tags first
  cleanHtml = cleanHtml.replace(/<p[^>]*>([^<]*<table[^>]*>.*?<\/table>[^<]*)<\/p>/gis, '$1');

  // Now extract all tables
  cleanHtml = cleanHtml.replace(/<table[^>]*>(.*?)<\/table>/gis, (_, tableContent) => {
    const placeholder = `__TABLE_${tableIndex}__`;

    // Extract table rows
    const rowMatches = tableContent.match(/<tr[^>]*>(.*?)<\/tr>/gis) || [];
    const tableData: TableData = { headers: [], rows: [] };

    rowMatches.forEach((rowHtml: string, rowIdx: number) => {
      // Extract all cells (both th and td)
      const cellMatches = rowHtml.match(/<(td|th)[^>]*>(.*?)<\/(td|th)>/gis) || [];
      const cellData = cellMatches.map((cell: string) => {
        // Remove HTML tags and clean up the content
        return cell
          .replace(/<[^>]*>/g, '')
          .replace(/&nbsp;/g, ' ')
          .trim();
      });

      if (cellData.length > 0) {
        // Check if this row contains mostly th elements (header row)
        const isHeaderRow =
          rowHtml.includes('<th') || (rowIdx === 0 && tableData.headers.length === 0);

        if (isHeaderRow && tableData.headers.length === 0) {
          tableData.headers = cellData;
        } else {
          tableData.rows.push(cellData);
        }
      }
    });

    // If no explicit headers were found, use first row as headers
    if (tableData.headers.length === 0 && tableData.rows.length > 0) {
      tableData.headers = tableData.rows.shift() || [];
    }

    tableReplacements[placeholder] = tableData;
    tableIndex++;
    return placeholder;
  });

  // Step 2: Split content and process each part
  const parts = cleanHtml.split(
    /(<h[1-6][^>]*>.*?<\/h[1-6]>|<p[^>]*>.*?<\/p>|<ul[^>]*>.*?<\/ul>|<ol[^>]*>.*?<\/ol>)/gis,
  );

  for (const part of parts) {
    if (!part.trim()) continue;

    // Handle table placeholders (both standalone and wrapped in p tags)
    const tableMatch = part.match(/__TABLE_(\d+)__/);
    if (tableMatch) {
      const placeholder = tableMatch[0];
      const tableData = tableReplacements[placeholder];
      if (tableData && (tableData.headers.length > 0 || tableData.rows.length > 0)) {
        elements.push(renderTable(tableData, keyIndex++));
      }
      continue;
    }

    // Handle headings
    const headingMatch = part.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/is);
    if (headingMatch) {
      const level = parseInt(headingMatch[1]);
      const content = headingMatch[2].replace(/<[^>]*>/g, '').trim();

      if (content) {
        const headingStyle =
          level === 1 ? styles.heading1 : level === 2 ? styles.heading2 : styles.heading3;
        elements.push(
          <Text key={keyIndex++} style={headingStyle}>
            {content}
          </Text>,
        );
      }
      continue;
    }

    // Handle unordered lists
    const ulMatch = part.match(/<ul[^>]*>(.*?)<\/ul>/is);
    if (ulMatch) {
      const items = (ulMatch[1].match(/<li[^>]*>(.*?)<\/li>/gis) || [])
        .map((li: string) => li.replace(/<[^>]*>/g, '').trim())
        .filter((item: string) => item);

      if (items.length > 0) {
        elements.push(
          <View key={keyIndex++}>
            {items.map((item: string, itemIndex: number) => (
              <Text key={itemIndex} style={styles.listItem}>
                • {item}
              </Text>
            ))}
          </View>,
        );
      }
      continue;
    }

    // Handle ordered lists
    const olMatch = part.match(/<ol[^>]*>(.*?)<\/ol>/is);
    if (olMatch) {
      const items = (olMatch[1].match(/<li[^>]*>(.*?)<\/li>/gis) || [])
        .map((li: string) => li.replace(/<[^>]*>/g, '').trim())
        .filter((item: string) => item);

      if (items.length > 0) {
        elements.push(
          <View key={keyIndex++}>
            {items.map((item: string, itemIndex: number) => (
              <Text key={itemIndex} style={styles.listItem}>
                {itemIndex + 1}. {item}
              </Text>
            ))}
          </View>,
        );
      }
      continue;
    }

    // Handle paragraphs and remaining text with inline styling
    let paragraphContent = part
      .replace(/<p[^>]*>/gi, '')
      .replace(/<\/p>/gi, '')
      .replace(/<br\s*\/?>/gi, '\n');

    if (paragraphContent.trim()) {
      // Parse inline styles like bold, italic, etc.
      const parsedContent = parseInlineStyles(paragraphContent.trim());
      if (parsedContent.length > 0) {
        elements.push(
          <View key={keyIndex++}>
            {parsedContent.map((line: React.ReactElement, lineIndex: number) => (
              <View key={lineIndex}>{line}</View>
            ))}
          </View>,
        );
      }
    }
  }

  return elements;
};

// Helper function to render a table as PDF components
const renderTable = (tableData: TableData, key: number): React.ReactElement => {
  return (
    <View key={key} style={styles.table}>
      {/* Table Header */}
      {tableData.headers.length > 0 && (
        <View style={styles.tableRow}>
          {tableData.headers.map((header: string, headerIndex: number) => (
            <Text
              key={headerIndex}
              style={
                headerIndex === tableData.headers.length - 1
                  ? styles.tableCellHeaderLast
                  : styles.tableCellHeader
              }
            >
              {header || ' '}
            </Text>
          ))}
        </View>
      )}

      {/* Table Rows */}
      {tableData.rows.map((row: string[], rowIndex: number) => (
        <View key={rowIndex} style={styles.tableRow}>
          {row.map((cell: string, cellIndex: number) => (
            <Text
              key={cellIndex}
              style={cellIndex === row.length - 1 ? styles.tableCellLast : styles.tableCell}
            >
              {cell || ' '}
            </Text>
          ))}
        </View>
      ))}
    </View>
  );
};

// Function to format date
const formatDate = (dateInput: string | Date): string => {
  const date = new Date(dateInput);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

// Function to format department list
const formatDepartmentList = (departments: any[]): string => {
  if (!departments || departments.length === 0) return 'N/A';
  return departments.map((dept) => dept.name).join(', ');
};

// Function to get the appropriate signature URL for PDF generation
const getAbsoluteSignatureUrl = (signatureUrl: string | undefined): string | null => {
  if (!signatureUrl) {
    return null;
  }

  // If already absolute URL (S3), use proxy to avoid CORS issues with @react-pdf/renderer
  if (signatureUrl.startsWith('http://') || signatureUrl.startsWith('https://')) {
    return `${window.location.origin}/api/proxy-image?url=${encodeURIComponent(signatureUrl)}`;
  }

  // If relative URL, convert to absolute using current origin
  if (signatureUrl.startsWith('/')) {
    return `${window.location.origin}${signatureUrl}`;
  }

  return null;
};

// Signature Image component with error handling
const PDFSignature: React.FC<{ src: string }> = ({ src }) => {
  return <Image style={styles.signatureImage} src={src} />;
};

const CircularPDFGenerator: React.FC<CircularPDFGeneratorProps> = ({
  circular,
  orgProfile,
  fromEmployeeSignature,
}) => {
  const contentElements = parseHtmlContent(circular.body || '');

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header - Organization Profile */}
        <OrgProfilePDF profile={orgProfile} />

        {/* Circular Title */}
        <Text style={styles.title}>CIRCULAR</Text>

        {/* Circular Details */}
        <View style={[styles.detailsSection, styles.longContentContainer]}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Subject:</Text>
            <Text style={styles.detailValue}>{circular.title}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Reference No:</Text>
            <Text style={styles.detailValue}>{circular.reference}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>To Departments:</Text>
            <Text style={styles.detailValue}>
              {formatDepartmentList(circular.circularDepartment)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>From:</Text>
            <Text style={styles.detailValue}>
              {circular.fromEmployee
                ? `${circular.fromEmployee.firstName} ${circular.fromEmployee.lastName}`
                : 'N/A'}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date:</Text>
            <Text style={styles.detailValue}>{formatDate(circular.createdAt)}</Text>
          </View>
        </View>

        {/* Circular Content */}
        {contentElements.length > 0 && (
          <View style={[styles.contentSection, styles.longContentContainer]}>
            {contentElements}
          </View>
        )}

        {/* Signature Section (only for published circulars) */}
        {circular.status === 'Publish' && circular.fromEmployee && (
          <View style={styles.signatureSection}>
            <Text style={styles.signatureLabel}>Authorized by:</Text>

            {/* Signature Image */}
            {fromEmployeeSignature &&
              (() => {
                const signatureSrc = getAbsoluteSignatureUrl(fromEmployeeSignature);
                return signatureSrc ? <PDFSignature src={signatureSrc} /> : null;
              })()}

            <Text style={styles.signatureName}>
              {circular.fromEmployee.firstName} {circular.fromEmployee.lastName}
            </Text>
            <Text style={styles.signatureTitle}>Authorized Signatory</Text>
          </View>
        )}

        {/* Footer */}
        <Text style={styles.footer}>
          Generated on{' '}
          {new Date().toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          })}{' '}
          • Circular ID: {circular.id}
        </Text>
      </Page>
    </Document>
  );
};

export default CircularPDFGenerator;
