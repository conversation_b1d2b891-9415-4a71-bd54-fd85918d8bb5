'use client';

import { useState, useEffect, useCallback } from 'react';
import { departmentApiService } from '@/services/api/departmentService';
import { DepartmentResponseDto } from '@/services/api/types/department.types';

// Define the structure for department options
export interface DepartmentOption {
  value: string;
  label: string;
}

/**
 * Interface for the hook's return value
 */
export interface UseDepartmentsReturn {
  /** Array of departments formatted for dropdown selection */
  departments: DepartmentOption[];
  /** Whether data is being fetched */
  isLoading: boolean;
  /** Error message if fetch fails */
  error: string | null;
  /** Function to manually refetch data */
  refetch: () => Promise<void>;
}

/**
 * Custom hook for fetching and managing department data for selection dropdowns.
 *
 * This hook provides a structured list of departments suitable for dropdown components.
 */
export function useDepartments(): UseDepartmentsReturn {
  const [departments, setDepartments] = useState<DepartmentOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAndProcessData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch all departments
      const departmentsResponse = await departmentApiService.getAllDepartments();

      const departmentData = departmentsResponse || [];

      // Transform departments into dropdown options
      const processedDepartments: DepartmentOption[] = departmentData
        .filter((dept) => dept.id && dept.name) // Ensure required fields are present
        .map((dept) => ({
          value: dept.id,
          label: dept.name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically

      setDepartments(processedDepartments);
    } catch (err) {
      console.error('Failed to fetch or process department data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch departments';
      setError(errorMessage);
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Manual refetch function
  const refetch = useCallback(async () => {
    await fetchAndProcessData();
  }, [fetchAndProcessData]);

  // Fetch data on mount
  useEffect(() => {
    fetchAndProcessData();
  }, [fetchAndProcessData]);

  return {
    departments,
    isLoading,
    error,
    refetch,
  };
}
