import { RegistryCollaboratorRepository, RegistryRepository } from '../repositories';
import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import {
  PaginatedRegistryCollaboratorResponseDto,
  QueryCollaboratorDto,
  RegistryCollaboratorResponseDto,
} from '../dtos';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class RegistryCollaboratorService {
  constructor(
    private readonly registryRepository: RegistryRepository,
    private readonly registryCollaboratorRepository: RegistryCollaboratorRepository,
  ) {}

  async createCollaborator(
    registryId: string,
    employeeId: string,
  ): Promise<RegistryCollaboratorResponseDto> {
    const registry = await this.registryRepository.findById(registryId);

    if (!registry) {
      throw new NotFoundException(`Registry with ID: ${registryId} does not exist`);
    }

    const existingCollaborator =
      await this.registryCollaboratorRepository.findByRegistryIdAndEmployeeId(
        registryId,
        employeeId,
      );
    if (existingCollaborator) {
      throw new ConflictException(
        `Employee with ID: ${employeeId} is already a collaborator in registry with ID: ${registryId}`,
      );
    }

    const collaborator = await this.registryCollaboratorRepository.saveCollaborator([
      {
        registryId,
        employeeId,
      },
    ]);
    return plainToInstance(RegistryCollaboratorResponseDto, collaborator[0]);
  }

  async createCollaborators(
    registryId: string,
    employeeIds: string[],
  ): Promise<RegistryCollaboratorResponseDto[]> {
    const registry = await this.registryRepository.findById(registryId);

    if (!registry) {
      throw new NotFoundException(`Registry with ID: ${registryId} does not exist`);
    }

    const collaboratorsToSave = employeeIds.map((employeeId) => ({
      registryId,
      employeeId,
    }));

    const savedCollaborators =
      await this.registryCollaboratorRepository.saveCollaborator(collaboratorsToSave);
    return savedCollaborators.map((collaborator) =>
      plainToInstance(RegistryCollaboratorResponseDto, collaborator),
    );
  }

  async getCollaborators(
    query: QueryCollaboratorDto,
  ): Promise<PaginatedRegistryCollaboratorResponseDto> {
    const { registryId, limit = 10, skip = 0 } = query;
    const collaborators = await this.registryCollaboratorRepository.findAll(
      { registryId },
      { limit, skip },
    );

    const { total, data } = collaborators;

    if (!total || total === 0) {
      throw new NotFoundException(`No collaborators found for registry with ID: ${registryId}`);
    }

    return {
      data,
      total,
    };
  }

  async removeCollaborators(registryId: string, employeeIds: string[]): Promise<void> {
    const registry = await this.registryRepository.findById(registryId);
    if (!registry) {
      throw new NotFoundException(`Registry with ID: ${registryId} does not exist`);
    }

    const collaborators = await this.registryCollaboratorRepository.findByRegistryId(registryId);

    if (!collaborators || collaborators.length === 0) {
      throw new NotFoundException(`No collaborators found for registry with ID: ${registryId}`);
    }

    const collaboratorsToRemove = collaborators.filter((collaborator) =>
      employeeIds.includes(collaborator.employeeId),
    );

    if (collaboratorsToRemove.length === 0) {
      throw new NotFoundException(
        `No matching collaborators found for provided employee IDs in registry with ID: ${registryId}`,
      );
    }

    await this.registryCollaboratorRepository.repository.remove(collaboratorsToRemove);
  }

  async removeCollaborator(registryId: string, employeeId: string): Promise<void> {
    const registry = await this.registryRepository.findById(registryId);
    if (!registry) {
      throw new NotFoundException(`Registry with ID: ${registryId} does not exist`);
    }

    const collaborator = await this.registryCollaboratorRepository.findByRegistryIdAndEmployeeId(
      registryId,
      employeeId,
    );
    if (!collaborator) {
      throw new NotFoundException(
        `Collaborator with Employee ID: ${employeeId} not found in registry with ID: ${registryId}`,
      );
    }

    await this.registryCollaboratorRepository.repository.remove(collaborator);
  }
}
