import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@igov/common';
import { RegistryEntity } from './registry.entity';
import { Employee } from '@igov/employee';
import { IRegistryCollaborator } from '../interfaces';

@Entity('registry_collaborators')
export class RegistryCollaboratorEntity extends BaseEntity implements IRegistryCollaborator {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  registryId!: string;

  @Column()
  employeeId!: string;

  @ManyToOne(() => RegistryEntity, (registry) => registry.id)
  @JoinColumn({ name: 'registry_id' })
  registry!: RegistryEntity;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'employee_id' })
  employee!: Employee;
}
