import { apiClient } from './apiClient';
import {
  ChatMessageResponseDto,
  CreateChatMessageDto,
  UserMessagesOptions,
  ConversationOptions,
} from './types/chat.types';

/**
 * Chat API Service
 * 
 * Matches the backend chat-message.controller.ts exactly:
 * - GET /chat-messages - getUserMessages()
 * - GET /chat-messages/:receiverId - getConversation()
 */
class ChatApiService {
  private baseUrl = '/chat-messages';

  /**
   * Get user's messages (unread/undelivered messages TO the user)
   * 
   * Backend: GET /chat-messages
   * Controller method: getUserMessages()
   * Returns: Messages where user is receiver AND (not delivered OR not read)
   * 
   * @param options - Pagination options
   * @returns Promise with array of chat messages
   */
  async getUserMessages(options: UserMessagesOptions = {}): Promise<ChatMessageResponseDto[]> {
    try {
      const params = new URLSearchParams();

      if (options.limit !== undefined) {
        params.append('limit', options.limit.toString());
      }

      if (options.after) {
        params.append('after', options.after.toISOString());
      }

      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : ''}`;
      
      return await apiClient.get<ChatMessageResponseDto[]>(url);
    } catch (error: any) {
      console.error('Failed to get user messages:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get conversation between current user and another user
   * 
   * Backend: GET /chat-messages/:receiverId
   * Controller method: getConversations() 
   * Returns: All messages between current user and specified user (bidirectional)
   * 
   * @param receiverId - ID of the other user in the conversation
   * @param options - Pagination options
   * @returns Promise with array of conversation messages
   */
  async getConversation(
    receiverId: string,
    options: ConversationOptions = {}
  ): Promise<ChatMessageResponseDto[]> {
    if (!receiverId) {
      throw new Error('Receiver ID is required');
    }

    try {
      const params = new URLSearchParams();

      if (options.limit !== undefined) {
        params.append('limit', options.limit.toString());
      }

      if (options.after) {
        params.append('after', options.after.toISOString());
      }

      const url = `${this.baseUrl}/${receiverId}${params.toString() ? `?${params.toString()}` : ''}`;
      
      return await apiClient.get<ChatMessageResponseDto[]>(url);
    } catch (error: any) {
      console.error(`Failed to get conversation with ${receiverId}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Mark a message as read
   * 
   * Backend: PATCH /chat-messages/:messageId/read
   * Controller method: markAsRead()
   * 
   * @param messageId - ID of the message to mark as read
   * @returns Promise with updated message
   */
  async markAsRead(messageId: string): Promise<ChatMessageResponseDto> {
    if (!messageId) {
      throw new Error('Message ID is required');
    }

    try {
      const url = `${this.baseUrl}/${messageId}/read`;
      return await apiClient.patch<ChatMessageResponseDto>(url);
    } catch (error: any) {
      console.error(`Failed to mark message ${messageId} as read:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Send a new message (placeholder for Phase 3 WebSocket integration)
   * 
   * Note: In Phase 3, this will be handled by WebSocket.
   * For now, including for API completeness but not used.
   * 
   * @param data - Message data to send
   * @returns Promise with created message
   */
  async sendMessage(data: CreateChatMessageDto): Promise<ChatMessageResponseDto> {
    if (!data.senderId) {
      throw new Error('Sender ID is required');
    }
    if (!data.receiverId) {
      throw new Error('Receiver ID is required');
    }
    if (!data.content?.trim()) {
      throw new Error('Message content is required');
    }

    try {
      // Note: This endpoint may not exist yet, reserved for Phase 3
      return await apiClient.post<ChatMessageResponseDto>(this.baseUrl, data);
    } catch (error: any) {
      console.error('Failed to send message:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }
}

// Export singleton instance
export const chatApiService = new ChatApiService();