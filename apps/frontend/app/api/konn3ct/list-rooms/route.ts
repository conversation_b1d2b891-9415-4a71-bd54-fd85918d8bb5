import { NextRequest, NextResponse } from 'next/server';

const KONN3CT_API_URL = process.env.KONN3CT_API_URL || 'https://dev.konn3ct.ng/api';
const KONN3CT_TOKEN = process.env.KONN3CT_API_TOKEN;

export async function GET(request: NextRequest) {
  try {
    if (!KONN3CT_TOKEN) {
      return NextResponse.json({ error: 'Konn3ct API token not configured' }, { status: 500 });
    }

    const response = await fetch(`${KONN3CT_API_URL}/list-rooms`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${KONN3CT_TOKEN}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || 'Failed to list rooms' },
        { status: response.status },
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Konn3ct API Error (list-rooms):', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
