'use client';

import { useState, FormEvent } from 'react';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import { Button, Divider, InputAdornment, Box, CircularProgress } from '@mui/material';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { useChangePassword } from './useChangePassword';

function SecurityTab() {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const { isLoading, error, success, changePassword, clearMessages } = useChangePassword();

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const success = await changePassword(currentPassword, newPassword, confirmPassword);
    if (success) {
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    }
  };

  const handleCancel = () => {
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    clearMessages();
  };

  const isFormValid =
    currentPassword && newPassword && confirmPassword && newPassword === confirmPassword;

  return (
    <div className="w-full max-w-5xl">
      <form onSubmit={handleSubmit}>
        <div className="w-full">
          <Typography className="text-xl">Change your password</Typography>
          <Typography color="text.secondary">
            Create a new secure password to access your account
          </Typography>
        </div>

        <div className="mt-8 grid w-full gap-1.5 sm:grid-cols-4 space-y-8">
          <div className="sm:col-span-4">
            <TextField
              label="Current Password"
              type="password"
              variant="outlined"
              fullWidth
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              disabled={isLoading}
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon size={20}>heroicons-outline:key</FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          <div className="sm:col-span-4">
            <TextField
              label="New Password"
              type="password"
              variant="outlined"
              fullWidth
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              disabled={isLoading}
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon size={20}>heroicons-outline:key</FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          <div className="sm:col-span-4">
            <TextField
              label="Password Confirmation"
              type="password"
              variant="outlined"
              fullWidth
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={isLoading}
              required
              error={!!confirmPassword && newPassword !== confirmPassword}
              helperText={
                confirmPassword && newPassword !== confirmPassword ? 'Passwords do not match' : ''
              }
              FormHelperTextProps={{
                sx: {
                  color: 'error.main',
                  mx: 0,
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon size={20}>heroicons-outline:key</FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>
        </div>

        <Divider className="mb-10 mt-11 border-t border-[#DBDBDB]" />

        <div className="flex flex-col space-y-3">
          <div className="flex justify-end">
            {error && <Typography className="text-red-500 text-sm">{error}</Typography>}
          </div>
          <div className="flex items-center justify-end space-x-2">
            <Button variant="outlined" onClick={handleCancel} disabled={isLoading} sx={{ mr: 2 }}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isLoading || !isFormValid}
            >
              {isLoading ? <CircularProgress size={15} color="inherit" /> : 'Change Password'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

export default SecurityTab;
