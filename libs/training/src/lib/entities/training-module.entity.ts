import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@igov/common';
import { ITrainingAttachment, ITrainingModule } from '../interfaces';
import { TrainingEntity } from './training.entity';

@Entity('training_modules')
export class TrainingModuleEntity extends BaseEntity implements ITrainingModule {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid', nullable: false })
  trainingId!: string;

  @Column({ type: 'text', nullable: false })
  title!: string;

  @Column({ type: 'text', nullable: false })
  description!: string;

  @Column({ type: 'jsonb', nullable: false })
  material!: ITrainingAttachment[];

  @ManyToOne(() => TrainingEntity, (training) => training.id, { eager: false })
  @JoinColumn({ name: 'training_id' })
  training?: TrainingEntity;
}
