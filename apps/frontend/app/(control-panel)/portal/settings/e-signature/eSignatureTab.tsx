'use client';

import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import Typography from '@mui/material/Typography';
import {
  acceptStyle,
  baseStyle,
  focusedStyle,
  img,
  rejectStyle,
  thumb,
  thumbInner,
  thumbsContainer,
} from './drop-zone-styles';
import { Alert, Button, Divider, InputAdornment, CircularProgress } from '@mui/material';
import TextField from '@mui/material/TextField';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { useESignature } from './useESignature';
import { useAuth } from '@/contexts/AuthContext';

interface FileWithPreview extends File {
  preview: string;
}

function ESignatureTab() {
  const { employeeDetails, refresh: refreshAuth } = useAuth();
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [password, setPassword] = useState('');
  const [uploadErrors, setUploadErrors] = useState<{ code: string; message: string }[]>([]);

  const { isLoading, error, uploadSignature, clearMessages } = useESignature({
    onSuccess: refreshAuth,
  });

  const { getRootProps, getInputProps, isFocused, isDragAccept, isDragReject, isDragActive } =
    useDropzone({
      accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.svg'] },
      maxFiles: 1,
      maxSize: 2 * 1024 * 1024, // 2MB
      onDrop: (acceptedFiles, fileRejections) => {
        setUploadErrors(fileRejections.flatMap(({ errors }) => errors));

        // Create preview URLs for the accepted files
        const filesWithPreview = acceptedFiles.map((file) =>
          Object.assign(file, {
            preview: URL.createObjectURL(file),
          }),
        );

        setFiles(filesWithPreview);

        // Clear any previous errors if we have valid files
        if (acceptedFiles.length > 0) {
          setUploadErrors([]);
        }
      },
    });

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      files.forEach((file) => URL.revokeObjectURL(file.preview));
    };
  }, [files]);

  const formatFileSize = useCallback((sizeInBytes: number) => {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} bytes`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (files.length === 0) {
      setUploadErrors([{ code: 'no-file', message: 'Please select a file to upload' }]);
      return;
    }

    if (!password) {
      setUploadErrors([{ code: 'no-password', message: 'Please enter your password' }]);
      return;
    }

    clearMessages();
    const success = await uploadSignature(files[0], password, employeeDetails.eSignature);

    if (success) {
      setFiles([]);
      setPassword('');
    }
  };

  const handleCancel = () => {
    setFiles([]);
    setUploadErrors([]);
    clearMessages();
  };

  // @ts-ignore
  const filePreviews = files.map((file) => (
    <div className="flex items-center gap-4 p-4" key={file.name}>
      {/*@ts-ignore*/}
      <div style={thumb}>
        <div style={thumbInner}>
          <img
            src={file.preview}
            style={img}
            alt="Signature preview"
            onLoad={() => URL.revokeObjectURL(file.preview)}
          />
        </div>
      </div>
      <div className="flex-1">
        <Typography variant="subtitle1" className="font-medium">
          {file.name}
        </Typography>
        <Typography variant="body2" color="text.secondary" className="mt-1">
          {file.type || 'Unknown type'} • {formatFileSize(file.size)}
        </Typography>
      </div>
    </div>
  ));

  const hasESignature = !!employeeDetails?.eSignature;

  // Dropzone styles
  const dropzoneStyles = useMemo(
    () =>
      ({
        ...baseStyle,
        ...(isFocused ? focusedStyle : {}),
        ...(isDragAccept ? acceptStyle : {}),
        ...(isDragReject ? rejectStyle : {}),
        cursor: 'pointer',
        textAlign: 'center',
        borderRadius: '8px',
        border: '2px dashed #e0e0e0',
        transition: 'border .24s ease-in-out',
      }) as React.CSSProperties,
    [isFocused, isDragAccept, isDragReject],
  );

  return (
    <div className="w-full max-w-5xl">
      <form onSubmit={handleSubmit}>
        <div className="space-y-8">
          {/* Header */}
          <div className="w-full">
            <Typography className="text-xl">Manage your e-Signature</Typography>
            <Typography color="text.secondary">
              Use your e-Signature to sign documents digitally. Please upload a clear image of your
              signature.
            </Typography>
          </div>

          {/* Status Alerts */}
          {!hasESignature && (
            <Alert severity="warning">
              Your e-Signature is not configured. You won't be able to sign documents digitally
              until setup is complete.
            </Alert>
          )}

          {/* File Upload Area */}
          <div className="space-y-4">
            <div {...getRootProps({ style: dropzoneStyles })}>
              <input {...getInputProps()} />
              <div className="space-y-2 p-4">
                <FuseSvgIcon className="text-4xl text-gray-400">
                  heroicons-outline:cloud-upload
                </FuseSvgIcon>
                <div>
                  <p className="font-medium">
                    {isDragActive
                      ? 'Drop the files here...'
                      : 'Drag and drop your signature image here, or click to select files'}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Supported formats: JPG, PNG, SVG (Max 2MB)
                  </p>
                </div>
              </div>
            </div>

            {/* File Previews */}
            {filePreviews.length > 0 && (
              <div className="space-y-2">
                <Typography variant="subtitle2" className="font-medium">
                  Selected File:
                </Typography>
                {filePreviews}
              </div>
            )}

            {/* Upload Errors */}
            {uploadErrors.length > 0 && (
              <Alert severity="error">
                <ul className="list-disc pl-5 space-y-1">
                  {uploadErrors.map((err, idx) => (
                    <li key={idx}>{err.message}</li>
                  ))}
                </ul>
              </Alert>
            )}
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <TextField
              label="Password Confirmation"
              fullWidth
              variant="outlined"
              type="password"
              placeholder="Enter your password to confirm"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <FuseSvgIcon className="text-20" size={20} color="action">
                      heroicons-outline:lock-closed
                    </FuseSvgIcon>
                  </InputAdornment>
                ),
              }}
            />
          </div>

          {/* Current Signature Preview */}
          {hasESignature && (
            <div className="space-y-2">
              <Typography variant="subtitle2" className="font-medium">
                Current e-Signature:
              </Typography>
              <div className="inline-block">
                <img
                  src={employeeDetails.eSignature}
                  alt="Current e-Signature"
                  className="h-20 object-contain"
                />
              </div>
            </div>
          )}
        </div>

        <Divider className="mb-10 mt-11 border-t border-[#DBDBDB]" />

        <div className="flex flex-col space-y-3">
          <div className="flex justify-end">
            {error && <Typography className="text-red-500 text-sm">{error}</Typography>}
          </div>
          <div className="flex items-center justify-end space-x-2">
            <Button variant="outlined" onClick={handleCancel} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={isLoading || files.length === 0 || !password}
            >
              {isLoading ? (
                <CircularProgress size={15} color="inherit" />
              ) : hasESignature ? (
                'Update e-Signature'
              ) : (
                'Save e-Signature'
              )}
            </Button>
          </div>
        </div>

        {/* Form Actions */}
      </form>
    </div>
  );
}

export default ESignatureTab;
