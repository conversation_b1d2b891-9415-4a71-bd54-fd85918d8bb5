import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { AttachmentDto } from '../dtos';
import { AnnouncementEnums } from '../enums';
import { QueryOptionsDto } from '@igov/common';

export class CreateAnnouncementDto {
  @ApiProperty({ description: 'Title of the announcement' })
  @IsDefined({ message: 'Title is required' })
  @IsNotEmpty({ message: 'Title cannot be empty' })
  @IsString({ message: 'Title must be a string' })
  title!: string;

  @ApiProperty({ description: 'Message body/content of the announcement' })
  @IsDefined({ message: 'Message is required' })
  @IsNotEmpty({ message: 'Message cannot be empty' })
  @IsString({ message: 'Message must be a string' })
  message!: string;

  @ApiProperty({
    description: 'Announcement attachments (optional)',
    type: [AttachmentDto],
    required: false,
  })
  @IsOptional()
  attachments?: AttachmentDto[];

  @ApiProperty({ description: 'ID of the employee that posted the announcement' })
  @IsDefined({ message: 'PostedBy ID is required' })
  @IsNotEmpty({ message: 'PostedBy ID cannot be empty' })
  @IsUUID()
  postedById!: string;
}

export class UpdateAnnouncementDto {
  @ApiProperty({ description: 'Title of the announcement' })
  @IsDefined({ message: 'Title is required' })
  @IsNotEmpty({ message: 'Title cannot be empty' })
  @IsString({ message: 'Title must be a string' })
  title!: string;

  @ApiProperty({ description: 'Message body/content of the announcement' })
  @IsDefined({ message: 'Message is required' })
  @IsNotEmpty({ message: 'Message cannot be empty' })
  @IsString({ message: 'Message must be a string' })
  message!: string;

  @ApiProperty({ enum: AnnouncementEnums, description: 'Status of the announcement' })
  @IsDefined({ message: 'Announcement status is required' })
  @IsEnum(AnnouncementEnums, { message: 'Invalid status value' })
  status!: AnnouncementEnums;
}

export class AnnouncementResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  message!: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  attachments?: AttachmentDto[];

  @ApiProperty()
  postedById!: string;

  @ApiProperty()
  draftedById!: string;

  @ApiProperty({ enum: AnnouncementEnums })
  status!: AnnouncementEnums;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class PaginatedAnnouncementResponseDto {
  @ApiProperty({ description: 'Total number of announcements', example: 125 })
  total!: number;

  @ApiProperty({ type: [AnnouncementResponseDto], description: 'List of announcements' })
  data!: AnnouncementResponseDto[];
}

export class AnnouncementFilterDto extends PartialType(QueryOptionsDto) {
  @ApiPropertyOptional({ description: 'Filter by Posted ID (UUID)' })
  @IsOptional()
  @IsUUID()
  postedById?: string;

  @ApiPropertyOptional({ description: 'Filter by Drafted by (UUID)' })
  @IsOptional()
  @IsUUID()
  draftedById?: string;

  @ApiPropertyOptional({ description: 'Filter by status', enum: AnnouncementEnums })
  @IsOptional()
  @IsEnum(AnnouncementEnums, { each: true })
  status?: AnnouncementEnums[];

  @ApiPropertyOptional({ description: 'Search keyword (optional)' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by start date (createdAt)' })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiPropertyOptional({ description: 'Filter by end date (createdAt)' })
  @IsOptional()
  @IsDateString()
  toDate?: string;

  showReadStatus?: boolean;

  employeeId?: string;
}
