//Hungarian , Magyar
RTE_DefaultConfig.text_language = "nyelv";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "mégse gombra";	//"Cancel"
RTE_DefaultConfig.text_normal = "normál";	//"Normal"
RTE_DefaultConfig.text_h1 = "1. címsor";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "2. címsor";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Címsor 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "4. címsor";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "5. címsor";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Címsor 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Címsor 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "zárja be";	//"Close"
RTE_DefaultConfig.text_bold = "merész";	//"Bold"
RTE_DefaultConfig.text_italic = "dőlt";	//"Italic"
RTE_DefaultConfig.text_underline = "aláhúzás";	//"Underline"
RTE_DefaultConfig.text_strike = "Csapásvonal";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "felső index";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Aláírás";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Nagybetű";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Kisbetű";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Formátum eltávolítása";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Hivatkozás beszúrása";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Hivatkozás megnyitása";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Hivatkozás szerkesztése";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Hivatkozás eltávolítása";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Vonal magassága";	//"Line Height"
RTE_DefaultConfig.text_indent = "francia bekezdés";	//"Indent"
RTE_DefaultConfig.text_outdent = "Kihúzás";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Idézőjelek blokkolása";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Rendezett lista";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Rendezetlen lista";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Vízszintes szabály beszúrása";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Dátum beszúrása";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Táblázat beszúrása";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Kép beszúrása";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Videó beszúrása";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Kód beszúrása";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF létrehozása";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Emoji beszúrása";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Különleges karakterek";	//"Special characters"
RTE_DefaultConfig.text_characters = "karakterek";	//"Characters"
RTE_DefaultConfig.text_fontname = "betűtípus";	//"Font"
RTE_DefaultConfig.text_fontsize = "méret";	//"Size"
RTE_DefaultConfig.text_forecolor = "Szöveg színe";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Hátsó szín";	//"Back Color"
RTE_DefaultConfig.text_justify = "igazolja";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Balra igazítás";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Sorkizárás jobbra";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Középre igazítás";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Teljes sorkizárás";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Sorkizárás: Nincs";	//"Justify None"
RTE_DefaultConfig.text_delete = "töröl";	//"Delete"
RTE_DefaultConfig.text_save = "Fájl mentése";	//"Save file"
RTE_DefaultConfig.text_selectall = "Az összes kijelölése";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-kód";	//"HTML Code"
RTE_DefaultConfig.text_preview = "előnézet";	//"Preview"
RTE_DefaultConfig.text_print = "nyomtatási";	//"Print"
RTE_DefaultConfig.text_undo = "visszavonás";	//"Undo"
RTE_DefaultConfig.text_redo = "átalakítani";	//"Redo"
RTE_DefaultConfig.text_more = "Több...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Új dokumentum";	//"New Doc"
RTE_DefaultConfig.text_help = "segítsen";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Igazítás az ablakhoz";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Kilépés a teljes képernyőről";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Képszerkesztő";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Képstílusok";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Szövegközi stílusok";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Bekezdésstílusok";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Stílusok csatolása";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "stílusok";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css osztályok";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url szerint";	//"By Url"
RTE_DefaultConfig.text_upload = "feltölt";	//"Upload"
RTE_DefaultConfig.text_size = "méret";	//"Size"
RTE_DefaultConfig.text_text = "szöveg";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Megnyitás az új lapon";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "beszúrása";	//"Insert"
RTE_DefaultConfig.text_update = "frissítés";	//"Update"
RTE_DefaultConfig.text_find = "Keresés és csere";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "talál";	//"Find"
RTE_DefaultConfig.text_replacewith = "cserélje ki";	//"Replace"
RTE_DefaultConfig.text_findnext = "következő";	//"Next"
RTE_DefaultConfig.text_replaceonce = "cserélje ki";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Az összes cseréje";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Eset egyeztetése";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Word egyeztetése";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Mozgás lefelé";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Felfelé";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Automatikus méret";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100%-os szélesség";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75%-os szélesség";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50%-os szélesség";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25%-os szélesség";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Méret beállítása";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Helyettesítő szöveg";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "igazolja";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Képaláírás";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Cellák egyesítése";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Cellák felosztása függőlegesen";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Cellák felosztása vízszintesen";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Cellaszöveg színe";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Cella vissza színe";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Sor beszúrása felett";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Sor beszúrása alá";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Oszlop beszúrása balra";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Oszlop beszúrása jobbra";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Oszlop törlése";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Sor törlése";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Tábla törlése";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Automatikus méret";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Táblázat fejléce";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Új bekezdés hozzáadása";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "beillesztés";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "beillesztés";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Szöveg beillesztése";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Beillesztés html formátumban";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Word beillesztése";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "A TARTALOM BEILLESZTéséhez használja a CTRL+V billentyűkombinációt. \r\nA tartalom automatikusan törlődik.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "bekezdések";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "bekezdések";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Felfelé";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Mozgás lefelé";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "ismétlődő";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "töröl";	//"Delete"
RTE_DefaultConfig.text_pmore = "Több..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Több..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Szegély be- és be- és bekapcsolása";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "vágott";	//"Cut"
RTE_DefaultConfig.text_copy = "másol";	//"Copy"
RTE_DefaultConfig.text_copied = "másolt";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Gyűjtemény beszúrása";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Dokumentum beszúrása";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Sablon beszúrása";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "előnézet";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normál";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobil";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tabletta";	//"Tablet"
RTE_DefaultConfig.text_table = "táblázat";	//"Table"
RTE_DefaultConfig.text_tablecell = "Táblázatcella";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Táblázat sora";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Táblázat oszlopa";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatikus";	//"Automatic"
RTE_DefaultConfig.text_colormore = "több";	//"More"
RTE_DefaultConfig.text_colorpicker = "Színválasztó";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Webpaletta";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Elnevezett színek";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "alapvető";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "ezen kívül";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Húzás";	//"Drag and drop"
RTE_DefaultConfig.text_or = "vagy";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Kattintson a feltöltéshez";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Alapértelmezett képfelirat";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "keresés";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "A hozzáadni hozandó szöveg elérte a mező karakterkorlátját.";	//"The text to be added has reached the character limit for this field."
