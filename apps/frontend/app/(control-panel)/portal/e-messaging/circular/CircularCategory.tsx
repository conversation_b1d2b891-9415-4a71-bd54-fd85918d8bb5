import {
  circularCategories,
  CircularCategory as CircularCategoryType,
} from '@/app/(control-panel)/portal/e-messaging/circular/circular-data';
import { Chip, darken, lighten } from '@mui/material';

type CircularCategoryProps = {
  slug: CircularCategoryType['slug'];
};

function CircularCategory({ slug }: CircularCategoryProps) {
  const category = circularCategories.find((category) => category.slug === slug);

  if (!category) {
    return null;
  }

  return (
    <Chip
      className="font-semibold text-md"
      label={category.title}
      sx={(theme) => ({
        color: lighten(category?.color, 0.8),
        backgroundColor: darken(category?.color, 0.1),
        ...theme.applyStyles('light', {
          color: darken(category?.color, 0.4),
          backgroundColor: lighten(category?.color, 0.8),
        }),
      })}
      size="small"
    />
  );
}

export default CircularCategory;
