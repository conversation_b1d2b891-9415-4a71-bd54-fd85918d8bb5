import AppBar from '@mui/material/AppBar';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { useState, useRef } from 'react';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import { Alert, AlertTitle, Box, List, ListItem, ListItemText, CircularProgress } from '@mui/material';
import { uploadToS3 } from '@/app/lib/s3Client';
import { queryApiService } from '@/services/api/queryService';
import { useSnackbar } from 'notistack';
import { X } from 'lucide-react';

type QueryReplyDialogProps = {
  open: boolean;
  onClose: () => void;
  queryId: string;
  onSuccess?: () => void;
};

function QueryReplyDialog(props: QueryReplyDialogProps) {
  const { open, onClose, queryId, onSuccess } = props;
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const editorRef = useRef<RTEEditorRef>(null);
  const { enqueueSnackbar } = useSnackbar();

  const handleEditorChange = (content: string) => {
    setMessage(content);
  };

  const handleClose = () => {
    // Reset form when closing without submitting
    setMessage('');
    setFiles([]);
    setUploadErrors([]);
    if (editorRef.current) {
      editorRef.current.setContent('');
    }
    onClose();
  };

  const handleFileSelect = (fileList: FileList) => {
    const newFiles = Array.from(fileList);
    const validFiles: File[] = [];
    const errors: string[] = [];

    newFiles.forEach((file) => {
      // Check file size (2MB limit)
      if (file.size > 2097152) {
        errors.push(`${file.name}: File size exceeds 2MB limit`);
        return;
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: File type not supported`);
        return;
      }

      validFiles.push(file);
    });

    setFiles((prev) => [...prev, ...validFiles]);
    setUploadErrors(errors);
  };

  const handleFileRemove = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (!message.trim()) {
      enqueueSnackbar('Please enter a reply message', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    try {
      let attachment = undefined;

      // Upload file if one is selected (currently supporting single attachment)
      // Note: API expects single attachment, but we're prepared for multiple in the future
      if (files.length > 0) {
        const uploadedFile = await uploadToS3(files[0], 'attachments');
        attachment = {
          name: uploadedFile.fileName,
          url: uploadedFile.url,
          type: uploadedFile.type,
        };
        
        // TODO: When API supports multiple attachments, we can upload all files:
        // const uploadPromises = files.map(file => uploadToS3(file, 'attachments'));
        // const uploadedFiles = await Promise.all(uploadPromises);
        // attachments = uploadedFiles.map(file => ({ name: file.fileName, url: file.url, type: file.type }));
      }

      // Submit reply
      await queryApiService.replyToQuery({
        comment: message,
        attachment,
        queryId,
      });

      enqueueSnackbar('Reply sent successfully!', { variant: 'success' });
      
      // Reset form
      setMessage('');
      setFiles([]);
      setUploadErrors([]);
      if (editorRef.current) {
        editorRef.current.setContent('');
      }
      onClose();
      
      // Call success callback to refresh data
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Failed to send reply:', error);
      enqueueSnackbar(error.message || 'Failed to send reply', { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => {}} // Disable click away completely
      disableEscapeKeyDown // Disable escape key completely
      aria-labelledby="form-dialog-title"
      scroll="body"
      maxWidth="md"
      fullWidth
    >
      <AppBar position="static" color="primary" elevation={0}>
        <Toolbar className="flex w-full">
          <Typography variant="subtitle1" color="inherit">
            Reply to Query
          </Typography>
        </Toolbar>
      </AppBar>

      <DialogContent classes={{ root: 'p-4 pb-0 sm:p-8 sm:pb-0' }}>
        <div className="mt-2 mb-4">
          <Typography variant="subtitle2" className="mb-2">
            Reply Message *
          </Typography>
          <RTEEditor 
            ref={editorRef} 
            onChange={handleEditorChange} 
            value={message}
          />
        </div>

        <div className="mt-2 mb-4">
          <Typography variant="subtitle2" className="mb-2">
            Attachments (Optional)
          </Typography>
          
          <input
            type="file"
            multiple
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            style={{ display: 'none' }}
            id="reply-file-upload"
            accept="image/jpeg,image/jpg,image/png,image/svg+xml,application/pdf"
            disabled={isSubmitting}
          />
          <label htmlFor="reply-file-upload">
            <Box
              sx={{
                border: '2px dashed',
                borderColor: isSubmitting ? 'action.disabled' : 'primary.main',
                borderRadius: 1,
                p: 2,
                textAlign: 'center',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.6 : 1,
                '&:hover': !isSubmitting ? {
                  backgroundColor: 'action.hover',
                } : {},
              }}
            >
              <Typography>Drop files here or click to upload</Typography>
              <Typography variant="caption" color="text.secondary">
                Supported: JPG, PNG, SVG, PDF (max 2MB each) • Currently supports 1 attachment per reply
              </Typography>
            </Box>
          </label>

          {files.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" className="mb-1">
                Selected Files:
              </Typography>
              <List dense>
                {files.map((file, index) => (
                  <ListItem
                    key={index}
                    secondaryAction={
                      <IconButton 
                        edge="end" 
                        onClick={() => handleFileRemove(index)} 
                        size="small"
                        disabled={isSubmitting}
                      >
                        <X size={18} />
                      </IconButton>
                    }
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemText
                      primary={file.name}
                      secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB • ${file.type}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {uploadErrors.length > 0 && (
            <Alert severity="error" className="mt-2">
              <AlertTitle>File Upload Error</AlertTitle>
              <ul className="ml-4 list-disc">
                {uploadErrors.map((error, index) => (
                  <li key={index} className="first-letter:capitalize">
                    {error}
                  </li>
                ))}
              </ul>
            </Alert>
          )}
        </div>
      </DialogContent>

      <DialogActions className="flex flex-col sm:flex-row sm:items-center justify-between py-4 sm:py-6 px-6">
        <div className="-mx-2">
          {/* <IconButton>
            <FuseSvgIcon size={20}>heroicons-solid:paper-clip</FuseSvgIcon>
          </IconButton> */}
        </div>

        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleSubmit}
            disabled={!message.trim() || isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={16} color="inherit" /> : undefined}
          >
            {isSubmitting ? 'Sending...' : 'Send Reply'}
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
}

export default QueryReplyDialog;
