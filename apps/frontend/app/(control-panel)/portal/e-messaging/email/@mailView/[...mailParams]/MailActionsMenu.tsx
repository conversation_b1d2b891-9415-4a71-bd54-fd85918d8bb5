import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { MouseEvent, useMemo, useState } from 'react';
import _ from 'lodash';
import useNavigate from '@fuse/hooks/useNavigate';
import useGetMail from '../../hooks/useGetEmail';
import { mockFolders } from '../../services/emailData';

type MailActionsMenuProps = {
  className?: string;
};

/**
 * The mail actions menu.
 */
function MailActionsMenu(props: MailActionsMenuProps) {
  const { className } = props;
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const { data: mail } = useGetMail();

  const spamFolderId = useMemo(() => _.find(mockFolders, { slug: 'spam' })?.id, []);
  const trashFolderId = useMemo(() => _.find(mockFolders, { slug: 'trash' })?.id, []);

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  if (!mail) {
    return null;
  }

  return (
    <div className={className}>
      <IconButton
        id="basic-button"
        aria-controls="basic-menu"
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        <FuseSvgIcon>heroicons-outline:ellipsis-vertical</FuseSvgIcon>
      </IconButton>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <MenuItem
          onClick={() => {
            console.log('Mark as unread:', mail.id);
            handleClose();
          }}
        >
          <ListItemIcon className="min-w-9">
            <FuseSvgIcon>heroicons-outline:envelope</FuseSvgIcon>
          </ListItemIcon>
          <ListItemText primary="Mark as unread" />
        </MenuItem>

        <MenuItem
          onClick={() => {
            console.log('Move to spam:', mail.id, spamFolderId);
            handleClose();
          }}
        >
          <ListItemIcon className="min-w-9">
            <FuseSvgIcon>heroicons-outline:exclamation-triangle</FuseSvgIcon>
          </ListItemIcon>
          <ListItemText primary="Spam" />
        </MenuItem>

        <MenuItem
          onClick={() => {
            console.log('Move to trash:', mail.id, trashFolderId);
            navigate(-1);
            handleClose();
          }}
        >
          <ListItemIcon className="min-w-9">
            <FuseSvgIcon>heroicons-outline:trash</FuseSvgIcon>
          </ListItemIcon>
          <ListItemText primary="Delete" />
        </MenuItem>
      </Menu>
    </div>
  );
}

export default MailActionsMenu;
