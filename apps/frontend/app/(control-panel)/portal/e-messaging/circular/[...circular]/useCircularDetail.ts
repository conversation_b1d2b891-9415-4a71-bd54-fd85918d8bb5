'use client';

import { useState, useEffect } from 'react';
import { circularApiService } from '@/services/api/circularService';
import { employeeApiService } from '@/services/api/employeeService';
import { CircularResponseDto, CircularStatusEnums } from '@/services/api/types/circular.types';
import { SelfServiceETokenDto, EmployeeResponseDto } from '@/services/api/types/employee.types';

interface UseCircularDetailReturn {
  circular: CircularResponseDto | null;
  isLoading: boolean;
  error: string | null;
  actionError: string | null;
  fromEmployeeSignature: string | null;
  refetch: () => void;
  performCircularAction: (
    action: 'sign' | 'reject',
    token: string,
    password: string,
  ) => Promise<boolean>;
  isPerformingAction: boolean;
  clearActionError: () => void;
}

/**
 * Hook for fetching and managing circular detail data
 * @param circularId - The ID of the circular to fetch
 * @returns Object containing circular data, loading state, error, and refetch function
 */
export function useCircularDetail(circularId: string): UseCircularDetailReturn {
  const [circular, setCircular] = useState<CircularResponseDto | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);
  const [isPerformingAction, setIsPerformingAction] = useState<boolean>(false);
  const [fromEmployeeSignature, setFromEmployeeSignature] = useState<string | null>(null);

  /**
   * Perform circular action (sign/reject) with token validation
   * @param action - The action to perform ('sign' publishes the circular, 'reject' future implementation)
   * @param token - The eToken to validate
   * @param password - The password for validation
   * @returns Promise<boolean> - true if successful, false if failed
   */
  const performCircularAction = async (
    action: 'sign' | 'reject',
    token: string,
    password: string,
  ): Promise<boolean> => {
    if (!circularId) {
      setError('Circular ID is required');
      return false;
    }

    try {
      setIsPerformingAction(true);
      setActionError(null);

      // Step 1: Validate token
      const tokenData: SelfServiceETokenDto = {
        token: token.trim(),
        passwordConfirmation: password.trim(),
      };

      const tokenValidationResponse = await employeeApiService.validateToken(tokenData);

      // Handle the response properly - API returns {message: "false"} for invalid tokens
      let isTokenValid = false;

      if (tokenValidationResponse === true) {
        isTokenValid = true;
      } else if (typeof tokenValidationResponse === 'object' && tokenValidationResponse) {
        // Check if the response indicates success (not "false")
        isTokenValid = (tokenValidationResponse as any)?.message !== 'false';
      }

      if (!isTokenValid) {
        setActionError('Invalid token or password. Please check your credentials and try again.');
        return false;
      }

      // Step 2: Update circular status based on action
      let newStatus: CircularStatusEnums;
      if (action === 'sign') {
        newStatus = CircularStatusEnums.PUBLISH;
      } else if (action === 'reject') {
        // TODO: Implement reject functionality when backend support is added
        // For now, this is a placeholder for future implementation
        throw new Error('Reject action not yet implemented in the backend');
      } else {
        throw new Error('Invalid action');
      }

      await circularApiService.updateCircular(circularId, { status: newStatus });

      // Silently refetch the circular after successful update without showing loading state
      try {
        const freshCircular = await circularApiService.getCircular(circularId);
        setCircular(freshCircular);
        
        // Fetch fromEmployee signature if circular is now published
        if (freshCircular?.status === CircularStatusEnums.PUBLISH && freshCircular?.fromEmployee?.userId) {
          await fetchFromEmployeeSignature(freshCircular.fromEmployee.userId);
        } else {
          setFromEmployeeSignature(null);
        }
      } catch (err) {
        // Keep current circular if refetch fails
      }

      return true;
    } catch (err: any) {
      console.error(`Failed to ${action} circular:`, err);

      // Handle specific error cases
      if (err.response?.status === 401 || err.response?.status === 400) {
        setActionError('Invalid credentials. Please check your token and password.');
      } else if (err.response?.status === 403) {
        setActionError('You are not authorized to perform this action.');
      } else if (err.response?.status === 404) {
        setActionError('Circular not found.');
      } else {
        setActionError(err.message || `Failed to ${action} circular. Please try again.`);
      }

      return false;
    } finally {
      setIsPerformingAction(false);
    }
  };

  /**
   * Fetch fromEmployee signature for published circulars
   */
  const fetchFromEmployeeSignature = async (fromEmployeeUserId: string) => {
    try {
      const employeeData = await employeeApiService.getEmployeeById(fromEmployeeUserId);
      setFromEmployeeSignature(employeeData.eSignature || null);
    } catch (err: any) {
      console.error('Failed to fetch fromEmployee signature:', err);
      setFromEmployeeSignature(null);
    }
  };

  const fetchCircular = async () => {
    if (!circularId) {
      setError('Circular ID is required');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const circularData = await circularApiService.getCircular(circularId);
      setCircular(circularData);

      // Fetch fromEmployee signature if circular is published
      if (circularData?.status === CircularStatusEnums.PUBLISH && circularData?.fromEmployee?.userId) {
        await fetchFromEmployeeSignature(circularData.fromEmployee.userId);
      } else {
        setFromEmployeeSignature(null);
      }
    } catch (err: any) {
      console.error('Failed to fetch circular:', err);
      setError(err.message || 'Failed to load circular');
      setCircular(null);
      setFromEmployeeSignature(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refetch = () => {
    fetchCircular();
  };

  const clearActionError = () => {
    setActionError(null);
  };

  useEffect(() => {
    fetchCircular();
  }, [circularId]);

  return {
    circular,
    isLoading,
    error,
    actionError,
    fromEmployeeSignature,
    refetch,
    performCircularAction,
    isPerformingAction,
    clearActionError,
  };
}