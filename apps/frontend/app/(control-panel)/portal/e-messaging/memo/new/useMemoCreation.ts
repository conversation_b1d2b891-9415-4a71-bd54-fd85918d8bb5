'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { useAuth } from '@/contexts/AuthContext';
import { memoApiService } from '@/services/api/memoService';
import { CreateMemoDto, AttachmentDto, MemoResponseDto } from '@/services/api/types/memo.types';
import { uploadToS3, deleteFromS3, getS3KeyFromUrl } from '@/app/lib/s3Client';
import { v4 as uuidv4 } from 'uuid';

/**
 * Interface for memo form data passed to the createMemo function.
 * Attachments are now handled internally by the hook.
 */
export interface CreateMemoHookParams {
  title: string;
  body: string;
  reference: string;
  recipientIds: string[]; // Renamed from toEmployees for clarity with NewMemo.tsx
  throughIds?: string[]; // Renamed from throughEmployees
  fromId: string; // Renamed from fromEmployeeId
}

/**
 * Interface for individual file wrapper used by the hook.
 */
export interface FileWrapper {
  id: string; // Unique identifier for the file in the list
  file: File;
  preview: string; // Data URL for image previews
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string; // S3 URL after successful upload
  s3Key?: string; // S3 key after successful upload
  error?: string; // Error message if upload fails or validation fails
}

/**
 * Interface for pre-upload validation errors.
 */
export interface AttachmentValidationError {
  fileName: string;
  message: string;
}

/**
 * Interface for the hook's return value.
 */
export interface UseMemoCreationReturn {
  isSubmitting: boolean; // Renamed from isCreating for consistency with NewMemo.tsx
  uploadedFiles: FileWrapper[]; // Renamed from attachmentProgress
  attachmentValidationErrors: AttachmentValidationError[]; // New state for pre-upload errors
  submissionError: string | null; // Renamed from error
  createMemo: (params: CreateMemoHookParams) => Promise<MemoResponseDto | null>; // Updated params
  handleFileChange: (acceptedFiles: File[], fileRejections?: any[]) => void; // New function
  removeFile: (fileId: string) => Promise<void>; // New function, potentially async if involves S3 delete
  cleanupUploads: () => Promise<void>; // To clear successfully uploaded files from S3 if memo creation fails
  reset: () => void;
}

const MAX_FILES = 5;
const MAX_SIZE_BYTES = 2 * 1024 * 1024; // 2MB
const ALLOWED_TYPES = {
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
  'image/svg+xml': ['.svg'],
  'application/pdf': ['.pdf'],
};

/**
 * Custom hook for handling memo creation with file uploads.
 */
export function useMemoCreation(): UseMemoCreationReturn {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { employeeDetails } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileWrapper[]>([]);
  const [attachmentValidationErrors, setAttachmentValidationErrors] = useState<
    AttachmentValidationError[]
  >([]);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  /**
   * Handles file selection from dropzone
   */
  const handleFileChange = useCallback(
    (acceptedFiles: File[], fileRejections: any[] = []) => {
      setAttachmentValidationErrors([]); // Clear previous validation errors
      const newValidationErrors: AttachmentValidationError[] = [];

      // Handle rejected files from Dropzone first
      fileRejections.forEach((rejection) => {
        rejection.errors.forEach((error: any) => {
          newValidationErrors.push({
            fileName: rejection.file.name,
            message: error.message,
          });
        });
      });

      // Check if adding these files would exceed MAX_FILES
      const currentTotalFiles = uploadedFiles.length + acceptedFiles.length;
      if (currentTotalFiles > MAX_FILES) {
        newValidationErrors.push({
          fileName: 'General',
          message: `Cannot upload more than ${MAX_FILES} files. Please remove some files first.`,
        });
        setAttachmentValidationErrors(newValidationErrors);
        return;
      }

      const newFilesToProcess: FileWrapper[] = [];

      acceptedFiles.forEach((file) => {
        // Validate file size
        if (file.size > MAX_SIZE_BYTES) {
          newValidationErrors.push({
            fileName: file.name,
            message: `File is too large (max ${MAX_SIZE_BYTES / 1024 / 1024}MB).`,
          });
          return;
        }

        // Validate file type
        const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        const isValidType = Object.values(ALLOWED_TYPES).some((extensions) =>
          extensions.includes(fileExtension),
        );

        if (!isValidType) {
          const allowedExtensions = Object.values(ALLOWED_TYPES).flat().join(', ');
          newValidationErrors.push({
            fileName: file.name,
            message: `File type not allowed. Allowed types: ${allowedExtensions}`,
          });
          return;
        }

        // Create preview URL
        const preview = file.type.startsWith('image/') ? URL.createObjectURL(file) : '';

        // Create FileWrapper object
        const fileWrapper: FileWrapper = {
          id: uuidv4(),
          file,
          preview,
          progress: 0,
          status: 'pending',
        };

        newFilesToProcess.push(fileWrapper);
      });

      // Update states
      setAttachmentValidationErrors(newValidationErrors);
      setUploadedFiles((prev) => [...prev, ...newFilesToProcess]);
    },
    [uploadedFiles.length],
  );

  /**
   * Removes a file from the uploaded files list
   */
  const removeFile = useCallback(
    async (fileId: string) => {
      const fileToRemove = uploadedFiles.find((f) => f.id === fileId);
      if (!fileToRemove) return;

      // If file was uploaded to S3, delete it
      if (fileToRemove.s3Key) {
        try {
          await deleteFromS3(fileToRemove.s3Key);
        } catch (error) {
          console.error('Failed to delete file from S3:', error);
          // Don't throw here - we still want to remove from UI
        }
      }

      // Revoke preview URL to free memory
      if (fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

      // Remove from state
      setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    },
    [uploadedFiles],
  );

  /**
   * Uploads pending files to S3 and returns attachment DTOs
   */
  const uploadAndPrepareAttachments = useCallback(async (): Promise<AttachmentDto[]> => {
    const attachmentDtos: AttachmentDto[] = [];
    const filesToUpload = uploadedFiles.filter((f) => f.status === 'pending');

    for (const fileWrapper of filesToUpload) {
      // Update status to uploading
      setUploadedFiles((prev) =>
        prev.map((f) =>
          f.id === fileWrapper.id ? { ...f, status: 'uploading' as const, progress: 0 } : f,
        ),
      );

      try {
        // Upload to S3
        const uploadResult = await uploadToS3(fileWrapper.file, 'attachments');

        // Update file status to completed
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === fileWrapper.id
              ? {
                  ...f,
                  status: 'completed' as const,
                  progress: 100,
                  url: uploadResult.url,
                  s3Key: uploadResult.key,
                }
              : f,
          ),
        );

        // Create attachment DTO
        attachmentDtos.push({
          name: fileWrapper.file.name,
          url: uploadResult.url,
          type: fileWrapper.file.type,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';

        // Update file status to error
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === fileWrapper.id ? { ...f, status: 'error' as const, error: errorMessage } : f,
          ),
        );

        throw new Error(`Failed to upload ${fileWrapper.file.name}: ${errorMessage}`);
      }
    }

    return attachmentDtos;
  }, [uploadedFiles]);

  /**
   * Cleans up uploaded files from S3
   */
  const cleanupUploads = useCallback(async () => {
    const filesToCleanup = uploadedFiles.filter((f) => f.s3Key && f.status === 'completed');

    for (const file of filesToCleanup) {
      try {
        await deleteFromS3(file.s3Key!);
      } catch (error) {
        console.error(`Failed to cleanup attachment ${file.file.name}:`, error);
        // Don't throw here - we don't want cleanup failures to affect the user experience
      }
    }
  }, [uploadedFiles]);

  /**
   * Creates a memo with the provided form data
   */
  const createMemo = useCallback(
    async (params: CreateMemoHookParams): Promise<MemoResponseDto | null> => {
      setIsSubmitting(true);
      setSubmissionError(null);

      try {
        // Basic validation (already done by Zod in form, but good for direct API use)
        if (!params.title?.trim()) throw new Error('Memo title is required');
        if (!params.body?.trim()) throw new Error('Memo content is required');
        if (!params.reference?.trim()) throw new Error('Memo reference is required');
        if (!params.recipientIds?.length) throw new Error('At least one recipient is required');
        if (!params.fromId?.trim()) throw new Error('From employee is required');

        // Upload attachments from the uploadedFiles state
        const s3Attachments = await uploadAndPrepareAttachments();

        // Prepare DTO for memo creation
        const memoDto: CreateMemoDto = {
          title: params.title.trim(),
          body: params.body.trim(),
          reference: params.reference.trim(),
          fromEmployeeId: params.fromId,
          toEmployees: params.recipientIds,
          throughEmployees: params.throughIds || [],
          createdBy: employeeDetails?.userId,
          attachments: s3Attachments.length > 0 ? s3Attachments : undefined,
        };

        // Call API to create memo
        const createdMemo = await memoApiService.createMemo(memoDto);

        enqueueSnackbar('Memo created successfully!', {
          variant: 'success',
          autoHideDuration: 3000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        });

        router.push(`/portal/e-messaging/memo/${createdMemo.id}`);
        return createdMemo;
      } catch (err: any) {
        console.error('Memo creation failed:', err);
        const message =
          err.response?.data?.message || err.message || 'An unexpected error occurred.';
        setSubmissionError(message);

        enqueueSnackbar(`Error: ${message}`, {
          variant: 'error',
          autoHideDuration: 5000,
          anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
        });

        // If memo creation fails AFTER attachments were uploaded, cleanup S3
        if (uploadedFiles.some((f) => f.status === 'completed' && f.s3Key)) {
          await cleanupUploads();
        }

        return null;
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      employeeDetails,
      enqueueSnackbar,
      router,
      uploadedFiles,
      uploadAndPrepareAttachments,
      cleanupUploads,
    ],
  );

  /**
   * Resets the hook state to initial values
   */
  const reset = useCallback(() => {
    // Revoke all preview URLs to free memory
    uploadedFiles.forEach((file) => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });

    setIsSubmitting(false);
    setUploadedFiles([]);
    setAttachmentValidationErrors([]);
    setSubmissionError(null);
  }, [uploadedFiles]);

  return {
    isSubmitting,
    uploadedFiles,
    attachmentValidationErrors,
    submissionError,
    createMemo,
    handleFileChange,
    removeFile,
    cleanupUploads,
    reset,
  };
}
