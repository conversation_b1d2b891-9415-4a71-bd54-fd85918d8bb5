import { MigrationInterface, QueryRunner } from 'typeorm';

export class WorkflowType1750060786028 implements MigrationInterface {
  name = 'WorkflowType1750060786028';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "workflow_types" ("created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workflow_type" character varying NOT NULL, CONSTRAINT "PK_272738f7b51a8f18f3b1a0a1280" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "workflow_types"`);
  }
}
