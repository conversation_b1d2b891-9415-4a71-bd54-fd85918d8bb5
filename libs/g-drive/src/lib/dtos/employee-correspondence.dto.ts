import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { CorrespondenceStatus } from '../enums';
import { AttachmentDto, CreateAttachmentDto, QueryOptionsDto } from '@igov/common';
import { EmployeeResponseDto } from '@igov/employee';
import { CorrespondenceResponseDto } from './correspondence.dto';

export class CreateEmployeeCorrespondenceDto {
  @ApiProperty()
  @IsUUID()
  correspondenceId!: string;

  @ApiProperty()
  @IsUUID()
  employeeId!: string;

  @ApiProperty()
  @IsUUID()
  createdBy!: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ required: false, type: CreateAttachmentDto })
  @IsOptional()
  attachment?: CreateAttachmentDto;

  @ApiProperty({ enum: CorrespondenceStatus })
  @IsEnum(CorrespondenceStatus)
  status!: CorrespondenceStatus;
}

export class UpdateEmployeeCorrespondenceDto extends PartialType(CreateEmployeeCorrespondenceDto) {}

export class QueryEmployeeCorrespondenceDto extends QueryOptionsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  correspondenceId?: string;

  @ApiProperty({ required: false, enum: CorrespondenceStatus })
  @IsOptional()
  @IsEnum(CorrespondenceStatus)
  status?: CorrespondenceStatus;
}

export class EmployeeCorrespondenceResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  correspondenceId!: string;

  @ApiProperty()
  employeeId!: string;

  @ApiProperty()
  createdBy!: string;

  @ApiProperty()
  createdDate!: Date;

  @ApiProperty({ required: false })
  notes?: string;

  @ApiProperty({ required: false, type: AttachmentDto })
  attachment?: AttachmentDto;

  @ApiProperty({ enum: CorrespondenceStatus })
  status!: CorrespondenceStatus;

  @ApiProperty({ type: () => CorrespondenceResponseDto })
  correspondence!: CorrespondenceResponseDto;

  @ApiProperty({ type: () => EmployeeResponseDto })
  recipient!: EmployeeResponseDto;

  @ApiProperty({ type: () => EmployeeResponseDto })
  forwarder!: EmployeeResponseDto;
}

export class PaginatedEmployeeCorrespondenceResponseDto {
  @ApiProperty({ type: [EmployeeCorrespondenceResponseDto] })
  data!: EmployeeCorrespondenceResponseDto[];

  @ApiProperty()
  total!: number;
}
