import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>umn,
  JoinT<PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { MemoStatusEnums } from '../enums';
import { BaseEntity } from '@igov/common';
import { IAttachment, IMemo } from '../interfaces';
import { MemoCommentEntity } from './memo-comment.entity';
import { MemoHistoryEntity } from './memo-history.entity';
import { Employee } from '@igov/employee';

@Entity('memos')
export class MemoEntity extends BaseEntity implements IMemo {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  title!: string;

  @Column('text')
  body!: string;

  @Column({ unique: true })
  reference!: string;

  @Column('uuid', { nullable: false })
  createdBy!: string;

  @Column('uuid', { nullable: false })
  fromEmployeeId!: string;

  @Column({ type: 'jsonb', nullable: true })
  attachments?: IAttachment[];

  @Column({ type: 'enum', enum: MemoStatusEnums, default: MemoStatusEnums.Draft })
  status!: MemoStatusEnums;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'created_by' })
  createdByEmployee!: Employee;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'from_employee_id' })
  fromEmployee!: Employee;

  @ManyToMany(() => Employee)
  @JoinTable({ name: 'memo_to_employees' })
  toEmployees!: Employee[];

  @ManyToMany(() => Employee)
  @JoinTable({ name: 'memo_through_employees' })
  throughEmployees!: Employee[];

  @OneToMany(() => MemoCommentEntity, (comment) => comment.memo, {
    eager: false,
    cascade: [],
  })
  comments?: MemoCommentEntity[];

  @OneToMany(() => MemoHistoryEntity, (history) => history.memo, {
    eager: false,
    cascade: [],
  })
  history?: MemoHistoryEntity[];
}
