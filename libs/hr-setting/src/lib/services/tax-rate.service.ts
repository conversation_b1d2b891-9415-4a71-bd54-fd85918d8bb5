import { Injectable, NotFoundException } from '@nestjs/common';
import { TaxRateRepository } from '../repositories';
import { CreateTaxRateDto, UpdateTaxRateDto, TaxRateResponseDto } from '../dtos';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class TaxRateService {
  constructor(private readonly taxRateRepo: TaxRateRepository) {}

  async create(data: CreateTaxRateDto): Promise<TaxRateResponseDto> {
    const created = await this.taxRateRepo.save(data);
    return plainToInstance(TaxRateResponseDto, created);
  }

  async findOne(id: string): Promise<TaxRateResponseDto> {
    const record = await this.taxRateRepo.findOne(id);
    if (!record) throw new NotFoundException('TaxRate not found');
    return plainToInstance(TaxRateResponseDto, record);
  }

  async findAll(): Promise<TaxRateResponseDto[]> {
    const result = await this.taxRateRepo.findAll();
    return result.map((item) => plainToInstance(TaxRateResponseDto, item));
  }

  async update(id: string, data: UpdateTaxRateDto): Promise<TaxRateResponseDto> {
    const existing = await this.findOne(id);
    const updated = await this.taxRateRepo.save({ ...existing, ...data });
    return plainToInstance(TaxRateResponseDto, updated);
  }

  async delete(id: string): Promise<void> {
    await this.findOne(id);
    await this.taxRateRepo.delete(id);
  }
}
