import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import { QueryOptionsDto } from '@igov/common';
import { GDriveFolderResponseDto } from './g-drive-folder.dto';
import { EmployeeResponseDto } from '@igov/employee';

export class CreateRegistryDto {
  @ApiProperty()
  @IsNotEmpty()
  registryName!: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  ownerId!: string;
}

export class UpdateRegistryDto extends PartialType(CreateRegistryDto) {}

export class QueryRegistryDto extends QueryOptionsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  ownerId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  search?: string;
}

export class RegistryResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  registryName!: string;

  @ApiProperty()
  ownerId!: string;

  @ApiProperty()
  folder!: GDriveFolderResponseDto;

  @ApiProperty()
  owner!: EmployeeResponseDto;

  @ApiProperty()
  createdAt?: Date;

  @ApiProperty()
  updatedAt?: Date;
}

export class PaginatedRegistryResponseDto {
  @ApiProperty({ type: [RegistryResponseDto] })
  data!: RegistryResponseDto[];

  @ApiProperty()
  total!: number;
}
