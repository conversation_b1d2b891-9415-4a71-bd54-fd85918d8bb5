import { QueryResponseDto, QueryStatusEnums } from '@/services/api/types/query.types';
import clsx from 'clsx';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import moment from 'moment';
import QueryStatus from './QueryStatus';
import { useState, useEffect } from 'react';

type QueryInfoProps = {
  query: QueryResponseDto;
  className?: string;
};

function QueryInfo({ query, className }: QueryInfoProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!query.dueDate) return;

    const updateCountdown = () => {
      const now = moment();
      const due = moment(query.dueDate);
      const duration = moment.duration(due.diff(now));

      if (duration.asMilliseconds() <= 0) {
        setTimeLeft('Expired');
        return;
      }

      const days = Math.floor(duration.asDays());
      const hours = duration.hours();
      const minutes = duration.minutes();

      if (days > 0) {
        setTimeLeft(`${days}d ${hours}h left`);
      } else if (hours > 0) {
        setTimeLeft(`${hours}h ${minutes}m left`);
      } else {
        setTimeLeft(`${minutes}m left`);
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [query.dueDate]);

  if (!query) {
    return null;
  }

  return (
    <div className={clsx('w-full', className)}>
      <div className="flex items-center justify-between mb-4">
        <QueryStatus status={query.status} />
      </div>

      <Typography className="text-lg font-medium">{query.title}</Typography>

      <Typography className="text-md mt-0.5 line-clamp-2" color="text.secondary">
        {query.reference}
      </Typography>

      <Divider className="w-12 my-6 border-1" />

      <Typography className="flex items-center space-x-1.5 text-md" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Issued By: {query.issuedByEmployee.firstName} {query.issuedByEmployee.lastName}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user-group
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Addressed To: {query.employee.firstName} {query.employee.lastName}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:clock
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Created: {moment(query.createdAt).format('MMMM Do YYYY, h:mm a')}
        </span>
      </Typography>

      {query.dueDate && (
        <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
          <FuseSvgIcon color="disabled" size={20}>
            heroicons-outline:calendar
          </FuseSvgIcon>
          <span className="whitespace-nowrap leading-none">
            <strong>Due Date:</strong> {moment(query.dueDate).format('MMMM Do YYYY, h:mm a')}
            {timeLeft && (
              <span className={`ml-2 ${timeLeft === 'Expired' ? 'text-red-600' : 'text-orange-600'} font-medium`}>
                ({timeLeft})
              </span>
            )}
          </span>
        </Typography>
      )}
    </div>
  );
}

export default QueryInfo; 