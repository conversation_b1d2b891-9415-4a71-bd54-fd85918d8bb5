# Frontend Application

## API Architecture

This application uses a specific API architecture to handle communication between the frontend and backend services, especially when running in Docker containers.

### Key Components

1. **API Client (`apiClient.ts`)**
   - Core service for making HTTP requests to the backend
   - Handles authentication automatically via interceptors
   - Properly routes requests based on environment (browser vs server)

2. **Next.js API Routes (`app/api/[...path]/route.ts`)**
   - Proxy service that forwards browser requests to the backend API
   - Avoids CORS issues by keeping all requests on the same origin
   - Used automatically in browser environments

3. **Auth Service (`authService.ts`)**
   - Handles authentication operations (login, register, logout)
   - Manages user roles and permissions
   - Stores tokens and user data in localStorage

### Request Flow

#### In Browser (Development or Production)
