import { apiClient } from './apiClient';
import {
  CreateCircularDto,
  CircularResponseDto,
  UpdateCircularDto,
  PaginatedCircularResponseDto,
  CircularQueryOptions,
} from './types/circular.types';

/**
 * Service for circular-related API calls
 */
class CircularApiService {
  private baseUrl = '/circular-self-service';

  /**
   * Get outbox circulars for the current user (DRAFT status)
   *
   * @param options - Query options for pagination and filtering
   * @returns Promise with paginated outbox circulars
   */
  async getOutboxCirculars(options?: CircularQueryOptions): Promise<PaginatedCircularResponseDto> {
    try {
      // Build query string from options
      const queryParams = new URLSearchParams();

      if (options?.skip !== undefined) {
        queryParams.append('skip', options.skip.toString());
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.search) {
        queryParams.append('search', options.search);
      }
      if (options?.status) {
        queryParams.append('status', options.status);
      }
      if (options?.fromDate) {
        queryParams.append('fromDate', options.fromDate);
      }
      if (options?.toDate) {
        queryParams.append('toDate', options.toDate);
      }
      if (options?.departmentId) {
        queryParams.append('departmentId', options.departmentId);
      }
      if (options?.from) {
        queryParams.append('from', options.from);
      }

      const queryString = queryParams.toString();
      const url = queryString ? `${this.baseUrl}/outbox?${queryString}` : `${this.baseUrl}/outbox`;

      console.log('=== Fetching outbox circulars with url ===', url);

      return await apiClient.get<PaginatedCircularResponseDto>(url);
    } catch (error: any) {
      console.error('Failed to get outbox circulars:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get inbox circulars for the current user (PUBLISHED status)
   *
   * @param options - Query options for pagination and filtering
   * @returns Promise with paginated inbox circulars
   */
  async getInboxCirculars(options?: CircularQueryOptions): Promise<PaginatedCircularResponseDto> {
    try {
      // Build query string from options
      const queryParams = new URLSearchParams();

      if (options?.skip !== undefined) {
        queryParams.append('skip', options.skip.toString());
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.search) {
        queryParams.append('search', options.search);
      }
      if (options?.status) {
        queryParams.append('status', options.status);
      }
      if (options?.fromDate) {
        queryParams.append('fromDate', options.fromDate);
      }
      if (options?.toDate) {
        queryParams.append('toDate', options.toDate);
      }
      if (options?.departmentId) {
        queryParams.append('departmentId', options.departmentId);
      }
      if (options?.from) {
        queryParams.append('from', options.from);
      }

      const queryString = queryParams.toString();
      const url = queryString ? `${this.baseUrl}/inbox?${queryString}` : `${this.baseUrl}/inbox`;

      return await apiClient.get<PaginatedCircularResponseDto>(url);
    } catch (error: any) {
      console.error('Failed to get inbox circulars:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get sent circulars for the current user (PUBLISHED status)
   *
   * @param options - Query options for pagination and filtering
   * @returns Promise with paginated sent circulars
   */
  async getSentCirculars(options?: CircularQueryOptions): Promise<PaginatedCircularResponseDto> {
    try {
      // Build query string from options
      const queryParams = new URLSearchParams();

      if (options?.skip !== undefined) {
        queryParams.append('skip', options.skip.toString());
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.search) {
        queryParams.append('search', options.search);
      }
      if (options?.status) {
        queryParams.append('status', options.status);
      }
      if (options?.fromDate) {
        queryParams.append('fromDate', options.fromDate);
      }
      if (options?.toDate) {
        queryParams.append('toDate', options.toDate);
      }
      if (options?.departmentId) {
        queryParams.append('departmentId', options.departmentId);
      }
      if (options?.from) {
        queryParams.append('from', options.from);
      }

      const queryString = queryParams.toString();
      const url = queryString ? `${this.baseUrl}/sent?${queryString}` : `${this.baseUrl}/sent`;

      return await apiClient.get<PaginatedCircularResponseDto>(url);
    } catch (error: any) {
      console.error('Failed to get sent circulars:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create a new circular
   *
   * @param data - The circular data to create
   * @returns Promise with the created circular data
   */
  async createCircular(data: CreateCircularDto): Promise<CircularResponseDto> {
    if (!data.title?.trim()) {
      throw new Error('Circular title is required');
    }
    if (!data.body?.trim()) {
      throw new Error('Circular content is required');
    }
    if (!data.reference?.trim()) {
      throw new Error('Circular reference is required');
    }
    if (!data.circularDepartment?.length) {
      throw new Error('At least one department is required');
    }

    try {
      return await apiClient.post<CircularResponseDto>(`${this.baseUrl}`, data);
    } catch (error: any) {
      console.error('Failed to create circular:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get a single circular by ID
   *
   * @param id - The ID of the circular to retrieve
   * @returns Promise with the circular data
   */
  async getCircular(id: string): Promise<CircularResponseDto> {
    if (!id) {
      throw new Error('Circular ID is required');
    }

    try {
      return await apiClient.get<CircularResponseDto>(`${this.baseUrl}/${id}`);
    } catch (error: any) {
      console.error(`Failed to get circular ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Update an existing circular by ID
   *
   * @param id - The ID of the circular to update
   * @param data - The circular data to update (fields are optional)
   * @returns Promise with the updated circular data
   */
  async updateCircular(id: string, data: UpdateCircularDto): Promise<CircularResponseDto> {
    if (!id) {
      throw new Error('Circular ID is required for update');
    }
    if (Object.keys(data).length === 0) {
      throw new Error('Update data cannot be empty');
    }

    try {
      return await apiClient.patch<CircularResponseDto>(`${this.baseUrl}/${id}`, data);
    } catch (error: any) {
      console.error(`Failed to update circular ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }
}

// Export a single instance for easy import
export const circularApiService = new CircularApiService();