{"name": "@igov/source", "version": "0.0.0", "license": "MIT", "scripts": {"format": "nx format", "build": "yarn build:api && yarn build:consumer && yarn build:websocket", "build:api": "nx run api:build", "build:consumer": "nx run consumer:build", "build:websocket": "nx run websocket:build", "docker:up": "docker compose up -d", "docker:up-services": "docker compose up -d postgres localstack mongodb rabbitmq api", "docker:up-infra": "docker compose up -d postgres localstack mongodb rabbitmq", "start-api": "nx serve api", "start-api-dev": "nx serve api --inspect  --host=0.0.0.0  --port=9229", "start-consumer-dev": "nx serve consumer --inspect  --host=0.0.0.0  --port=9227", "start-websocket-dev": "nx serve websocket --inspect  --host=0.0.0.0  --port=9228", "start-frontend-dev": "nx serve frontend --watch", "start-frontend-dev:https": "nx serve frontend --watch --experimental-https", "start:all": "npm-run-all -p start-api-dev start-websocket-dev start-frontend-dev:https", "typeorm": "ts-node --project ./tsconfig.base.json -r tsconfig-paths/register tools/typeorm.custom-cli.ts", "migration:generate": "tsx ./tools/typeorm-migrations/typeorm-migrations-generate.ts", "migration:revert": "npm run typeorm -- migration:revert --dataSource ./tools/typeorm-migrations/data-source.ts", "copy-pspdfkit-assets": "mkdir -p apps/frontend/public/pspdfkit-lib && cp -R node_modules/pspdfkit/dist/pspdfkit-lib/* apps/frontend/public/pspdfkit-lib"}, "private": true, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@hookform/devtools": "4.3.1", "@nestjs/schematics": "11.0.5", "@nestjs/testing": "^10.0.2", "@nx/eslint": "21.2.3", "@nx/eslint-plugin": "21.2.3", "@nx/jest": "21.2.3", "@nx/js": "21.2.3", "@nx/nest": "21.2.3", "@nx/next": "21.2.3", "@nx/node": "21.2.3", "@nx/rollup": "21.2.3", "@nx/web": "21.2.3", "@nx/webpack": "21.2.3", "@nx/workspace": "21.2.3", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/typography": "0.5.15", "@types/autosuggest-highlight": "3.2.3", "@types/bcrypt": "^5.0.2", "@types/draft-js": "0.11.18", "@types/draftjs-to-html": "0.8.4", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/lodash": "4.17.4", "@types/node": "~18.16.9", "@types/passport-jwt": "^4.0.1", "@types/prismjs": "1.26.5", "@types/qs": "6.9.17", "@types/react": "19.0.0", "@types/react-autosuggest": "10.1.11", "@types/react-dom": "19.0.0", "@types/react-draft-wysiwyg": "1.13.8", "@types/react-redux": "7.1.34", "@types/styled-components": "5.1.34", "@typescript-eslint/eslint-plugin": "8.36.0", "autoprefixer": "10.4.13", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.16", "eslint-plugin-unused-imports": "4.1.4", "immutable": "4.3.6", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "npm-run-all": "^4.1.5", "nx": "21.2.3", "postcss": "8.4.38", "prettier": "^3.5.3", "promise": "8.3.0", "raw-loader": "^4.0.2", "rollup": "^4.14.0", "swc-loader": "0.1.15", "tailwindcss": "4.0.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "5.8.3", "typescript-eslint": "8.36.0", "typescript-plugin-css-modules": "5.1.0", "webpack-cli": "^5.1.4"}, "peerDependencies": {"autoprefixer": "10.4.20", "postcss": "8.4.49", "react": "19.0.0", "react-dom": "19.0.0"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "redux": "^5.0.1", "semver": "7.5.4"}, "dependencies": {"@auth/unstorage-adapter": "2.7.4", "@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/client-ses": "^3.799.0", "@aws-sdk/lib-storage": "^3.828.0", "@emotion/cache": "11.13.5", "@emotion/react": "11.13.5", "@emotion/styled": "11.13.5", "@hookform/resolvers": "3.6.0", "@mui/base": "5.0.0-beta.64", "@mui/icons-material": "6.1.10", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "6.1.10", "@mui/material-nextjs": "6.1.9", "@mui/styles": "6.1.10", "@mui/system": "6.1.10", "@mui/x-data-grid": "^8.1.0", "@mui/x-date-pickers": "7.23.1", "@nestjs/common": "^10.0.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.2", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/swagger": "^11.1.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@popperjs/core": "2.11.8", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "2.4.0", "@sendgrid/mail": "^8.1.5", "@tailwindcss/postcss": "^4.1.3", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@vercel/kv": "3.0.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.7", "antd": "^5.26.0", "autosuggest-highlight": "3.3.4", "axios": "^1.6.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "clsx": "2.1.1", "core-js": "3.39.0", "date-fns": "4.1.0", "draft-js": "0.11.7", "draftjs-to-html": "0.9.1", "env-var": "^7.5.0", "formik": "^2.4.6", "history": "5.3.0", "i18next": "24.0.5", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "keycode": "2.2.1", "lodash": "4.17.21", "lucide-react": "^0.501.0", "material-react-table": "3.0.1", "mobile-detect": "1.4.5", "moment": "2.30.1", "motion": "12.0.0-alpha.2", "next": "~15.2.4", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.10.1", "notistack": "^3.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "perfect-scrollbar": "1.5.5", "pg": "^8.14.1", "prismjs": "1.29.0", "pspdfkit": "^2024.8.2", "qs": "6.13.1", "react": "19.0.0", "react-app-alias": "2.2.2", "react-autosuggest": "10.1.0", "react-dom": "19.0.0", "react-draft-wysiwyg": "1.15.0", "react-dropzone": "^14.3.8", "react-hook-form": "7.53.2", "react-i18next": "15.1.3", "react-pdf": "^9.2.1", "react-popper": "2.3.0", "react-redux": "9.1.2", "react-swipeable": "7.0.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "styled-components": "6.1.13", "stylis": "4.3.4", "stylis-plugin-rtl": "2.1.1", "tsx": "^4.19.3", "type-fest": "4.30.0", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0", "unstorage": "1.13.1", "uuid": "^11.1.0", "yup": "^1.6.1", "zod": "3.23.8"}}