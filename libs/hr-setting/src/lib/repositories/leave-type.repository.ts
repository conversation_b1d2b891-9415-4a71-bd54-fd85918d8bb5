import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { LeaveType } from '../entities';
import { ILeaveType } from '../interfaces';

@Injectable()
export class LeaveTypeRepository extends BaseRepository<LeaveType> {
  private readonly logger = new Logger(LeaveTypeRepository.name);
  override entityClassName = LeaveType;

  findOne(id: string): Promise<LeaveType | null> {
    return this.repository.findOne({ where: { id } });
  }

  save(leaveType: ILeaveType): Promise<LeaveType> {
    return this.repository.save(leaveType);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  findAll(): Promise<LeaveType[]> {
    return this.repository.find();
  }
}
