import { BaseRepository } from '@igov/common';
import { MemoEntity } from '../entity';
import { Injectable, Logger } from '@nestjs/common';
import { IMemo, IMemoFilters } from '../interfaces';

@Injectable()
export class MemoRepository extends BaseRepository<MemoEntity> {
  private readonly logger = new Logger(MemoRepository.name);
  override entityClassName = MemoEntity;

  async findOne(params: {
    id: string;
    employeeId?: string;
    relations?: string[];
  }): Promise<MemoEntity | null> {
    const { id, employeeId, relations = [] } = params;

    const queryBuilder = this.repository.createQueryBuilder('memos');

    queryBuilder
      .leftJoin('memos.createdByEmployee', 'createdByEmployee')
      .addSelect([
        'createdByEmployee.userId',
        'createdByEmployee.firstName',
        'createdByEmployee.lastName',
      ])
      .leftJoin('memos.fromEmployee', 'fromEmployee')
      .addSelect(['fromEmployee.userId', 'fromEmployee.firstName', 'fromEmployee.lastName']);

    const needsToEmployees = relations.includes('toEmployees') || !!employeeId;
    const needsThroughEmployees = relations.includes('throughEmployees') || !!employeeId;

    if (needsToEmployees) {
      queryBuilder.leftJoin('memos.toEmployees', 'toEmployees');
      if (relations.includes('toEmployees')) {
        queryBuilder.addSelect([
          'toEmployees.userId',
          'toEmployees.firstName',
          'toEmployees.lastName',
        ]);
      }
    }

    if (needsThroughEmployees) {
      queryBuilder.leftJoin('memos.throughEmployees', 'throughEmployees');
      if (relations.includes('throughEmployees')) {
        queryBuilder.addSelect([
          'throughEmployees.userId',
          'throughEmployees.firstName',
          'throughEmployees.lastName',
        ]);
      }
    }

    if (relations.includes('comments')) {
      queryBuilder
        .leftJoinAndSelect('memos.comments', 'comments')
        .leftJoin('comments.commenter', 'commenter')
        .addSelect(['commenter.userId', 'commenter.firstName', 'commenter.lastName']);
    }

    if (relations.includes('history')) {
      queryBuilder.leftJoinAndSelect('memos.history', 'history');
    }

    queryBuilder.where('memos.id = :id', { id });

    if (employeeId) {
      queryBuilder.andWhere(
        `(
        toEmployees.userId = :employeeId OR
        throughEmployees.userId = :employeeId OR
        memos.createdBy = :employeeId OR
        memos.fromEmployeeId = :employeeId
      )`,
        { employeeId },
      );
    }

    return queryBuilder.getOne();
  }

  async findOneMemoByAuthorizedEmployee(params: {
    id: string;
    employeeId: string;
  }): Promise<MemoEntity | null> {
    const { id, employeeId } = params;

    const queryBuilder = this.repository.createQueryBuilder('memos');
    queryBuilder.leftJoin('memos.throughEmployees', 'throughEmployees');
    queryBuilder.addSelect([
      'throughEmployees.userId',
      'throughEmployees.firstName',
      'throughEmployees.lastName',
    ]);
    queryBuilder.where('memos.id = :id', { id });
    queryBuilder.andWhere(
      `(
        throughEmployees.userId = :employeeId OR
        memos.fromEmployeeId = :employeeId
      )`,
      { employeeId },
    );

    return queryBuilder.getOne();
  }

  async findAll(query: IMemoFilters): Promise<{ data: MemoEntity[]; total: number }> {
    const {
      skip,
      limit,
      search,
      status,
      fromEmployeeId,
      toEmployeeIds,
      throughEmployeeIds,
      fromDate,
      toDate,
      createdById,
      useOrConditionForEmployees,
    } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('memos')
      .leftJoin('memos.createdByEmployee', 'createdByEmployee')
      .addSelect([
        'createdByEmployee.userId',
        'createdByEmployee.firstName',
        'createdByEmployee.lastName',
      ])
      .leftJoin('memos.fromEmployee', 'fromEmployee')
      .addSelect(['fromEmployee.userId', 'fromEmployee.firstName', 'fromEmployee.lastName'])
      .leftJoin('memos.toEmployees', 'toEmployees')
      .addSelect(['toEmployees.userId', 'toEmployees.firstName', 'toEmployees.lastName'])
      .leftJoin('memos.throughEmployees', 'throughEmployees')
      .addSelect([
        'throughEmployees.userId',
        'throughEmployees.firstName',
        'throughEmployees.lastName',
      ]);

    // Filters
    if (search) {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere('(memos.title ILIKE :search OR memos.body ILIKE :search)', {
        search: searchTerm,
      });
    }

    if (useOrConditionForEmployees) {
      const orConditions: string[] = [];
      const orParams: Record<string, any> = {};

      if (toEmployeeIds?.length) {
        orConditions.push('toEmployees.userId IN (:...toEmployeeIds)');
        orParams['toEmployeeIds'] = toEmployeeIds;
      }

      if (throughEmployeeIds?.length) {
        orConditions.push('throughEmployees.userId IN (:...throughEmployeeIds)');
        orParams['throughEmployeeIds'] = throughEmployeeIds;
      }

      if (createdById) {
        orConditions.push('memos.createdBy = :createdById');
        orParams['createdById'] = createdById;
      }

      if (fromEmployeeId) {
        orConditions.push('memos.fromEmployeeId = :fromEmployeeId');
        orParams['fromEmployeeId'] = fromEmployeeId;
      }

      if (orConditions.length > 0) {
        queryBuilder.andWhere(`(${orConditions.join(' OR ')})`, orParams);
      }
    } else {
      if (createdById) {
        queryBuilder.andWhere('memos.createdBy = :createdById', { createdById });
      }

      if (fromEmployeeId) {
        queryBuilder.andWhere('memos.fromEmployeeId = :fromEmployeeId', { fromEmployeeId });
      }

      if (toEmployeeIds?.length) {
        queryBuilder.andWhere('toEmployees.userId IN (:...toEmployeeIds)', {
          toEmployeeIds,
        });
      }

      if (throughEmployeeIds?.length) {
        queryBuilder.andWhere('throughEmployees.userId IN (:...throughEmployeeIds)', {
          throughEmployeeIds,
        });
      }
    }

    // Apply status filtering regardless of useOrConditionForEmployees flag
    if (status?.length) {
      queryBuilder.andWhere('memos.status IN (:...status)', { status });
    }

    if (fromDate) {
      queryBuilder.andWhere('memos.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      queryBuilder.andWhere('memos.createdAt <= :toDate', { toDate });
    }

    // Order by creation date (most recent first)
    queryBuilder.orderBy('memos.createdAt', 'DESC');

    const totalCount = await queryBuilder.clone().getCount();
    this.logger.log(`Total memos found: ${totalCount}`);

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const memos = await queryBuilder.getMany();

    return { data: memos, total: totalCount };
  }

  async save(memo: Partial<IMemo>): Promise<MemoEntity> {
    return this.repository.save(memo);
  }

  async findOneById(id: string): Promise<MemoEntity | null> {
    return await this.repository.findOne({ where: { id } });
  }
}
