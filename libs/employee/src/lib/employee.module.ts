import { Module } from '@nestjs/common';
import { EmployeeController } from './controllers/employee/employee.controller';
import { EmployeeService } from './services';
import { EmployeeRepository } from './repositories';
import { DatabaseModule } from '@igov/common';
import { Employee } from './entity';
import { AuthCoreModule } from '@igov/auth';
import { OrgStructureModule } from '@igov/org-structure';
import { EmployeeSelfServiceController } from './controllers/employee/employee-self-service.controller';

@Module({
  imports: [DatabaseModule.forFeature([Employee]), AuthCoreModule, OrgStructureModule],
  controllers: [EmployeeController, EmployeeSelfServiceController],
  providers: [EmployeeRepository, EmployeeService],
  exports: [EmployeeRepository, EmployeeService],
})
export class EmployeeModule {}
