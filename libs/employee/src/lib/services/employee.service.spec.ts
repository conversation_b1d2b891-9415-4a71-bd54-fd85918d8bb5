import { Test, TestingModule } from '@nestjs/testing';
import { EmployeeService } from './employee.service';
import { DataSource } from 'typeorm';
import { EmployeeRepository } from '../repositories';
import { Department, DepartmentRepository, Unit, UnitRepository } from '@igov/org-structure';
import { UserRepository } from '@igov/auth';
import { ConflictException, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { CreateEmployeeDto, QueryEmployeeDto, UpdateEmployeeDto } from '../dtos';
import { Employee } from '../entity';
import { plainToInstance } from 'class-transformer';
import * as bcrypt from 'bcrypt';

const mockEmployeeRepository = () => ({
  findAll: jest.fn(),
  findOne: jest.fn(),
  findOneByEmail: jest.fn(),
  save: jest.fn(),
});

const mockUserRepository = () => ({
  findById: jest.fn(),
});

const mockDepartmentRepository = () => ({
  findOne: jest.fn(),
});

const mockUnitRepository = () => ({
  findOne: jest.fn(),
});

const mockQueryRunner = {
  connect: jest.fn(),
  startTransaction: jest.fn(),
  manager: {
    create: jest.fn(),
    save: jest.fn(),
  },
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn(),
  release: jest.fn(),
};

const mockDataSource = {
  createQueryRunner: jest.fn(() => mockQueryRunner),
};

describe('EmployeeService', () => {
  let service: EmployeeService;
  let employeeRepository: jest.Mocked<EmployeeRepository>;
  let userRepository: jest.Mocked<UserRepository>;
  let departmentRepository: jest.Mocked<DepartmentRepository>;
  let unitRepository: jest.Mocked<UnitRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmployeeService,
        { provide: DataSource, useFactory: () => mockDataSource },
        { provide: EmployeeRepository, useFactory: mockEmployeeRepository },
        { provide: UserRepository, useFactory: mockUserRepository },
        { provide: DepartmentRepository, useFactory: mockDepartmentRepository },
        { provide: UnitRepository, useFactory: mockUnitRepository },
      ],
    }).compile();

    service = module.get<EmployeeService>(EmployeeService);
    employeeRepository = module.get(EmployeeRepository);
    userRepository = module.get(UserRepository);
    departmentRepository = module.get(DepartmentRepository);
    unitRepository = module.get(UnitRepository);
  });

  describe('getAll', () => {
    it('should fetch all employees', async () => {
      const query: QueryEmployeeDto = { skip: 1, limit: 10 };
      const mockEmployees = [{ id: '1' }, { id: '2' }] as unknown as Employee[];
      employeeRepository.findAll.mockResolvedValue({ data: mockEmployees, total: 2 });

      const result = await service.getAll(query);

      expect(employeeRepository.findAll).toHaveBeenCalledWith(query);
      expect(result.total).toBe(2);
      expect(result.employees.length).toBe(2);
    });

    it('should throw InternalServerErrorException if fetching fails', async () => {
      employeeRepository.findAll.mockRejectedValue(new Error('Something went wrong'));

      await expect(service.getAll({ skip: 1, limit: 10 })).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('create', () => {
    const createEmployeeDto: CreateEmployeeDto = {
      email: '<EMAIL>',
      departmentId: 'dept-id',
      unitId: 'unit-id',
    } as any;

    it('should create a new employee with a new user', async () => {
      employeeRepository.findOneByEmail.mockResolvedValue(null);
      departmentRepository.findOne.mockResolvedValue({ id: 'dept-id' } as unknown as Department);
      unitRepository.findOne.mockResolvedValue({ id: 'unit-id' } as unknown as Unit);
      mockQueryRunner.manager.create.mockImplementation((entity, data) => data);
      mockQueryRunner.manager.save.mockResolvedValue({ id: 'emp-id' });

      jest.spyOn(bcrypt, 'hash').mockResolvedValue('hashedpassword' as unknown as never);

      const result = await service.create(createEmployeeDto);

      expect(employeeRepository.findOneByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(departmentRepository.findOne).toHaveBeenCalledWith('dept-id');
      expect(unitRepository.findOne).toHaveBeenCalledWith('unit-id');
      expect(result).toEqual(plainToInstance(Employee, { id: 'emp-id' }));
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it('should throw ConflictException if employee already exists', async () => {
      employeeRepository.findOneByEmail.mockResolvedValue({
        id: 'existing-id',
      } as unknown as Employee);

      await expect(service.create(createEmployeeDto)).rejects.toThrow(ConflictException);
    });

    it('should throw NotFoundException if department does not exist', async () => {
      employeeRepository.findOneByEmail.mockResolvedValue(null);
      departmentRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createEmployeeDto)).rejects.toThrow(NotFoundException);
    });

    it('should rollback transaction if an error occurs', async () => {
      employeeRepository.findOneByEmail.mockResolvedValue(null);
      departmentRepository.findOne.mockResolvedValue({ id: 'dept-id' } as unknown as Department);
      unitRepository.findOne.mockResolvedValue({ id: 'unit-id' } as unknown as Unit);
      mockQueryRunner.manager.create.mockImplementation(() => {
        throw new Error('Creation error');
      });

      await expect(service.create(createEmployeeDto)).rejects.toThrow(InternalServerErrorException);
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    const updateEmployeeDto: UpdateEmployeeDto = {
      departmentId: 'dept-id',
      unitId: 'unit-id',
    } as any;

    it('should update an existing employee', async () => {
      employeeRepository.findOne.mockResolvedValue({ id: 'emp-id' } as unknown as Employee);
      departmentRepository.findOne.mockResolvedValue({ id: 'dept-id' } as unknown as Department);
      unitRepository.findOne.mockResolvedValue({ id: 'unit-id' } as unknown as Unit);
      employeeRepository.save.mockResolvedValue({
        id: 'emp-id',
        userId: 'user-id',
      } as unknown as Employee);

      const result = await service.update('user-id', updateEmployeeDto);

      expect(result).toEqual(plainToInstance(Employee, { id: 'emp-id', userId: 'user-id' }));
    });

    it('should throw NotFoundException if employee does not exist', async () => {
      employeeRepository.findOne.mockResolvedValue(null);

      await expect(service.update('user-id', updateEmployeeDto)).rejects.toThrow(NotFoundException);
    });
  });
});
