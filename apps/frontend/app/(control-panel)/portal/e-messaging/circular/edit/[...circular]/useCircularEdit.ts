import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { circularApiService } from '@/services/api/circularService';
import { UpdateCircularDto, CircularResponseDto } from '@/services/api/types/circular.types';

export interface EditCircularHookParams {
  title: string;
  body: string;
}

export interface UseCircularEditReturn {
  isSubmitting: boolean;
  submissionError: string | null;
  submitCircular: (params: EditCircularHookParams) => Promise<void>;
}

export function useCircularEdit(circularId: string, existingCircular?: CircularResponseDto): UseCircularEditReturn {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const submitCircular = useCallback(
    async (params: EditCircularHookParams) => {
      setIsSubmitting(true);
      setSubmissionError(null);

      try {
        // Prepare circular data - only include fields supported by UpdateCircularDto
        const updateCircularData: UpdateCircularDto = {
          title: params.title,
          body: params.body,
        };

        // Update the circular
        const updatedCircular = await circularApiService.updateCircular(circularId, updateCircularData);

        enqueueSnackbar('Circular updated successfully!', { variant: 'success' });

        // Navigate to the updated circular
        router.push(`/portal/e-messaging/circular/${updatedCircular.id}`);
      } catch (error: any) {
        console.error('Failed to update circular:', error);
        setSubmissionError(error.message || 'Failed to update circular. Please try again.');
        enqueueSnackbar('Failed to update circular', { variant: 'error' });
      } finally {
        setIsSubmitting(false);
      }
    },
    [circularId, enqueueSnackbar, router],
  );

  return {
    isSubmitting,
    submissionError,
    submitCircular,
  };
}