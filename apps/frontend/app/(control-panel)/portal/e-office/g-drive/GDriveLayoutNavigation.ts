import { FuseNavItemType } from '@/@fuse/core/FuseNavigation/types/FuseNavItemType';

/**
 * GDrive Layout Navigation configuration
 * Defines the navigation structure for the left sidebar
 */
const GDriveLayoutNavigation: FuseNavItemType = {
  id: 'gdrive-navigation',
  title: 'GDrive Navigation',
  type: 'collapse',
  children: [
    {
      id: 'my-drive',
      title: 'My Drive',
      type: 'item',
      url: '/portal/e-office/g-drive',
      icon: 'heroicons-outline:folder',
      subtitle: 'Your personal files and folders',
    },
    {
      id: 'shared-with-me',
      title: 'Shared with me',
      type: 'item',
      url: '/portal/e-office/g-drive',
      icon: 'heroicons-outline:share',
      subtitle: 'Files shared by others',
    },
  ],
};

export default GDriveLayoutNavigation;
