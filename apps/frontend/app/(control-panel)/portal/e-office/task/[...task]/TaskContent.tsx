'use client';

import { Task } from '../task-data';
import { Paper, Typography } from '@mui/material';

type TaskContentProps = {
  task: Task;
};

function TaskContent({ task }: TaskContentProps) {
  return (
    <Paper className="w-full mx-auto sm:my-2 lg:mt-4 p-6 sm:p-10 sm:py-12 rounded-xl shadow-sm overflow-hidden">
      <div className="flex flex-col">
        <Typography className="text-3xl font-bold tracking-tight leading-tight">
          {task.subject}
        </Typography>
        <div className="mt-8 flex flex-col gap-4">
          <div className="flex flex-col">
            <Typography className="font-medium text-gray-500">Task Overview</Typography>
            <Typography className="mt-1">{task.overview}</Typography>
          </div>
        </div>
      </div>
    </Paper>
  );
}

export default TaskContent;
