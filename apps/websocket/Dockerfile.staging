# Staging Dockerfile for NestJS Websocket
# Use slim for both builder and final stage to fix native module compatibility
FROM node:20-slim AS builder

# Set up with more memory
WORKDIR /app
COPY package*.json nx.json tsconfig*.json yarn.lock ./

# Install dependencies with substantially increased memory and limited parallelism
# No need for apk add build-base etc. with slim/Debian generally
RUN NODE_OPTIONS="--max_old_space_size=8192" yarn install --frozen-lockfile --network-timeout 600000 --legacy-peer-deps --jobs=1 && \
    yarn cache clean

# Copy and build the project
COPY . .
RUN NODE_OPTIONS="--max_old_space_size=8192" yarn nx build websocket --configuration=staging

# Final production image
FROM node:20-slim
WORKDIR /app

# Copy built artifacts and required files
COPY --from=builder /app/dist/apps/websocket ./
# node_modules copied from builder will now contain glibc-compiled native modules
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3002
ENV NODE_ENV=staging
ENV PORT=3002

CMD ["node", "main.js"]
