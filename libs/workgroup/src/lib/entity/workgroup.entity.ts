import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity, IAttachment } from '@igov/common';
import { IWorkgroup } from '../interfaces';
import { Employee } from '@igov/employee';
import { WorkgroupStatusEnum } from '../enums';
import { WorkgroupDocsEntity } from './workgroup-docs.entity';
import { WorkgroupCommentsEntity } from './workgroup-comments.entity';
import { WorkgroupHistoryEntity } from './workgroup-history.entity';

@Entity('workgroups')
export class WorkgroupEntity extends BaseEntity implements IWorkgroup {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  title!: string;

  @Column('text')
  description!: string;

  @Column()
  dueDate!: Date;

  @Column({ type: 'jsonb', nullable: true })
  executors!: string[];

  @Column()
  amount?: number;

  @Column({ type: 'jsonb', nullable: true })
  attachments?: IAttachment[];

  @Column('uuid', { nullable: false })
  initiatedById!: string;

  @Column({ type: 'enum', enum: WorkgroupStatusEnum, default: WorkgroupStatusEnum.New })
  status!: WorkgroupStatusEnum;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'initiated_by_id' })
  initiatedBy!: Employee;

  @OneToMany(() => WorkgroupDocsEntity, (doc) => doc.workgroup, {
    eager: false,
    cascade: [],
  })
  docs?: WorkgroupDocsEntity[];

  @OneToMany(() => WorkgroupCommentsEntity, (comment) => comment.workgroup, {
    eager: false,
    cascade: [],
  })
  comments?: WorkgroupCommentsEntity[];

  @OneToMany(() => WorkgroupHistoryEntity, (history) => history.workgroup, {
    eager: false,
    cascade: [],
  })
  history?: WorkgroupHistoryEntity[];
}
