import { <PERSON><PERSON><PERSON><PERSON>, IsOptional, Min } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

const DEFAULT_PAGE_SIZE = 10;

export class QueryOptionsDto {
  @ApiPropertyOptional({
    description: 'Number of item to skip.',
    required: false,
    type: Number,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  readonly skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of items to return. Defaults to `10` if not provided.',
    required: false,
    type: Number,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  readonly limit?: number = DEFAULT_PAGE_SIZE;
}
