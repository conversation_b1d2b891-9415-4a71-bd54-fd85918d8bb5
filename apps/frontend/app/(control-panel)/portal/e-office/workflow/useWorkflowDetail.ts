import { useCallback, useEffect, useState } from 'react';
import { workflowApiService } from '@/services/api/workflowService';
import {
  WorkflowResponseDto,
  WorkflowStatusEnum,
  IAttachment,
  WorkflowCommentResponseDto,
  CreateWorkflowCommentDto,
} from '@/services/api/types/workflow.types';
import { showMessage } from '@fuse/core/FuseMessage/fuseMessageSlice';
import { useAppDispatch } from '@/store/hooks';

interface UseWorkflowDetailState {
  workflow: WorkflowResponseDto | null;
  isLoading: boolean;
  error: string | null;
  comments: WorkflowCommentResponseDto[];
  isLoadingComments: boolean;
  isAddingComment: boolean;
}

interface UseWorkflowDetailActions {
  addComment: (params: CreateWorkflowCommentDto) => Promise<boolean>;
  refetch: () => Promise<void>;
}

export function useWorkflowDetail(
  workflowId: string,
): UseWorkflowDetailState & UseWorkflowDetailActions {
  const dispatch = useAppDispatch();

  const [state, setState] = useState<UseWorkflowDetailState>({
    workflow: null,
    isLoading: true,
    error: null,
    comments: [],
    isLoadingComments: false,
    isAddingComment: false,
  });

  const fetchWorkflow = useCallback(async () => {
    if (!workflowId) return;

    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const workflow = await workflowApiService.getWorkflow(workflowId);

      setState((prev) => ({
        ...prev,
        workflow,
        comments: workflow.comments || [], // Extract comments from workflow data
        isLoading: false,
      }));
    } catch (error: any) {
      console.error('Failed to fetch workflow:', error);
      setState((prev) => ({
        ...prev,
        workflow: null,
        isLoading: false,
        error: error.message || 'Failed to fetch workflow',
      }));

      dispatch(
        showMessage({
          message: error.message || 'Failed to fetch workflow',
          variant: 'error',
        }),
      );
    }
  }, [workflowId, dispatch]);

  const fetchComments = useCallback(async () => {
    // Comments are included with workflow data, so this function is no longer needed
    // but kept for backward compatibility with components that might call it
    return;
  }, []);

  // Fetch workflow on mount and when workflowId changes
  useEffect(() => {
    fetchWorkflow();
  }, [fetchWorkflow]);

  const addComment = useCallback(
    async (params: CreateWorkflowCommentDto): Promise<boolean> => {
      if (!params.workflowId || !params.comment?.trim()) {
        return false;
      }

      try {
        setState((prev) => ({ ...prev, isAddingComment: true }));

        await workflowApiService.addComment(params);

        // Refetch workflow to get the updated workflow with new comment
        await fetchWorkflow();

        return true;
      } catch (error: any) {
        console.error('Failed to add comment:', error);
        dispatch(
          showMessage({
            message: error.message || 'Failed to add comment',
            variant: 'error',
          }),
        );
        return false;
      } finally {
        setState((prev) => ({ ...prev, isAddingComment: false }));
      }
    },
    [dispatch, fetchWorkflow],
  );

  const refetch = useCallback(async () => {
    await fetchWorkflow();
  }, [fetchWorkflow]);

  return {
    ...state,
    addComment,
    refetch,
  };
}

// Helper functions for workflow status and permissions
export const getWorkflowStatusColor = (
  status: WorkflowStatusEnum,
): 'success' | 'warning' | 'error' | 'info' => {
  switch (status) {
    case WorkflowStatusEnum.Completed:
      return 'success';
    case WorkflowStatusEnum.Ongoing:
    case WorkflowStatusEnum.New:
      return 'warning';
    case WorkflowStatusEnum.Declined:
      return 'error';
    default:
      return 'info';
  }
};

export const getWorkflowStatusText = (status: WorkflowStatusEnum): string => {
  switch (status) {
    case WorkflowStatusEnum.New:
      return 'New';
    case WorkflowStatusEnum.Ongoing:
      return 'In Progress';
    case WorkflowStatusEnum.Completed:
      return 'Completed';
    case WorkflowStatusEnum.Declined:
      return 'Declined';
    default:
      return 'Unknown';
  }
};

export const canUserAuthorizeWorkflow = (
  workflow: WorkflowResponseDto | null,
  currentUserId: string,
): boolean => {
  if (!workflow || !currentUserId) return false;

  // User can authorize if:
  // 1. Workflow is not completed or declined
  // 2. User is in the actors list with pending status
  // 3. [COMMENTED] It's not their own workflow (they didn't initiate it) - Initiators can also be authorizers

  const isCompleted =
    workflow.status === WorkflowStatusEnum.Completed ||
    workflow.status === WorkflowStatusEnum.Declined;

  if (isCompleted) return false;

  // Commented out initiator check - initiators can be authorizers too
  // const isInitiator = workflow.initiatedBy?.id === currentUserId;
  // if (isInitiator) return false;

  const userActor = workflow.actors?.find((actor) => actor.actor?.userId === currentUserId);
  return userActor?.authStatus === 'Pending';
};

export const canUserEditWorkflow = (
  workflow: WorkflowResponseDto | null,
  currentUserId: string,
): boolean => {
  if (!workflow || !currentUserId) return false;

  // User can edit if:
  // 1. They initiated the workflow
  // 2. Workflow is still new or ongoing (not completed/declined)

  const isInitiator = workflow.initiatedBy?.id === currentUserId;
  const isEditable =
    workflow.status === WorkflowStatusEnum.New || workflow.status === WorkflowStatusEnum.Ongoing;

  return isInitiator && isEditable;
};
