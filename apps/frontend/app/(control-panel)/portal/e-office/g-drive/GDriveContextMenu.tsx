'use client';

import React from 'react';
import { Menu, MenuItem, ListItemIcon, ListItemText, Divider } from '@mui/material';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';

interface GDriveContextMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  isFolder: boolean;
  onDownload?: () => void;
  onShare?: () => void;
  onRename?: () => void;
  onDelete?: () => void;
  onViewDetails?: () => void;
  onOpenFolder?: () => void;
  onRevokeShare?: () => void;
  showRevokeShare?: boolean;
}

/**
 * Context menu for GDrive files and folders
 */
function GDriveContextMenu({
  anchorEl,
  open,
  onClose,
  isFolder,
  onDownload,
  onShare,
  onRename,
  onDelete,
  onViewDetails,
  onOpenFolder,
  onRevokeShare,
  showRevokeShare,
}: GDriveContextMenuProps) {
  const handleMenuItemClick = (action: () => void) => {
    action();
    onClose();
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          minWidth: 200,
        },
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
    >
      {onViewDetails && (
        <MenuItem onClick={() => handleMenuItemClick(onViewDetails)}>
          <ListItemIcon>
            <FuseSvgIcon>heroicons-outline:eye</FuseSvgIcon>
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
      )}

      {isFolder && onOpenFolder && (
        <MenuItem onClick={() => handleMenuItemClick(onOpenFolder)}>
          <ListItemIcon>
            <FuseSvgIcon>heroicons-outline:folder-open</FuseSvgIcon>
          </ListItemIcon>
          <ListItemText>Open Folder</ListItemText>
        </MenuItem>
      )}

      {!isFolder && onDownload && (
        <>
          <MenuItem onClick={() => handleMenuItemClick(onDownload)}>
            <ListItemIcon>
              <FuseSvgIcon>heroicons-outline:arrow-down-tray</FuseSvgIcon>
            </ListItemIcon>
            <ListItemText>Download</ListItemText>
          </MenuItem>
        </>
      )}

      {onShare && (
        <>
          <MenuItem onClick={() => handleMenuItemClick(onShare)}>
            <ListItemIcon>
              <FuseSvgIcon>heroicons-outline:share</FuseSvgIcon>
            </ListItemIcon>
            <ListItemText>Share</ListItemText>
          </MenuItem>
        </>
      )}

      {isFolder && onRename && (
        <>
          <MenuItem onClick={() => handleMenuItemClick(onRename)}>
            <ListItemIcon>
              <FuseSvgIcon>heroicons-outline:pencil</FuseSvgIcon>
            </ListItemIcon>
            <ListItemText>Rename</ListItemText>
          </MenuItem>
        </>
      )}

      {showRevokeShare && onRevokeShare && (
        <>
          <Divider />
          <MenuItem onClick={() => handleMenuItemClick(onRevokeShare)} sx={{ color: 'error.main' }}>
            <ListItemIcon sx={{ color: 'error.main' }}>
              <FuseSvgIcon>heroicons-outline:x-mark</FuseSvgIcon>
            </ListItemIcon>
            <ListItemText>Revoke Share</ListItemText>
          </MenuItem>
        </>
      )}

      {/*{onDelete && (*/}
      {/*  <>*/}
      {/*    <Divider />*/}
      {/*    <MenuItem onClick={() => handleMenuItemClick(onDelete)} sx={{ color: 'error.main' }}>*/}
      {/*      <ListItemIcon sx={{ color: 'error.main' }}>*/}
      {/*        <FuseSvgIcon>heroicons-outline:trash</FuseSvgIcon>*/}
      {/*      </ListItemIcon>*/}
      {/*      <ListItemText>Delete</ListItemText>*/}
      {/*    </MenuItem>*/}
      {/*  </>*/}
      {/*)}*/}
    </Menu>
  );
}

export default GDriveContextMenu;
