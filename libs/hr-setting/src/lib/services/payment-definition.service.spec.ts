import { NotFoundException } from '@nestjs/common';
import { PaymentDefinitionService } from './payment-definition.service';
import { PaymentDefinitionRepository } from '../repositories';
import {
  CreatePaymentDefinitionDto,
  UpdatePaymentDefinitionDto,
  PaymentDefinitionResponseDto,
} from '../dtos';

jest.mock('../repositories');

describe('PaymentDefinitionService', () => {
  let service: PaymentDefinitionService;
  let repo: jest.Mocked<PaymentDefinitionRepository>;

  const mockEntity = {
    id: '123',
  } as PaymentDefinitionResponseDto;

  beforeEach(() => {
    repo = {
      save: jest.fn(),
      findOne: jest.fn(),
      findAll: jest.fn(),
      delete: jest.fn(),
    } as unknown as jest.Mocked<PaymentDefinitionRepository>;

    service = new PaymentDefinitionService(repo);
  });

  describe('create', () => {
    it('should create and return a PaymentDefinition', async () => {
      const dto = {} as CreatePaymentDefinitionDto;

      repo.save.mockResolvedValue(mockEntity);

      const result = await service.create(dto);

      expect(repo.save).toHaveBeenCalledWith(dto);
      expect(result).toBeInstanceOf(PaymentDefinitionResponseDto);
    });
  });

  describe('findOne', () => {
    it('should return a PaymentDefinition if found', async () => {
      repo.findOne.mockResolvedValue(mockEntity);

      const result = await service.findOne('123');

      expect(repo.findOne).toHaveBeenCalledWith('123');
      expect(result).toBeInstanceOf(PaymentDefinitionResponseDto);
    });

    it('should throw NotFoundException if not found', async () => {
      repo.findOne.mockResolvedValue(null);

      await expect(service.findOne('not-exist')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findAll', () => {
    it('should return a list of PaymentDefinitions', async () => {
      repo.findAll.mockResolvedValue([mockEntity]);

      const result = await service.findAll();

      expect(repo.findAll).toHaveBeenCalled();
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(PaymentDefinitionResponseDto);
    });
  });

  describe('update', () => {
    it('should update and return the PaymentDefinition', async () => {
      const updateDto = {} as UpdatePaymentDefinitionDto;
      repo.findOne.mockResolvedValue(mockEntity);
      repo.save.mockResolvedValue({ ...mockEntity });

      const result = await service.update('123', updateDto);

      expect(repo.findOne).toHaveBeenCalledWith('123');
      expect(repo.save).toHaveBeenCalledWith({ ...mockEntity, ...updateDto });
      expect(result).toBeInstanceOf(PaymentDefinitionResponseDto);
    });
  });

  describe('delete', () => {
    it('should delete the PaymentDefinition if it exists', async () => {
      repo.findOne.mockResolvedValue(mockEntity);
      repo.delete.mockResolvedValue(undefined);

      await service.delete('123');

      expect(repo.findOne).toHaveBeenCalledWith('123');
      expect(repo.delete).toHaveBeenCalledWith('123');
    });

    it('should throw NotFoundException if not found', async () => {
      repo.findOne.mockResolvedValue(null);

      await expect(service.delete('non-existent')).rejects.toThrow(NotFoundException);
    });
  });
});
