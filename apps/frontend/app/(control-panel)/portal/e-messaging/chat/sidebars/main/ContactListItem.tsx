'use client';

import ListItemText from '@mui/material/ListItemText';
import ListItemButton from '@mui/material/ListItemButton';
import useNavigate from '@fuse/hooks/useNavigate';
import UserAvatar from '../../components/UserAvatar';
import { Contact } from '../../types/chat.types';
import { useChatApiData } from '../../hooks/useChatApiData';

type ContactListItemProps = {
  item: Contact;
};

/**
 * The contact list item.
 */
function ContactListItem(props: ContactListItemProps) {
  const { item } = props;
  const { chatList } = useChatApiData();

  const navigate = useNavigate();

  function handleClick() {
    const existingChat = chatList?.find((chat) => chat.contactIds.includes(item.id));

    if (existingChat) {
      // Navigate to existing conversation
      navigate(`/portal/e-messaging/chat/${existingChat.id}`);
    } else {
      // Navigate to contact directly - the Chat component will handle creating the conversation
      navigate(`/portal/e-messaging/chat/${item.id}`);
    }
  }

  return (
    <ListItemButton className="px-6 py-3 min-h-20" onClick={handleClick}>
      <UserAvatar user={item} />

      <ListItemText
        classes={{
          root: 'min-w-px px-4',
          primary: 'font-medium text-base',
          secondary: 'truncate',
        }}
        primary={item.name}
      />
    </ListItemButton>
  );
}

export default ContactListItem;
