import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { IGDriveFolder } from '../interfaces';
import { BaseEntity } from '@igov/common';
import { Employee } from '@igov/employee';

@Entity('g_drive_folders')
export class GDriveFolderEntity extends BaseEntity implements IGDriveFolder {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  folderName!: string;

  @Column({ nullable: true })
  parentId?: string;

  @Column()
  employeeId!: string;

  @Column({ nullable: true })
  registryId?: string;

  @ManyToOne(() => Employee, (employee) => employee.userId)
  @JoinColumn({ name: 'employee_id' })
  employee!: Employee;
}
