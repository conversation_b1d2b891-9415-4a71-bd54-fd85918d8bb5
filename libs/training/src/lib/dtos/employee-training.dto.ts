import { Is<PERSON><PERSON>, <PERSON>Optional, <PERSON>U<PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsN<PERSON>ber, <PERSON>, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EmployeeTrainingStatus } from '../enums';
import { TrainingResponseDto } from './training.dto';
import { EmployeeResponseDto } from '@igov/employee';
import { QueryOptionsDto } from '@igov/common';

export class CreateFeedbackEmployeeTrainingDto {
  @ApiProperty({ required: true })
  @IsString()
  feedback!: string;

  @ApiProperty({ required: true })
  @IsNumber({ allowNaN: false, allowInfinity: false })
  @Min(1)
  @Max(5)
  rating!: number;
}

export class EmployeeTrainingResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  employeeId!: string;

  @ApiProperty()
  trainingId!: string;

  @ApiProperty({ enum: EmployeeTrainingStatus })
  status!: EmployeeTrainingStatus;

  @ApiProperty({ required: false })
  feedback?: string;

  @ApiProperty({ required: false })
  rating?: number;

  @ApiProperty({ required: false })
  training!: TrainingResponseDto;

  @ApiProperty({ required: false })
  employee!: EmployeeResponseDto;
}

export class PaginatedEmployeeTrainingResponseDto {
  @ApiProperty({ type: [EmployeeTrainingResponseDto] })
  data!: EmployeeTrainingResponseDto[];

  @ApiProperty()
  total!: number;
}

export class QueryEmployeeTrainingDto extends QueryOptionsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  employeeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID(4)
  trainingId?: string;

  @ApiProperty({ required: false, enum: EmployeeTrainingStatus })
  @IsOptional()
  @IsEnum(EmployeeTrainingStatus)
  status?: EmployeeTrainingStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  feedback?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false })
  rating?: number;
}
