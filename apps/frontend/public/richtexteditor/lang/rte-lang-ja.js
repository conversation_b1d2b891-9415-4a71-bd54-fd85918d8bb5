//Japanese , 日本語
RTE_DefaultConfig.text_language = "言語";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "キャンセル";	//"Cancel"
RTE_DefaultConfig.text_normal = "通常";	//"Normal"
RTE_DefaultConfig.text_h1 = "見出し 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "見出し 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "見出し 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "見出し 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "見出し 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "ヘッドライン 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "ヘッドライン 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "閉じる";	//"Close"
RTE_DefaultConfig.text_bold = "太字";	//"Bold"
RTE_DefaultConfig.text_italic = "斜体";	//"Italic"
RTE_DefaultConfig.text_underline = "下線";	//"Underline"
RTE_DefaultConfig.text_strike = "ストライクライン";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "上付き";	//"Superscript"
RTE_DefaultConfig.text_subscript = "サブクリプト";	//"Subcript"
RTE_DefaultConfig.text_ucase = "大文字";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "小文字";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "フォーマットの削除";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "リンクの挿入";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "リンクを開く";	//"Open Link"
RTE_DefaultConfig.text_editlink = "リンクの編集";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "リンクの削除";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "ラインの高さ";	//"Line Height"
RTE_DefaultConfig.text_indent = "インデント";	//"Indent"
RTE_DefaultConfig.text_outdent = "インデント解除";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "見積もりをブロックする";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "順序付きリスト";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "順序なしリスト";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "水平ルールの挿入";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "日付の挿入";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "テーブルの挿入";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "イメージを挿入";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "ビデオを挿入";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "コードの挿入";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF の作成";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "絵文字を挿入する";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "特殊文字";	//"Special characters"
RTE_DefaultConfig.text_characters = "文字";	//"Characters"
RTE_DefaultConfig.text_fontname = "フォント";	//"Font"
RTE_DefaultConfig.text_fontsize = "サイズ";	//"Size"
RTE_DefaultConfig.text_forecolor = "テキストの色";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "背景色";	//"Back Color"
RTE_DefaultConfig.text_justify = "正当 化";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "左を正当化する";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "正当化権";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "センターを正当化する";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "完全に正当化";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "なしを正当化する";	//"Justify None"
RTE_DefaultConfig.text_delete = "削除";	//"Delete"
RTE_DefaultConfig.text_save = "ファイルの保存";	//"Save file"
RTE_DefaultConfig.text_selectall = "すべて選択";	//"Select All"
RTE_DefaultConfig.text_code = "HTML コード";	//"HTML Code"
RTE_DefaultConfig.text_preview = "プレビュー";	//"Preview"
RTE_DefaultConfig.text_print = "印刷";	//"Print"
RTE_DefaultConfig.text_undo = "アンドゥ";	//"Undo"
RTE_DefaultConfig.text_redo = "やり直し";	//"Redo"
RTE_DefaultConfig.text_more = "もっとその。。。";	//"More..."
RTE_DefaultConfig.text_newdoc = "新しいドキュメント";	//"New Doc"
RTE_DefaultConfig.text_help = "ヘルプ";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "ウィンドウに合わせる";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "全画面表示を終了";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "イメージ エディター";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "イメージスタイル";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "インライン スタイル";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "段落スタイル";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "リンク スタイル";	//"Link Styles"
RTE_DefaultConfig.text_link = "リンク";	//"Link"
RTE_DefaultConfig.text_style = "スタイル";	//"Styles"
RTE_DefaultConfig.text_cssclass = "CSS クラス";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "URL 別";	//"By Url"
RTE_DefaultConfig.text_upload = "アップロード";	//"Upload"
RTE_DefaultConfig.text_size = "サイズ";	//"Size"
RTE_DefaultConfig.text_text = "テキスト";	//"Text"
RTE_DefaultConfig.text_opennewwin = "新しいタブで開く";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "挿入";	//"Insert"
RTE_DefaultConfig.text_update = "更新";	//"Update"
RTE_DefaultConfig.text_find = "検索と置換";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "見つける";	//"Find"
RTE_DefaultConfig.text_replacewith = "置き換える";	//"Replace"
RTE_DefaultConfig.text_findnext = "次";	//"Next"
RTE_DefaultConfig.text_replaceonce = "置き換える";	//"Replace"
RTE_DefaultConfig.text_replaceall = "すべて置換";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "大文字と小文字の一致";	//"Match Case"
RTE_DefaultConfig.text_matchword = "単語の一致";	//"Match Word"
RTE_DefaultConfig.text_move_down = "下に移動";	//"Move Down"
RTE_DefaultConfig.text_move_up = "上へ移動";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "自動サイズ";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "幅 100%";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% 幅";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "幅 50%";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "幅 25%";	//"25% width"
RTE_DefaultConfig.text_controlsize = "サイズを設定する";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "代替テキスト";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "正当 化";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "画像キャプション";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "セルの結合";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "セルを垂直に分割";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "セルを水平に分割";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "セルのテキストの色";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "セルの背景色";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "行を上に挿入";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "行を下に挿入";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "列を左に挿入";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "列を右に挿入";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "列の削除";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "行の削除";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "テーブルの削除";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "自動サイズ";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "テーブルヘッダー";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "新しい段落を追加する";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "ペースト";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "ペースト";	//"Paste"
RTE_DefaultConfig.text_pastetext = "テキストを貼り付ける";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "HTML として貼り付け";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "単語を貼り付ける";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Ctrl キーを押しながら V キーを押して、下のボックスに内容を貼り付けてください。\r\nコンテンツは自動的にクリーニングされます。";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "段落";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "段落";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "上へ移動";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "下に移動";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "重複";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "削除";	//"Delete"
RTE_DefaultConfig.text_pmore = "もっとその。。";	//"More.."
RTE_DefaultConfig.text_togglemore = "もっとその。。";	//"More.."
RTE_DefaultConfig.text_toggleborder = "枠線の切り替え";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "カット";	//"Cut"
RTE_DefaultConfig.text_copy = "コピー";	//"Copy"
RTE_DefaultConfig.text_copied = "コピー";	//"copied"
RTE_DefaultConfig.text_insertgallery = "ギャラリーの挿入";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "ドキュメントの挿入";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "テンプレートの挿入";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "プレビュー";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "通常";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "モバイル";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "タブレット";	//"Tablet"
RTE_DefaultConfig.text_table = "テーブル";	//"Table"
RTE_DefaultConfig.text_tablecell = "テーブルセル";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "テーブル行";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "テーブル列";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "自動";	//"Automatic"
RTE_DefaultConfig.text_colormore = "より";	//"More"
RTE_DefaultConfig.text_colorpicker = "カラーピッカー";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "ウェブパレット";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "名前付き色";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "基本";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "さら";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "ドラッグ アンド ドロップ";	//"Drag and drop"
RTE_DefaultConfig.text_or = "または";	//"or"
RTE_DefaultConfig.text_clicktoupload = "クリックしてアップロード";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "既定の画像キャプション";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "検索";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "追加するテキストがこのフィールドの文字数制限に達しました。";	//"The text to be added has reached the character limit for this field."
