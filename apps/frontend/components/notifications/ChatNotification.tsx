'use client';

import React, { useState, useEffect } from 'react';
import { Snackbar, Paper, Typography, Avatar, IconButton, Slide, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { TransitionProps } from '@mui/material/transitions';

const StyledChatNotification = styled(Paper)(({ theme }) => ({
  background: '#FFFFFF',
  color: '#333333',
  borderRadius: 12,
  padding: '12px 16px',
  minWidth: 320,
  maxWidth: 400,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
  border: '1px solid #E0E0E0',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    boxShadow: '0 6px 24px rgba(0, 0, 0, 0.2)',
    transform: 'translateY(-1px)',
  },
}));

const SlideTransition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="left" ref={ref} {...props} />;
});

export interface ChatNotificationData {
  id: string;
  senderName: string;
  senderAvatar?: string;
  message: string;
  timestamp: string;
  chatId: string;
}

interface ChatNotificationProps {
  notification: ChatNotificationData | null;
  onClose: () => void;
  onNavigateToChat: (chatId: string) => void;
  autoHideDuration?: number;
}

/**
 * Custom Chat Notification Component
 * 
 * Displays incoming chat messages as notifications in the top-right corner.
 * Features:
 * - Clean white design with subtle shadows
 * - Shows sender info, message preview, and timestamp
 * - Click to navigate to chat conversation
 * - Auto-hide with manual close option
 */
export default function ChatNotification({
  notification,
  onClose,
  onNavigateToChat,
  autoHideDuration = 5000,
}: ChatNotificationProps) {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (notification) {
      setOpen(true);
    }
  }, [notification]);

  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
    setTimeout(onClose, 150); // Wait for animation
  };

  const handleClick = () => {
    if (notification) {
      onNavigateToChat(notification.chatId);
      handleClose();
    }
  };

  if (!notification) {
    return null;
  }

  // Truncate long messages
  const truncatedMessage = notification.message.length > 60 
    ? `${notification.message.substring(0, 60)}...` 
    : notification.message;

  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      TransitionComponent={SlideTransition}
      sx={{
        top: 24,
        right: 24,
        '.MuiSnackbarContent-root': {
          padding: 0,
          background: 'transparent',
          boxShadow: 'none',
        }
      }}
    >
      <StyledChatNotification onClick={handleClick} elevation={0}>
        <Box className="flex items-start gap-3">
          {/* Sender Avatar */}
          <Avatar
            src={notification.senderAvatar}
            alt={notification.senderName}
            sx={{ width: 40, height: 40 }}
          >
            {notification.senderName.charAt(0).toUpperCase()}
          </Avatar>

          {/* Message Content */}
          <Box className="flex-1 min-w-0">
            <Box className="flex items-center justify-between mb-1">
              <Typography 
                variant="subtitle2" 
                className="font-semibold text-gray-800 truncate"
                sx={{ fontSize: '0.875rem' }}
              >
                {notification.senderName}
              </Typography>
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClose();
                }}
                sx={{ 
                  padding: '2px',
                  marginLeft: 1,
                  '&:hover': { background: 'rgba(0, 0, 0, 0.04)' }
                }}
              >
                <FuseSvgIcon size={16} className="text-gray-400">
                  heroicons-outline:x-mark
                </FuseSvgIcon>
              </IconButton>
            </Box>
            
            <Typography 
              variant="body2" 
              className="text-gray-600 mb-1"
              sx={{ fontSize: '0.813rem', lineHeight: 1.4 }}
            >
              {truncatedMessage}
            </Typography>

            <Box className="flex items-center gap-1">
              <FuseSvgIcon size={12} className="text-gray-400">
                heroicons-outline:chat-bubble-left-right
              </FuseSvgIcon>
              <Typography 
                variant="caption" 
                className="text-gray-400"
                sx={{ fontSize: '0.75rem' }}
              >
                New message • Click to reply
              </Typography>
            </Box>
          </Box>
        </Box>
      </StyledChatNotification>
    </Snackbar>
  );
}