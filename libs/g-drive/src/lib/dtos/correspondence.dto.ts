import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { CorrespondenceStatus, CorrespondenceType } from '../enums';
import { CreateAttachmentDto, QueryOptionsDto } from '@igov/common';
import { GDriveFileResponseDto } from './g-drive-file.dto';
import { EmployeeResponseDto } from '@igov/employee';

export class CreateCorrespondenceDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  refNo!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  subject!: string;

  @ApiProperty()
  @IsString()
  receivedFrom!: string;

  @ApiProperty()
  @IsString()
  sentTo!: string;

  @ApiProperty({ enum: CorrespondenceType })
  @IsEnum(CorrespondenceType)
  type!: CorrespondenceType;

  @ApiProperty({ format: 'date-time' })
  @IsDateString()
  correspondenceDate!: Date;

  @ApiProperty({ format: 'date-time' })
  @IsDateString()
  receiptDate!: Date;

  @ApiProperty({ type: CreateAttachmentDto })
  @IsObject()
  attachment!: CreateAttachmentDto;

  @ApiProperty()
  @IsUUID()
  registryId!: string;

  createdBy!: string;
}

export class UpdateCorrespondenceDto extends PartialType(CreateCorrespondenceDto) {}

export class QueryCorrespondenceDto extends QueryOptionsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  registryId?: string;

  @ApiProperty({ required: false, enum: CorrespondenceType })
  @IsOptional()
  @IsEnum(CorrespondenceType)
  type?: CorrespondenceType;
}

export class ForwardCorrespondenceDto {
  correspondenceId!: string;

  @ApiProperty()
  @IsUUID()
  employeeId!: string;

  createdBy!: string;

  @ApiProperty({ enum: CorrespondenceStatus })
  @IsEnum(CorrespondenceStatus)
  status!: CorrespondenceStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class CorrespondenceResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  refNo!: string;

  @ApiProperty()
  subject!: string;

  @ApiProperty()
  receivedFrom!: string;

  @ApiProperty()
  sentTo!: string;

  @ApiProperty({ enum: CorrespondenceType })
  type!: CorrespondenceType;

  @ApiProperty({ type: 'string', format: 'date-time' })
  correspondenceDate!: Date;

  @ApiProperty({ type: 'string', format: 'date-time' })
  receiptDate!: Date;

  @ApiProperty()
  fileId!: string;

  @ApiProperty({ type: () => GDriveFileResponseDto })
  file!: GDriveFileResponseDto;

  @ApiProperty()
  createdBy!: string;

  @ApiProperty({ type: () => EmployeeResponseDto })
  creator!: EmployeeResponseDto;

  @ApiProperty()
  createdDate!: Date;

  @ApiProperty()
  updatedAt?: Date;
}

export class PaginatedCorrespondenceResponseDto {
  @ApiProperty({ type: [CorrespondenceResponseDto] })
  data!: CorrespondenceResponseDto[];

  @ApiProperty()
  total!: number;
}
