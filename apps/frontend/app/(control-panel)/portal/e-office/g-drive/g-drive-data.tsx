export type GDriveItem = {
  id: string;
  folderId: string | null; // null for root items
  type: 'folder' | string; // 'folder' or file extension like 'PDF', 'DOCX', 'JPG'
  name: string;
  createdBy?: string;
  createdAt?: string; // ISO date string
  modifiedAt?: string; // ISO date string
  size?: string; // e.g., "15 MB", "200 KB"
  contents?: string; // For folders: "3 files, 1 folder", for files: could be description
  sharedWith?: string[]; // List of user IDs or emails
  // Add any other fields relevant to your GDrive items
};

export const gDriveData: GDriveItem[] = [
  // Root Folders
  {
    id: 'folder-my-drive',
    folderId: null,
    type: 'folder',
    name: 'My Drive',
    createdBy: 'Admin User',
    createdAt: '2023-01-01T10:00:00Z',
    modifiedAt: '2023-01-10T10:00:00Z',
    contents: '2 files, 1 folder',
  },
  {
    id: 'folder-shared-with-me',
    folderId: null,
    type: 'folder',
    name: 'Shared with me',
    createdBy: 'System',
    createdAt: '2023-01-01T10:00:00Z',
    modifiedAt: '2023-01-05T10:00:00Z',
    contents: '1 file, 0 folders',
  },

  // Items inside 'My Drive' (folder-my-drive)
  {
    id: 'file-project-alpha-report',
    folderId: 'folder-my-drive',
    type: 'PDF',
    name: 'Project Alpha Report.pdf',
    createdBy: 'User One',
    createdAt: '2023-01-05T11:00:00Z',
    modifiedAt: '2023-01-06T14:30:00Z',
    size: '2.5 MB',
  },
  {
    id: 'file-budget-q1',
    folderId: 'folder-my-drive',
    type: 'XLSX',
    name: 'Budget Q1.xlsx',
    createdBy: 'User Two',
    createdAt: '2023-01-03T09:00:00Z',
    modifiedAt: '2023-01-08T12:00:00Z',
    size: '512 KB',
  },
  {
    id: 'folder-project-beta',
    folderId: 'folder-my-drive',
    type: 'folder',
    name: 'Project Beta',
    createdBy: 'User One',
    createdAt: '2023-01-10T15:00:00Z',
    modifiedAt: '2023-01-11T10:00:00Z',
    contents: '1 file, 0 folders',
  },

  // Item inside 'Project Beta' (folder-project-beta)
  {
    id: 'file-beta-requirements',
    folderId: 'folder-project-beta',
    type: 'DOCX',
    name: 'Beta Requirements.docx',
    createdBy: 'User Three',
    createdAt: '2023-01-11T09:30:00Z',
    modifiedAt: '2023-01-11T11:45:00Z',
    size: '1.2 MB',
  },

  // Item inside 'Shared with me' (folder-shared-with-me)
  {
    id: 'file-presentation-deck',
    folderId: 'folder-shared-with-me',
    type: 'PPTX',
    name: 'Company Presentation.pptx',
    createdBy: 'External Collaborator',
    createdAt: '2023-01-04T16:00:00Z',
    modifiedAt: '2023-01-04T17:30:00Z',
    size: '15.3 MB',
    sharedWith: ['Admin User'],
  },
];
