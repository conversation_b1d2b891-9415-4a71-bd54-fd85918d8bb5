//Lithuanian , Lietuvių
RTE_DefaultConfig.text_language = "kalba";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "atšaukti";	//"Cancel"
RTE_DefaultConfig.text_normal = "normalus";	//"Normal"
RTE_DefaultConfig.text_h1 = "1 antraštė";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "2 antraštė";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "3 antraštė";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "4 antraštė";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "5 antraštė";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "6 antraštė";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "7 antraštė";	//"Headline 7"
RTE_DefaultConfig.text_close = "uždaryti";	//"Close"
RTE_DefaultConfig.text_bold = "paryškinti";	//"Bold"
RTE_DefaultConfig.text_italic = "pasvirasis";	//"Italic"
RTE_DefaultConfig.text_underline = "pabraukimas";	//"Underline"
RTE_DefaultConfig.text_strike = "Perbraukialinija";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Viršutinis indeksas";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Poverštas";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Didžiosiomis raidėmis";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Mažosiomis raidėmis";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Šalinti formatą";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Įterpti saitą";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Atidaryti saitą";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Redaguoti saitą";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Šalinti saitą";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Linijos aukštis";	//"Line Height"
RTE_DefaultConfig.text_indent = "įtrauka";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent (-us)";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Blokuoti pasiūlymą";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Užsakytas sąrašas";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Netvarkingas sąrašas";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Įterpti horizontaliąją taisyklę";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Įterpti datą";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Įterpti lentelę";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Įterpti vaizdą";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Įterpti vaizdo įrašą";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Įterpti kodą";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Kurti PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Įterpti jaustukus";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Specialieji simboliai";	//"Special characters"
RTE_DefaultConfig.text_characters = "simbolių";	//"Characters"
RTE_DefaultConfig.text_fontname = "šrifto";	//"Font"
RTE_DefaultConfig.text_fontsize = "dydis";	//"Size"
RTE_DefaultConfig.text_forecolor = "Teksto spalva";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Galinė spalva";	//"Back Color"
RTE_DefaultConfig.text_justify = "pateisinti";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Abipusė lygiuotė kairėje";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Abipusė lygiuotė dešinėje";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Lygiuotė centras";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Lygiuoti visą";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Lygiuoti nė vieno";	//"Justify None"
RTE_DefaultConfig.text_delete = "naikinti";	//"Delete"
RTE_DefaultConfig.text_save = "Įrašyti failą";	//"Save file"
RTE_DefaultConfig.text_selectall = "Pažymėti viską";	//"Select All"
RTE_DefaultConfig.text_code = "HTML kodas";	//"HTML Code"
RTE_DefaultConfig.text_preview = "peržiūra";	//"Preview"
RTE_DefaultConfig.text_print = "spausdinti";	//"Print"
RTE_DefaultConfig.text_undo = "anuliuoti";	//"Undo"
RTE_DefaultConfig.text_redo = "perdaryti";	//"Redo"
RTE_DefaultConfig.text_more = "Daugiau...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Naujas dokumentas";	//"New Doc"
RTE_DefaultConfig.text_help = "padėti";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Talpinti į langą";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Išeiti iš viso ekrano";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Vaizdo rengyklė";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Vaizdo stiliai";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Įdėtieji stiliai";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Pastraipų stiliai";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Saitų stiliai";	//"Link Styles"
RTE_DefaultConfig.text_link = "nuorodą";	//"Link"
RTE_DefaultConfig.text_style = "stilių";	//"Styles"
RTE_DefaultConfig.text_cssclass = "CSS klasės";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Pagal URL";	//"By Url"
RTE_DefaultConfig.text_upload = "įkelti";	//"Upload"
RTE_DefaultConfig.text_size = "dydis";	//"Size"
RTE_DefaultConfig.text_text = "teksto";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Atidaryti naujame skirtuke";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "įterpti";	//"Insert"
RTE_DefaultConfig.text_update = "naujinimas";	//"Update"
RTE_DefaultConfig.text_find = "Rasti&pakeisti";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "rasti";	//"Find"
RTE_DefaultConfig.text_replacewith = "pakeisti";	//"Replace"
RTE_DefaultConfig.text_findnext = "kitą";	//"Next"
RTE_DefaultConfig.text_replaceonce = "pakeisti";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Keisti viską";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Rungtynių atvejis";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Atitikti žodį";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Perkelti žemyn";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Perkelti aukštyn";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Automatinis dydis";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% plotis";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% plotis";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% plotis";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% plotis";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Nustatyti dydį";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alternatyvusis tekstas";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "pateisinti";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Vaizdo antraštė";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Langelių suliejimas";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Skaidyti langelius vertikaliai";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Skaidyti langelius horizontaliai";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Langelio teksto spalva";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Langelio nugaros spalva";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Įterpti eilutę aukščiau";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Įterpti eilutę žemiau";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Įterpti stulpelį kairėje";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Įterpti stulpelį dešinėje";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Naikinti stulpelį";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Naikinti eilutę";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Naikinti lentelę";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Automatinis dydis";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Lentelės antraštė";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Įtraukti naują pastraipą";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "įklijuoti";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "įklijuoti";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Įklijuoti tekstą";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Įklijuoti kaip HTML";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Įklijuoti žodį";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Norėdami įklijuoti turinį į žemiau esantį laukelį, naudokite CTRL+V. \r\nTurkytas turinys bus išvalytas automatiškai.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "dalyse";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "dalyse";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Perkelti aukštyn";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Perkelti žemyn";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "pasikartojančius";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "naikinti";	//"Delete"
RTE_DefaultConfig.text_pmore = "Daugiau..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Daugiau..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Kaitalioti kraštinę";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "supjaustyti";	//"Cut"
RTE_DefaultConfig.text_copy = "kopijuoti";	//"Copy"
RTE_DefaultConfig.text_copied = "nukopijuoti";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Įterpti galeriją";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Įterpti dokumentą";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Įterpti šabloną";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "peržiūra";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normalus";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobilus";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tabletė";	//"Tablet"
RTE_DefaultConfig.text_table = "lentelės";	//"Table"
RTE_DefaultConfig.text_tablecell = "Lentelės langelis";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Lentelės eilutė";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Lentelės stulpelis";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatinis";	//"Automatic"
RTE_DefaultConfig.text_colormore = "daugiau";	//"More"
RTE_DefaultConfig.text_colorpicker = "Spalvų parinkiklis";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Žiniatinklio paletė";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Įvardytos spalvos";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "pagrindinio";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "be to";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Vilkite ir upuść";	//"Drag and drop"
RTE_DefaultConfig.text_or = "arba";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Spustelėkite, jei norite įkelti";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Numatytoji vaizdo antraštė";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "paieškos";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Tekstas, kurį reikia pridėti, pasiekė šio lauko simbolių ribą.";	//"The text to be added has reached the character limit for this field."
