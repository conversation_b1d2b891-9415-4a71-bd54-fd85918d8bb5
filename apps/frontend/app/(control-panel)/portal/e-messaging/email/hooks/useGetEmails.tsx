import { useParams } from 'next/navigation';
import _ from 'lodash';
import { mockEmails, mockFolders, mockLabels } from '../services/emailData';

function useGetEmails() {
  const { emailParams } = useParams<{ emailParams: string[] }>();
  const [category, subCategory] = emailParams || [];

  // Simulate query behavior with mock data
  let filteredEmails = [...mockEmails];

  if (category === 'folders') {
    const folder = _.find(mockFolders, { slug: subCategory });
    if (folder) {
      filteredEmails = filteredEmails.filter((email) => email.folder === folder.slug);
    }
  } else if (category === 'filters') {
    if (subCategory === 'starred') {
      filteredEmails = filteredEmails.filter((email) => email.starred);
    } else if (subCategory === 'important') {
      filteredEmails = filteredEmails.filter((email) => email.important);
    }
  } else if (category === 'labels') {
    const label = _.find(mockLabels, { slug: subCategory });
    if (label) {
      filteredEmails = filteredEmails.filter((email) => email.labels.includes(label.slug));
    }
  }

  return {
    data: filteredEmails,
    isLoading: false,
    isError: false,
    error: null,
  };
}

export default useGetEmails;
