import React, { useState } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import { Document, Page } from 'react-pdf';
import { X } from 'lucide-react';

interface FilePreviewProps {
  file: {
    url: string;
    type: string;
    name: string;
  };
  onClose?: () => void;
}

const FilePreview: React.FC<FilePreviewProps> = ({ file, onClose }) => {
  const [numPages, setNumPages] = useState<number>(0);

  const renderPreview = () => {
    if (file.type.startsWith('image/')) {
      return (
        <img
          src={file.url}
          alt={file.name}
          style={{
            maxWidth: '100%',
            maxHeight: '500px',
            objectFit: 'contain',
          }}
        />
      );
    }

    if (file.type.startsWith('video/')) {
      return (
        <video
          controls
          style={{
            maxWidth: '100%',
            maxHeight: '500px',
          }}
        >
          <source src={file.url} type={file.type} />
          Your browser does not support the video tag.
        </video>
      );
    }

    if (file.type === 'application/pdf') {
      return (
        <Document file={file.url} onLoadSuccess={({ numPages }) => setNumPages(numPages)}>
          <Page pageNumber={1} />
          {numPages > 1 && (
            <Typography variant="caption" sx={{ mt: 1 }}>
              + {numPages - 1} more pages
            </Typography>
          )}
        </Document>
      );
    }

    if (file.type.startsWith('text/')) {
      return (
        <iframe
          src={file.url}
          style={{
            width: '100%',
            height: '500px',
            border: '1px solid #ccc',
          }}
          title={file.name}
        />
      );
    }

    return <Typography color="text.secondary">Preview not available for this file type</Typography>;
  };

  return (
    <Box
      sx={{
        position: 'relative',
        p: 2,
        bgcolor: 'background.paper',
        borderRadius: 1,
        boxShadow: 3,
      }}
    >
      {onClose && (
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            zIndex: 1,
          }}
        >
          <X size={20} />
        </IconButton>
      )}
      <Typography variant="subtitle1" gutterBottom>
        {file.name}
      </Typography>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
          bgcolor: 'action.hover',
          borderRadius: 1,
          overflow: 'hidden',
        }}
      >
        {renderPreview()}
      </Box>
    </Box>
  );
};

export default FilePreview;
