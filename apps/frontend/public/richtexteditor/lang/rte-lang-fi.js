//Finnish , <PERSON>omi
RTE_DefaultConfig.text_language = "kieli";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "peruuta";	//"Cancel"
RTE_DefaultConfig.text_normal = "normaali";	//"Normal"
RTE_DefaultConfig.text_h1 = "Otsikko 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Otsikko 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Otsikko 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Otsikko 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Otsikko 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Otsikko 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Otsikko 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "sulje";	//"Close"
RTE_DefaultConfig.text_bold = "lihavoitu";	//"Bold"
RTE_DefaultConfig.text_italic = "kursivoitu";	//"Italic"
RTE_DefaultConfig.text_underline = "alleviivaus";	//"Underline"
RTE_DefaultConfig.text_strike = "Strike Linja";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "yläindeksi";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Lisää lävy";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Iso kirjain";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Pieni kirjain";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Poista muoto";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Lisää linkki";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Avaa linkki";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Muokkaa linkkiä";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Poista linkki";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Viivan korkeus";	//"Line Height"
RTE_DefaultConfig.text_indent = "luetelmakohta";	//"Indent"
RTE_DefaultConfig.text_outdent = "ulonna";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Estä lainaus";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Tilattu luettelo";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Järjestämätön luettelo";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Lisää vaakaviiva";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Lisää päivämäärä";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Lisää taulukko";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Lisää kuva";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Lisää video";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Lisää koodi";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Luo PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Lisää emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Erikoismerkkejä";	//"Special characters"
RTE_DefaultConfig.text_characters = "merkkiä";	//"Characters"
RTE_DefaultConfig.text_fontname = "fontti";	//"Font"
RTE_DefaultConfig.text_fontsize = "koko";	//"Size"
RTE_DefaultConfig.text_forecolor = "Tekstin väri";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Taustan väri";	//"Back Color"
RTE_DefaultConfig.text_justify = "perustella";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Tasaa vasemmalle";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Tasaa oikealle";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Tasaa keskipiste";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Tasaa koko";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Tasaa ei mitään";	//"Justify None"
RTE_DefaultConfig.text_delete = "poista";	//"Delete"
RTE_DefaultConfig.text_save = "Tallenna tiedosto";	//"Save file"
RTE_DefaultConfig.text_selectall = "Valitse kaikki";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-koodi";	//"HTML Code"
RTE_DefaultConfig.text_preview = "esikatselu";	//"Preview"
RTE_DefaultConfig.text_print = "tulosta";	//"Print"
RTE_DefaultConfig.text_undo = "kumoa";	//"Undo"
RTE_DefaultConfig.text_redo = "redo";	//"Redo"
RTE_DefaultConfig.text_more = "Lisää...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Uusi asiakirja";	//"New Doc"
RTE_DefaultConfig.text_help = "auttaa";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Sovita ikkunaan";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Poistu koko näytöstä";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Kuvankäsittelyohjelmassa";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Kuva tyylit";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline-tyylit";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Kappaletyylit";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Linkitä tyylit";	//"Link Styles"
RTE_DefaultConfig.text_link = "linkki";	//"Link"
RTE_DefaultConfig.text_style = "tyylejä";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css Luokat";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url-osoitteen mukaan";	//"By Url"
RTE_DefaultConfig.text_upload = "ladata";	//"Upload"
RTE_DefaultConfig.text_size = "koko";	//"Size"
RTE_DefaultConfig.text_text = "teksti";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Avaa uudessa välilehdessä";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "aseta";	//"Insert"
RTE_DefaultConfig.text_update = "päivitys";	//"Update"
RTE_DefaultConfig.text_find = "Etsi ja korvaa";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "löytää";	//"Find"
RTE_DefaultConfig.text_replacewith = "korvata";	//"Replace"
RTE_DefaultConfig.text_findnext = "seuraava";	//"Next"
RTE_DefaultConfig.text_replaceonce = "korvata";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Korvaa kaikki";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Sovita kirjainkoko";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Vastaa sanaa";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Siirrä alas";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Siirrä ylös";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Automaattinen koko";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% leveys";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% leveys";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% leveys";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% leveys";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Aseta koko";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Vaihtoehtoinen teksti";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "perustella";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Kuvan otsikko";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Yhdistä solut";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Jaa solut pystysuunnassa";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Jaa solut vaakasuunnassa";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Solun tekstin väri";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Solun takaisin -väri";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Lisää rivi yläpuolelle";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Lisää rivi alapuolelle";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Lisää sarake vasemmalle";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Lisää sarake oikealle";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Poista sarake";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Poista rivi";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Poista taulukko";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Automaattinen koko";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Taulukon otsikko";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Uuden kappaleen lisääminen";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "liitä";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "liitä";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Liitä teksti";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Liitä html-muodossa";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Liitä Sana";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Liitä sisältö alla olevaan ruutuun CTRL+V:n avulla. \r\nSisältö puhdistetaan automaattisesti.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "kohdassa";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "kohdassa";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Siirrä ylös";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Siirrä alas";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "kaksoiskappaleiden";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "poista";	//"Delete"
RTE_DefaultConfig.text_pmore = "Lisää..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Lisää..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Ota reunat käyttöön tai poista se käytöstä";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "leikata";	//"Cut"
RTE_DefaultConfig.text_copy = "kopioi";	//"Copy"
RTE_DefaultConfig.text_copied = "kopioida";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Lisää valikoima";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Lisää asiakirja";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Lisää malli";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "esikatselu";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normaali";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tabletti";	//"Tablet"
RTE_DefaultConfig.text_table = "taulukossa";	//"Table"
RTE_DefaultConfig.text_tablecell = "Taulukon solu";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Taulukon rivi";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Taulukon sarake";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automaattinen";	//"Automatic"
RTE_DefaultConfig.text_colormore = "lisää";	//"More"
RTE_DefaultConfig.text_colorpicker = "Värinvalitsin";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Web-paletti";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Nimetyt värit";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "perus";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "lisäksi";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Vedä ja pudota";	//"Drag and drop"
RTE_DefaultConfig.text_or = "tai";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Lataa napsauttamalla";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Kuvan oletusotsikko";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "etsi";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Lisättävä teksti on saavuttanut tämän kentän merkkirajan.";	//"The text to be added has reached the character limit for this field."
