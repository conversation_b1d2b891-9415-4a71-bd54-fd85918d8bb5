import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { Employee } from '../entity';
import { IEmployee, IEmployeeFilters } from '../interfaces';
import { In } from 'typeorm';

@Injectable()
export class EmployeeRepository extends BaseRepository<Employee> {
  override entityClassName = Employee;
  private readonly logger = new Logger(EmployeeRepository.name);

  findOne(id: string): Promise<Employee | null> {
    return this.repository.findOne({ where: { userId: id } });
  }

  findOneByEmail(email: string): Promise<Employee | null> {
    return this.repository.findOne({ where: { email } });
  }

  async findAll(query: IEmployeeFilters): Promise<{ data: Employee[]; total: number }> {
    const { skip, limit } = query;
    const queryBuilder = this.repository.createQueryBuilder('employee');

    if (query?.search) {
      const searchTerm = `%${query.search.trim()}%`;
      queryBuilder.andWhere(
        '(employee.firstName ILIKE :search OR ' +
          'employee.lastName ILIKE :search OR ' +
          'employee.email ILIKE :search OR ' +
          'employee.otherNames ILIKE :search OR ' +
          'employee.title ILIKE :search OR ' +
          'employee.mobileNumber ILIKE :search OR ' +
          'employee.presentPosting ILIKE :search)',
        { search: searchTerm },
      );
    }

    if (query?.gender) {
      queryBuilder.andWhere('employee.gender = :gender', { gender: query.gender });
    }

    if (query?.unitId) {
      queryBuilder.andWhere('employee.unitId = :unitId', { unitId: query.unitId });
    }

    if (query?.departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId: query.departmentId,
      });
    }

    if (query?.gradeLevel) {
      queryBuilder.andWhere('employee.gradeLevel = :gradeLevel', {
        gradeLevel: query.gradeLevel,
      });
    }

    if (query?.stateOfOrigin) {
      queryBuilder.andWhere('employee.stateOfOrigin = :stateOfOrigin', {
        stateOfOrigin: query.stateOfOrigin,
      });
    }

    if (query?.bloodGroup) {
      queryBuilder.andWhere('employee.bloodGroup = :bloodGroup', {
        bloodGroup: query.bloodGroup,
      });
    }

    if (query?.fromBirthDate) {
      queryBuilder.andWhere('employee.birthDate >= :fromBirthDate', {
        fromBirthDate: query.fromBirthDate,
      });
    }

    if (query?.toBirthDate) {
      queryBuilder.andWhere('employee.birthDate <= :toBirthDate', {
        toBirthDate: query.toBirthDate,
      });
    }

    if (query?.fromFirstAppointmentDate) {
      queryBuilder.andWhere('employee.firstAppointmentDate >= :fromFirstAppointmentDate', {
        fromFirstAppointmentDate: query.fromFirstAppointmentDate,
      });
    }

    if (query?.toFirstAppointmentDate) {
      queryBuilder.andWhere('employee.firstAppointmentDate <= :toFirstAppointmentDate', {
        toFirstAppointmentDate: query.toFirstAppointmentDate,
      });
    }

    const totalCount = await queryBuilder.getCount();
    this.logger.log(`Total employees found: ${totalCount}`);
    if (skip) {
      queryBuilder.skip(skip);
    }

    if (limit) {
      queryBuilder.take(limit);
    }

    const employees = await queryBuilder.getMany();

    return { data: employees, total: totalCount };
  }

  async findByIdsList(ids: string[]): Promise<Employee[]> {
    return this.repository.find({ where: { userId: In(ids) } });
  }

  save(employee: Partial<IEmployee>): Promise<Employee> {
    return this.repository.save(employee);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}
