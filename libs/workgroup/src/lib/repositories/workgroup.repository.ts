import { Injectable } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { WorkgroupEntity } from '../entity';
import { WorkgroupStatusEnum } from '../enums';
import { ExecutorDetailDto } from '../dtos';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class WorkgroupRepository extends BaseRepository<WorkgroupEntity> {
  override entityClassName = WorkgroupEntity;
  async findOne(params: {
    id: string;
    relations?: string[];
  }): Promise<(WorkgroupEntity & { executorDetails: ExecutorDetailDto[] }) | null> {
    const { id } = params;

    const queryBuilder = this.repository.createQueryBuilder('workgroup');
    queryBuilder
      .leftJoinAndSelect('workgroup.initiatedBy', 'initiator')
      .addSelect(['initiator.userId', 'initiator.firstName', 'initiator.lastName']);
    queryBuilder
      .leftJoinAndSelect('workgroup.history', 'history')
      .leftJoinAndSelect('history.employee', 'historyEmployee')
      .addSelect([
        'historyEmployee.userId',
        'historyEmployee.firstName',
        'historyEmployee.lastName',
      ]);
    queryBuilder
      .leftJoinAndSelect('workgroup.comments', 'comments')
      .leftJoinAndSelect('comments.commentedBy', 'commentedByEmployee')
      .addSelect([
        'commentedByEmployee.userId',
        'commentedByEmployee.firstName',
        'commentedByEmployee.lastName',
      ]);
    queryBuilder
      .leftJoinAndSelect('workgroup.docs', 'docs')
      .leftJoinAndSelect('docs.gDrive', 'gDrive')
      .leftJoinAndSelect('docs.uploadedBy', 'uploadedByEmployee')
      .addSelect([
        'gDrive.id',
        'gDrive.attachment',
        'uploadedByEmployee.userId',
        'uploadedByEmployee.firstName',
        'uploadedByEmployee.lastName',
      ]);

    queryBuilder.where('workgroup.id = :id', { id });

    const workgroup = await queryBuilder.getOne();
    if (!workgroup) return null;
    const executorIds = workgroup.executors || [];
    const rawExecutors = executorIds.length
      ? await this.repository.manager
          .createQueryBuilder('employees', 'e')
          .select(['e.userId', 'e.firstName', 'e.lastName'])
          .where('e.userId IN (:...executorIds)', { executorIds })
          .getMany()
      : [];

    const executorDetails = plainToInstance(ExecutorDetailDto, rawExecutors);

    return {
      ...workgroup,
      executorDetails,
    };
  }
  async findOneById(params: { id: string }): Promise<WorkgroupEntity | null> {
    const { id } = params;

    const queryBuilder = this.repository.createQueryBuilder('workgroups');
    queryBuilder.where('workgroups.id = :id', { id });

    return queryBuilder.getOne();
  }
  async findAll(query: {
    skip?: number;
    limit?: number;
    search?: string;
    status?: WorkgroupStatusEnum;
    fromDate?: string;
    toDate?: string;
    initiatedById?: string;
    executorId?: string;
    dueDate?: string;
  }): Promise<{ data: WorkgroupEntity[]; total: number }> {
    const { skip, limit, search, status, initiatedById, fromDate, toDate, executorId, dueDate } =
      query;

    const queryBuilder = this.repository
      .createQueryBuilder('workgroup')
      .leftJoin('workgroup.initiatedBy', 'initiatedBy')
      .addSelect(['initiatedBy.userId', 'initiatedBy.firstName', 'initiatedBy.lastName']);

    if (search) {
      const searchTerm = `%${search.trim()}%`;
      queryBuilder.andWhere(
        '(workgroup.title ILIKE :search OR workgroup.description ILIKE :search)',
        { search: searchTerm },
      );
    }

    if (status) {
      queryBuilder.andWhere('workgroup.status = :status', { status });
    }

    if (initiatedById) {
      queryBuilder.andWhere('workgroup.initiatedById = :initiatedById', { initiatedById });
    }

    if (fromDate) {
      queryBuilder.andWhere('workgroup.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      queryBuilder.andWhere('workgroup.createdAt <= :toDate', { toDate });
    }
    if (dueDate) {
      queryBuilder.andWhere('workgroup.dueDate = :dueDate', { dueDate });
    }

    if (executorId) {
      queryBuilder.andWhere('workgroup.executors @> :executorId', {
        executorId: JSON.stringify([executorId]),
      });
    }

    const total = await queryBuilder.clone().getCount();

    if (skip) queryBuilder.skip(skip);
    if (limit) queryBuilder.take(limit);

    const data = await queryBuilder.getMany();
    return { data, total };
  }
}
