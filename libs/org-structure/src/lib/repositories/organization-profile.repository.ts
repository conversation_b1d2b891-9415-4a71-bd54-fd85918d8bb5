import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { OrganizationProfile } from '../entities';
import { IOrganizationProfile } from '../interfaces';

@Injectable()
export class OrganizationProfileRepository extends BaseRepository<OrganizationProfile> {
  private readonly logger = new Logger(OrganizationProfileRepository.name);
  override entityClassName = OrganizationProfile;

  findOne(id: string): Promise<OrganizationProfile | null> {
    return this.repository.findOne({ where: { id } });
  }

  findAll(): Promise<OrganizationProfile[]> {
    return this.repository.find();
  }

  save(profile: IOrganizationProfile): Promise<OrganizationProfile> {
    return this.repository.save(profile);
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}
