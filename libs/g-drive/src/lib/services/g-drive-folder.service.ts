import { GDriveFolderRepository } from '../repositories';
import { Injectable, NotFoundException } from '@nestjs/common';
import { IGDriveFolder } from '../interfaces';
import {
  GDriveFolderResponseDto,
  PaginatedGDriveFolderResponseDto,
  QueryGDriveFolderDto,
  QueryRootFoldersDto,
} from '../dtos';

@Injectable()
export class GDriveFolderService {
  constructor(private readonly gDriveFolderRepository: GDriveFolderRepository) {}

  async getRootFolders(query: QueryRootFoldersDto): Promise<PaginatedGDriveFolderResponseDto> {
    const { employeeId, skip = 0, limit = 10, search } = query;
    return this.gDriveFolderRepository.findRootFoldersByEmployee(employeeId, skip, limit, search);
  }

  async getSubFolders(query: QueryGDriveFolderDto): Promise<PaginatedGDriveFolderResponseDto> {
    const { parentId, skip = 0, limit = 10, search } = query;
    return this.gDriveFolderRepository.findSubFolders(parentId, skip, limit, search);
  }

  async save(folderData: Partial<IGDriveFolder>): Promise<GDriveFolderResponseDto> {
    return this.gDriveFolderRepository.saveFolder(folderData);
  }

  async update(folderData: Partial<IGDriveFolder>): Promise<GDriveFolderResponseDto> {
    const { employeeId, id } = folderData;
    const existingFolder = await this.gDriveFolderRepository.findFolderByIdAndEmployeeId(
      id as string,
      employeeId as string,
    );
    if (!existingFolder) {
      throw new NotFoundException(`Folder with id: ${id} for employee: ${employeeId} not found`);
    }
    return this.gDriveFolderRepository.saveFolder(folderData);
  }
}
