import { Controller, Post, Body, Get, Param, Patch, Delete, UseGuards } from '@nestjs/common';
import { OrganizationStructureService } from '../services/org-structure.service';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
  CreateMdaDto,
  MdaResponseDto,
  UpdateMdaDto,
  CreateUnitDto,
  UnitResponseDto,
  UpdateUnitDto,
  CreatePositionDto,
  PositionResponseDto,
  UpdatePositionDto,
  CreateOrganizationProfileDto,
  OrganizationProfileResponseDto,
  UpdateOrganizationProfileDto,
} from '../dtos';
import { ApiBody, ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@igov/auth';

@ApiBearerAuth()
@ApiTags('Organization structure')
@Controller('organization-structure')
@UseGuards(JwtAuthGuard)
export class OrganizationStructureController {
  constructor(private readonly organizationStructureService: OrganizationStructureService) {}

  @Post('departments')
  @ApiOperation({ summary: 'Create Department' })
  @ApiBody({ type: CreateDepartmentDto })
  @ApiResponse({ status: 201, type: DepartmentResponseDto })
  createDepartment(@Body() dto: CreateDepartmentDto): Promise<DepartmentResponseDto> {
    return this.organizationStructureService.saveDepartment(dto);
  }

  @Get('departments/:id')
  @ApiOperation({ summary: 'Get Department by ID' })
  @ApiResponse({ status: 200, type: DepartmentResponseDto })
  getDepartment(@Param('id') id: string): Promise<DepartmentResponseDto> {
    return this.organizationStructureService.getDepartment(id);
  }

  @Get('departments')
  @ApiOperation({ summary: 'Get all Departments' })
  @ApiResponse({ status: 200, type: [DepartmentResponseDto] })
  getDepartments(): Promise<DepartmentResponseDto[]> {
    return this.organizationStructureService.getDepartments();
  }

  @Patch('departments/:id')
  @ApiOperation({ summary: 'Update Department by ID' })
  @ApiBody({ type: UpdateDepartmentDto })
  @ApiResponse({ status: 200, type: DepartmentResponseDto })
  updateDepartment(
    @Param('id') id: string,
    @Body() dto: UpdateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    return this.organizationStructureService.updateDepartment(id, dto);
  }

  @Delete('departments/:id')
  @ApiOperation({ summary: 'Delete Department by ID' })
  async deleteDepartment(@Param('id') id: string): Promise<string> {
    await this.organizationStructureService.deleteDepartment(id);
    return 'Deleted successfully';
  }

  @Post('mdas')
  @ApiOperation({ summary: 'Create MDA' })
  @ApiBody({ type: CreateMdaDto })
  @ApiResponse({ status: 201, type: MdaResponseDto })
  createMda(@Body() dto: CreateMdaDto): Promise<MdaResponseDto> {
    return this.organizationStructureService.saveMda(dto);
  }

  @Get('mdas/:id')
  @ApiOperation({ summary: 'Get MDA by ID' })
  @ApiResponse({ status: 200, type: MdaResponseDto })
  getMda(@Param('id') id: string): Promise<MdaResponseDto> {
    return this.organizationStructureService.getMda(id);
  }

  @Get('mdas')
  @ApiOperation({ summary: 'Get all MDAs' })
  @ApiResponse({ status: 200, type: [MdaResponseDto] })
  getMdas(): Promise<MdaResponseDto[]> {
    return this.organizationStructureService.getMdas();
  }

  @Patch('mdas/:id')
  @ApiOperation({ summary: 'Update MDA by ID' })
  @ApiBody({ type: UpdateMdaDto })
  @ApiResponse({ status: 200, type: MdaResponseDto })
  updateMda(@Param('id') id: string, @Body() dto: UpdateMdaDto): Promise<MdaResponseDto> {
    return this.organizationStructureService.updateMda(id, dto);
  }

  @Delete('mdas/:id')
  @ApiOperation({ summary: 'Delete MDA by ID' })
  async deleteMda(@Param('id') id: string): Promise<string> {
    await this.organizationStructureService.deleteMda(id);
    return 'Deleted successfully';
  }

  @Post('units')
  @ApiOperation({ summary: 'Create Unit' })
  @ApiBody({ type: CreateUnitDto })
  @ApiResponse({ status: 201, type: UnitResponseDto })
  createUnit(@Body() dto: CreateUnitDto): Promise<UnitResponseDto> {
    return this.organizationStructureService.saveUnit(dto);
  }

  @Get('units/:id')
  @ApiOperation({ summary: 'Get Unit by ID' })
  @ApiResponse({ status: 200, type: UnitResponseDto })
  getUnit(@Param('id') id: string): Promise<UnitResponseDto> {
    return this.organizationStructureService.getUnit(id);
  }

  @Get('units')
  @ApiOperation({ summary: 'Get all Units' })
  @ApiResponse({ status: 200, type: [UnitResponseDto] })
  getUnits(): Promise<UnitResponseDto[]> {
    return this.organizationStructureService.getUnits();
  }

  @Patch('units/:id')
  @ApiOperation({ summary: 'Update Unit by ID' })
  @ApiBody({ type: UpdateUnitDto })
  @ApiResponse({ status: 200, type: UnitResponseDto })
  updateUnit(@Param('id') id: string, @Body() dto: UpdateUnitDto): Promise<UnitResponseDto> {
    return this.organizationStructureService.updateUnit(id, dto);
  }

  @Delete('units/:id')
  @ApiOperation({ summary: 'Delete Unit by ID' })
  async deleteUnit(@Param('id') id: string): Promise<string> {
    await this.organizationStructureService.deleteUnit(id);
    return 'Deleted successfully';
  }

  @Post('positions')
  @ApiOperation({ summary: 'Create Position' })
  @ApiBody({ type: CreatePositionDto })
  @ApiResponse({ status: 201, type: PositionResponseDto })
  createPosition(@Body() dto: CreatePositionDto): Promise<PositionResponseDto> {
    return this.organizationStructureService.savePosition(dto);
  }

  @Get('positions/:id')
  @ApiOperation({ summary: 'Get Position by ID' })
  @ApiResponse({ status: 200, type: PositionResponseDto })
  getPosition(@Param('id') id: string): Promise<PositionResponseDto> {
    return this.organizationStructureService.getPosition(id);
  }

  @Get('positions')
  @ApiOperation({ summary: 'Get all Positions' })
  @ApiResponse({ status: 200, type: [PositionResponseDto] })
  getPositions(): Promise<PositionResponseDto[]> {
    return this.organizationStructureService.getPositions();
  }

  @Patch('positions/:id')
  @ApiOperation({ summary: 'Update Position by ID' })
  @ApiBody({ type: UpdatePositionDto })
  @ApiResponse({ status: 200, type: PositionResponseDto })
  updatePosition(
    @Param('id') id: string,
    @Body() dto: UpdatePositionDto,
  ): Promise<PositionResponseDto> {
    return this.organizationStructureService.updatePosition(id, dto);
  }

  @Delete('positions/:id')
  @ApiOperation({ summary: 'Delete Position by ID' })
  async deletePosition(@Param('id') id: string): Promise<string> {
    await this.organizationStructureService.deletePosition(id);
    return 'Deleted successfully';
  }

  @Post('organization-profiles')
  @ApiOperation({ summary: 'Create Organization Profile' })
  @ApiBody({ type: CreateOrganizationProfileDto })
  @ApiResponse({ status: 201, type: OrganizationProfileResponseDto })
  createOrganizationProfile(
    @Body() dto: CreateOrganizationProfileDto,
  ): Promise<OrganizationProfileResponseDto> {
    return this.organizationStructureService.saveOrganization(dto);
  }

  @Get('organization-profiles/:id')
  @ApiOperation({ summary: 'Get Organization Profile by ID' })
  @ApiResponse({ status: 200, type: OrganizationProfileResponseDto })
  getOrganizationProfile(@Param('id') id: string): Promise<OrganizationProfileResponseDto> {
    return this.organizationStructureService.getOrganization(id);
  }

  @Get('organization-profiles')
  @ApiOperation({ summary: 'Get all Organization Profiles' })
  @ApiResponse({ status: 200, type: [OrganizationProfileResponseDto] })
  getOrganizationProfiles(): Promise<OrganizationProfileResponseDto[]> {
    return this.organizationStructureService.getOrganizations();
  }

  @Patch('organization-profiles/:id')
  @ApiOperation({ summary: 'Update Organization Profile by ID' })
  @ApiBody({ type: UpdateOrganizationProfileDto })
  @ApiResponse({ status: 200, type: OrganizationProfileResponseDto })
  updateOrganizationProfile(
    @Param('id') id: string,
    @Body() dto: UpdateOrganizationProfileDto,
  ): Promise<OrganizationProfileResponseDto> {
    return this.organizationStructureService.updateOrganization(id, dto);
  }

  @Delete('organization-profiles/:id')
  @ApiOperation({ summary: 'Delete Organization Profile by ID' })
  async deleteOrganizationProfile(@Param('id') id: string): Promise<string> {
    await this.organizationStructureService.deleteOrganization(id);
    return 'Deleted successfully';
  }
}
