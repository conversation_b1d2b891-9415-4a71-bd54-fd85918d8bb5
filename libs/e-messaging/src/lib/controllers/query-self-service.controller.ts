import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IJwtPayload, JwtAuthGuard, PayloadFromJwt } from '@igov/auth';
import { QueryService } from '../services';
import {
  CreateQueryAnnouncementDto,
  PaginatedQueryResponseDto,
  QueryAnnouncementResponseDto,
  QueryFilterDto,
  QueryResponseDto,
  UpdateQueryDto,
  UpdateStatusDto,
} from '../dtos/query.dto';
import { QueryStatusEnums } from '../enums';

@ApiTags('Query Self Service')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('query-self-service')
export class QuerySelfServiceController {
  constructor(private readonly queryService: QueryService) {}
  @Get(':id')
  @ApiOperation({ summary: 'Get one query record' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  async getRecord(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<QueryResponseDto> {
    return this.queryService.getOne({ id, employeeId: jwtPayload.id });
  }
  @Post('/reply-query')
  @ApiOperation({ summary: 'Reply query/announcement' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 201, type: QueryAnnouncementResponseDto })
  async replyQuery(
    @Body() dto: CreateQueryAnnouncementDto,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
  ): Promise<QueryAnnouncementResponseDto> {
    return this.queryService.replyQuery(dto, jwtPayload.id);
  }
  @Get()
  @ApiOperation({ summary: 'Get all employees queries' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  async myQueries(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryFilterDto,
  ): Promise<PaginatedQueryResponseDto> {
    return this.queryService.getAll({
      ...query,
      issuedById: jwtPayload.id,
      employeeId: jwtPayload.id,
      draftedById: jwtPayload.id,
      useOrConditionForEmployees: true,
    });
  }

  @Get('/queries/issued')
  @ApiOperation({ summary: 'Get all employee issued' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  async issuedQueries(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryFilterDto,
  ): Promise<PaginatedQueryResponseDto> {
    return this.queryService.getAll({
      ...query,
      issuedById: jwtPayload.id,
      useOrConditionForEmployees: true,
      status: QueryStatusEnums.Open,
    });
  }
  @Get('/queries/outbox')
  @ApiOperation({ summary: 'Get all employee outbox queries' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  async outboxQueries(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryFilterDto,
  ): Promise<PaginatedQueryResponseDto> {
    return this.queryService.getAll({
      ...query,
      issuedById: jwtPayload.id,
      status: QueryStatusEnums.Draft,
      useOrConditionForEmployees: true,
    });
  }
  @Get('/queries/inbox')
  @ApiOperation({ summary: 'Get all employee inbox queries' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  async inboxQueries(
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Query() query: QueryFilterDto,
  ): Promise<PaginatedQueryResponseDto> {
    return this.queryService.getAll({
      ...query,
      employeeId: jwtPayload.id,
      status: QueryStatusEnums.Open,
      useOrConditionForEmployees: true,
    });
  }
  @Patch(':id')
  @ApiOperation({ summary: 'Update details of query' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  updateQuery(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Body() dto: UpdateQueryDto,
  ): Promise<QueryResponseDto> {
    const updatedDto = {
      ...dto,
      isSelfService: true,
    };
    return this.queryService.updateRecord({ id, data: updatedDto, employeeId: jwtPayload.id });
  }
  @Patch('status/:id')
  @ApiOperation({ summary: 'Update status of query' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiResponse({ status: 200, type: QueryResponseDto })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @PayloadFromJwt() jwtPayload: IJwtPayload,
    @Body() dto: UpdateStatusDto,
  ): Promise<QueryResponseDto> {
    const updatedDto = {
      ...dto,
      isSelfService: true,
    };
    return this.queryService.updateStatus({ id, data: updatedDto, employeeId: jwtPayload.id });
  }
}
