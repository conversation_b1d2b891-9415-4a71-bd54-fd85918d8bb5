# EC2 Module Outputs

output "instance_id" {
  description = "ID of the created EC2 instance"
  value       = aws_instance.main_server.id
}

output "public_ip" {
  description = "Public IP address of the EC2 instance"
  value       = aws_eip.server_eip.public_ip
}

output "public_dns" {
  description = "Public DNS name of the EC2 instance"
  value       = aws_instance.main_server.public_dns
}

output "security_group_id" {
  description = "ID of the security group created for the instance"
  value       = aws_security_group.server_sg.id
}

output "private_key_pem" {
  value     = tls_private_key.server_key.private_key_pem
  sensitive = true
}

output "public_key_openssh" {
  value     = tls_private_key.server_key.public_key_openssh
}

output "key_name" {
  value = aws_key_pair.generated_key.key_name
}
