import { useMemo } from 'react';
import { MRT_ColumnDef } from 'material-react-table';
import { RoomWithStatus } from '@/services/api/types/konn3ct.types';
import { Button, ListItemIcon, MenuItem, Typography } from '@mui/material';
import Paper from '@mui/material/Paper';
import DataTable from '@/components/data-table/DataTable';
import moment from 'moment';
import FuseSvgIcon from '@/@fuse/core/FuseSvgIcon';
import RoomStatus from './components/RoomStatus';

interface MeetingTableProps {
  rooms: RoomWithStatus[];
  loading: boolean;
  error: string | null;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  globalFilter: string;
  onPaginationChange: (pagination: { pageIndex: number; pageSize: number }) => void;
  onGlobalFilterChange: (filter: string) => void;
  onStartMeeting: (room: RoomWithStatus) => void;
  onJoinMeeting: (room: RoomWithStatus) => void;
  onDeleteRoom: (room: RoomWithStatus) => void;
  starting: boolean;
  joining: boolean;
  deleting: boolean;
}

function MeetingTable({
  rooms,
  loading,
  error,
  pagination,
  globalFilter,
  onPaginationChange,
  onGlobalFilterChange,
  onStartMeeting,
  onJoinMeeting,
  onDeleteRoom,
  starting,
  joining,
  deleting,
}: MeetingTableProps) {
  // Helper function to determine if room can be started
  const canStartRoom = (room: RoomWithStatus): boolean => {
    return room.status === 'available';
  };

  // Helper function to determine if room can be joined
  const canJoinRoom = (room: RoomWithStatus): boolean => {
    return room.status === 'occupied';
  };

  // Helper function to determine if room can be deleted
  const canDeleteRoom = (room: RoomWithStatus): boolean => {
    return room.status === 'available';
  };

  // Helper function to get join actions based on room status
  const getJoinActions = (room: RoomWithStatus) => {
    if (!canJoinRoom(room)) return [];
    
    return [
      {
        key: 'join-participant',
        label: 'Join as Participant',
        icon: 'heroicons-outline:users',
        handler: () => onJoinMeeting(room),
      },
    ];
  };

  const columns = useMemo<MRT_ColumnDef<RoomWithStatus>[]>(
    () => [
      {
        id: 'serialNumber',
        header: 'S/N',
        Cell: ({ row }) => {
          const serialNumber = pagination.pageIndex * pagination.pageSize + row.index + 1;
          return <Typography>{serialNumber}</Typography>;
        },
        enableColumnFilter: false,
        enableColumnDragging: false,
        size: 60,
        enableSorting: false,
      },
      {
        accessorKey: 'name',
        header: 'Room Name',
        Cell: ({ row }) => (
          <Typography className="font-medium text-primary">{row.original.name}</Typography>
        ),
        size: 200,
      },
      {
        accessorKey: 'status',
        header: 'Status',
        Cell: ({ row }) => (
          <RoomStatus status={row.original.status} size="small" />
        ),
        size: 100,
      },
      {
        accessorKey: 'max_participants',
        header: 'Max Occupants',
        Cell: ({ row }) => <Typography>{row.original.max_participants}</Typography>,
        size: 120,
      },
      {
        accessorKey: 'duration',
        header: 'Max Duration',
        Cell: ({ row }) => {
          const durationInMinutes = row.original.duration;
          const hours = Math.floor(durationInMinutes / 60);
          const minutes = durationInMinutes % 60;
          
          if (hours === 0) {
            return <Typography>{minutes} min</Typography>;
          } else if (minutes === 0) {
            return <Typography>{hours} hour{hours > 1 ? 's' : ''}</Typography>;
          } else {
            return <Typography>{hours}h {minutes}m</Typography>;
          }
        },
        size: 110,
      },
      {
        accessorKey: 'created_at',
        header: 'Created',
        Cell: ({ row }) => (
          <Typography className="text-sm">
            {moment(row.original.created_at).format('DD MMM YYYY')}
          </Typography>
        ),
        size: 120,
      },
    ],
    [pagination, starting, joining],
  );

  // Client-side filtering
  const filteredRooms = useMemo(() => {
    if (!globalFilter) return rooms;

    const searchTerm = globalFilter.toLowerCase();
    return rooms.filter(
      (room) =>
        room.name.toLowerCase().includes(searchTerm) ||
        room.status.toLowerCase().includes(searchTerm),
    );
  }, [rooms, globalFilter]);

  // Note: Pagination is handled internally by DataTable (client-side)
  // Unlike other modules that use server-side pagination with manualPagination: true

  return (
    <Paper className="flex flex-col flex-auto shadow-1 rounded-t-16 overflow-hidden rounded-b-0 w-full h-full">
      <DataTable
        columns={columns}
        data={loading ? Array(3).fill({}) : filteredRooms}
        state={{
          isLoading: loading,
          globalFilter,
          pagination: {
            pageIndex: pagination.pageIndex,
            pageSize: pagination.pageSize,
          },
        }}
        onGlobalFilterChange={onGlobalFilterChange}
        onPaginationChange={onPaginationChange}
        enableGlobalFilter
        enablePagination
        enableRowActions
        paginationDisplayMode="pages"
        muiPaginationProps={{
          rowsPerPageOptions: [10, 25, 50],
          showFirstButton: true,
          showLastButton: true,
        }}
        renderRowActionMenuItems={({ row, closeMenu }) => {
          const room = row.original;
          const actions = [];

          // Show Start Meeting only if room is available
          if (canStartRoom(room)) {
            actions.push(
              <MenuItem
                key="start"
                onClick={() => {
                  closeMenu();
                  onStartMeeting(room);
                }}
                disabled={starting || joining || deleting}
              >
                <ListItemIcon>
                  <FuseSvgIcon>heroicons-outline:play</FuseSvgIcon>
                </ListItemIcon>
                Start Meeting
              </MenuItem>
            );
          }

          // Show Join options only if room is occupied
          if (canJoinRoom(room)) {
            const joinActions = getJoinActions(room);
            joinActions.forEach((action) => {
              actions.push(
                <MenuItem
                  key={action.key}
                  onClick={() => {
                    closeMenu();
                    action.handler();
                  }}
                  disabled={starting || joining || deleting}
                >
                  <ListItemIcon>
                    <FuseSvgIcon>{action.icon}</FuseSvgIcon>
                  </ListItemIcon>
                  {action.label}
                </MenuItem>
              );
            });
          }

          // Show Delete option only if room is available
          if (canDeleteRoom(room)) {
            actions.push(
              <MenuItem
                key="delete"
                onClick={() => {
                  closeMenu();
                  onDeleteRoom(room);
                }}
                disabled={starting || joining || deleting}
              >
                <ListItemIcon>
                  <FuseSvgIcon>heroicons-outline:trash</FuseSvgIcon>
                </ListItemIcon>
                Delete Room
              </MenuItem>
            );
          }

          return actions;
        }}
      />

      {error && (
        <div className="p-16 text-center">
          <Typography color="error">{error}</Typography>
        </div>
      )}
    </Paper>
  );
}

export default MeetingTable;
