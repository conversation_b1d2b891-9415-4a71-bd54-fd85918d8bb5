import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSnackbar } from 'notistack';
import { queryApiService } from '@/services/api/queryService';
import { UpdateQueryDto, QueryResponseDto } from '@/services/api/types/query.types';

export interface EditQueryHookParams {
  title: string;
  message: string;
}

export interface UseQueryEditReturn {
  isSubmitting: boolean;
  submissionError: string | null;
  submitQuery: (params: EditQueryHookParams) => Promise<void>;
}

export function useQueryEdit(queryId: string, existingQuery?: QueryResponseDto): UseQueryEditReturn {
  const router = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const submitQuery = useCallback(
    async (params: EditQueryHookParams) => {
      setIsSubmitting(true);
      setSubmissionError(null);

      try {
        // Prepare query data - only include fields supported by UpdateQueryDto
        const updateQueryData: UpdateQueryDto = {
          title: params.title,
          message: params.message,
        };

        // Update the query
        const updatedQuery = await queryApiService.updateQuery(queryId, updateQueryData);

        enqueueSnackbar('Query updated successfully!', { variant: 'success' });

        // Navigate to the updated query
        router.push(`/portal/communication/query/${updatedQuery.id}`);
      } catch (error: any) {
        console.error('Failed to update query:', error);
        setSubmissionError(error.message || 'Failed to update query. Please try again.');
        enqueueSnackbar('Failed to update query', { variant: 'error' });
      } finally {
        setIsSubmitting(false);
      }
    },
    [queryId, enqueueSnackbar, router],
  );

  return {
    isSubmitting,
    submissionError,
    submitQuery,
  };
}