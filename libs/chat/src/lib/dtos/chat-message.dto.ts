import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsNumber, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateChatMessageDto {
  @ApiProperty()
  @IsUUID()
  @IsNotEmpty()
  senderId!: string;

  @ApiProperty()
  @IsUUID()
  @IsNotEmpty()
  receiverId!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  content!: string;
}

export class ChatMessageResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  senderId!: string;

  @ApiProperty()
  receiverId!: string;

  @ApiProperty()
  content!: string;

  @ApiProperty()
  deliveredAt?: Date;

  @ApiProperty()
  readAt?: Date;

  @ApiProperty()
  isRead?: boolean;

  @ApiProperty()
  isEdited?: boolean;

  @ApiPropertyOptional()
  createdAt?: string;

  @ApiPropertyOptional()
  updatedAt?: string;
}

export class ChatMessagePaginationDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  after?: Date;
}
