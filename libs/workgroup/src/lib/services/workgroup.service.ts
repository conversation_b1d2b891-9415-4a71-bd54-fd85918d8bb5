import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DataSource } from 'typeorm';
import {
  WorkgroupCommentsEntity,
  WorkgroupDocsEntity,
  WorkgroupEntity,
  WorkgroupHistoryEntity,
} from '../entity';
import { WorkgroupRepository } from '../repositories';
import { plainToInstance } from 'class-transformer';
import {
  AddWorkgroupDocsDto,
  AddWorkgroupExecutorDto,
  CreateWorkgroupDto,
  PaginatedWorkgroupResponseDto,
  RemoveWorkgroupDocsDto,
  UpdateWorkgroupDto,
  WorkgroupCommentCreateDto,
  WorkgroupFilterDto,
  WorkgroupResponseDto,
} from '../dtos';

@Injectable()
export class WorkgroupService {
  private readonly logger = new Logger(WorkgroupEntity.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly workgroupRepository: WorkgroupRepository,
  ) {}
  async create(
    createWorkgroupDto: CreateWorkgroupDto,
    initiatedById: string,
  ): Promise<WorkgroupResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    const { status } = createWorkgroupDto;
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const workgroup = await queryRunner.manager.save(WorkgroupEntity, createWorkgroupDto);
      await queryRunner.manager.save(WorkgroupHistoryEntity, {
        performedBy: initiatedById,
        workgroupId: workgroup.id,
        action: status,
        snapshot: {
          before: {
            title: workgroup.title,
            body: workgroup.description,
          },
        },
      });
      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, workgroup);
    } catch (e) {
      await queryRunner.rollbackTransaction();
      this.handleError(e);
      throw e;
    } finally {
      await queryRunner.release();
    }
  }
  async updateRecord(params: {
    id: string;
    data: UpdateWorkgroupDto;
    employeeId: string;
  }): Promise<WorkgroupResponseDto> {
    const { id, data, employeeId } = params;
    const existingRecord = await this.ensureRecordExists({ id });
    if (existingRecord.initiatedById !== employeeId) {
      this.logger.log(`Only the initiator can edit this workgroup.`);
      throw new UnauthorizedException('Only the initiator can edit this workgroup.');
    }
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const updatedRecord = await queryRunner.manager.save(WorkgroupEntity, {
        id,
        ...data,
      });

      await queryRunner.manager.save(WorkgroupHistoryEntity, {
        performedBy: employeeId,
        workgroupId: id,
        action: updatedRecord.status,
        snapshot: {
          before: {
            title: existingRecord.title,
            body: existingRecord.description,
          },
          after: {
            title: updatedRecord.title,
            body: updatedRecord.description,
          },
        },
      });

      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, updatedRecord);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getOne(params: { id: string }): Promise<WorkgroupResponseDto> {
    const { id } = params;
    await this.ensureRecordExists({ id });
    const record = await this.workgroupRepository.findOne({
      id,
      relations: ['initiatedBy', 'history', 'comments', 'docs'],
    });
    if (!record) {
      throw new NotFoundException(`Record with ID ${id} not found`);
    }
    return plainToInstance(WorkgroupResponseDto, record);
  }
  async getAll(query: Partial<WorkgroupFilterDto>): Promise<PaginatedWorkgroupResponseDto> {
    this.logger.log(`Get all workgroup requests: ${JSON.stringify(query, null, 2)}`);
    const { data, total } = await this.workgroupRepository.findAll(query);
    const queries = plainToInstance(WorkgroupResponseDto, data);
    return {
      total,
      data: queries,
    };
  }
  async comment(
    workgroupCommentDto: WorkgroupCommentCreateDto,
    employeeId: string,
  ): Promise<WorkgroupResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    const { workgroupId } = workgroupCommentDto;
    const record = await this.ensureRecordExists({ id: workgroupId });
    const isExecutor = record.executors?.includes(employeeId);
    const isInitiator = employeeId === record.initiatedById;

    if (!isExecutor || !isInitiator) {
      this.logger.log(`You are not allowed to comment on this workgroup.`);
      throw new UnauthorizedException('You are not allowed to comment on this workgroup.');
    }

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const comment = await queryRunner.manager.save(WorkgroupCommentsEntity, {
        employeeId,
        ...workgroupCommentDto,
      });
      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, comment);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  async addExecutor(
    dto: AddWorkgroupExecutorDto,
    initiatedById: string,
  ): Promise<WorkgroupResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    const { workgroupId, employeeId } = dto;
    const record = await this.ensureRecordExists({ id: workgroupId });
    if (initiatedById != record.initiatedById) {
      throw new UnauthorizedException('You cannot add executor to this workgroup.');
    }
    const currentExecutors = record.executors || [];
    if (currentExecutors.includes(employeeId)) {
      this.logger.log(`Executor already assigned to this workgroup.`);
      throw new ConflictException('Executor already assigned to this workgroup.');
    }
    const updatedExecutors = [...currentExecutors, employeeId];
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const updatedRecord = await queryRunner.manager.save(WorkgroupEntity, {
        id: workgroupId,
        executors: updatedExecutors,
      });
      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, updatedRecord);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  async removeExecutor(
    dto: AddWorkgroupExecutorDto,
    initiatedById: string,
  ): Promise<WorkgroupResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    const { workgroupId, employeeId } = dto;

    const record = await this.ensureRecordExists({ id: workgroupId });
    if (initiatedById !== record.initiatedById) {
      throw new UnauthorizedException('You cannot remove executor from this workgroup.');
    }
    const currentExecutors = record.executors || [];

    if (!currentExecutors.includes(employeeId)) {
      throw new ConflictException('Executor is not part of this workgroup.');
    }

    const updatedExecutors = currentExecutors.filter((executorId) => executorId !== employeeId);

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const updatedRecord = await queryRunner.manager.save(WorkgroupEntity, {
        id: workgroupId,
        executors: updatedExecutors,
      });

      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, updatedRecord);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  async addDoc(dto: AddWorkgroupDocsDto, employeeId: string): Promise<WorkgroupResponseDto> {
    const { gDriveFileId, workgroupId } = dto;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    const record = await this.ensureRecordExists({ id: workgroupId });
    try {
      await queryRunner.manager.save(WorkgroupDocsEntity, {
        gDriveFileId: gDriveFileId,
        workgroupId,
        employeeId,
      });
      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, record);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  async removeDoc(dto: RemoveWorkgroupDocsDto, employeeId: string): Promise<WorkgroupResponseDto> {
    const { id, workgroupId } = dto;
    if (!id) {
      throw new BadRequestException('Document ID is required for removal.');
    }
    const recordExist = await this.ensureRecordExists({ id: workgroupId });

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const record = await queryRunner.manager.findOne(WorkgroupDocsEntity, {
        where: { id, workgroupId, employeeId },
      });

      if (!record) {
        throw new NotFoundException('Document not found or you are not authorized to delete it.');
      }

      await queryRunner.manager.delete(WorkgroupDocsEntity, id);
      await queryRunner.commitTransaction();
      return plainToInstance(WorkgroupResponseDto, recordExist);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.handleError(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async ensureRecordExists(data: { id: string }): Promise<WorkgroupEntity> {
    const { id } = data;
    const record = await this.workgroupRepository.findOneById({ id });
    if (!record) {
      const message = `Workgroup with ID ${id} not found `;
      this.logger.warn(message);
      throw new NotFoundException(message);
    }
    return record;
  }
  private handleError(error: unknown): never {
    this.logger.error('An error occurred:', error);

    if (error instanceof BadRequestException) {
      throw new BadRequestException({
        statusCode: 400,
        message: error.message || 'Invalid input data.',
        error: 'Bad Request',
      });
    }

    if (error instanceof NotFoundException) {
      throw new NotFoundException({
        statusCode: 404,
        message: error.message || 'Resource not found.',
        error: 'Not Found',
      });
    }

    if (error instanceof ConflictException) {
      throw new ConflictException({
        statusCode: 409,
        message: error.message || 'Conflict occurred.',
        error: 'Conflict',
      });
    }

    if (error instanceof HttpException) {
      throw error;
    }

    throw new InternalServerErrorException({
      statusCode: 500,
      message: 'An unexpected error occurred. Please try again later.',
      error: 'Internal Server Error',
    });
  }
}
