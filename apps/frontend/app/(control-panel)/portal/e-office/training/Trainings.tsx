'use client';

import FusePageSimple from '@/@fuse/core/FusePageSimple';
import { styled } from '@mui/material/styles';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import TrainingTable from './TrainingTable';
import TrainingsHeader from './TrainingsHeader';
import { useTrainings } from './useTrainings';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.getContrastText(theme.palette.primary.main),
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

function Trainings() {
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));
  
  // Use the custom hook for training data management
  const trainingState = useTrainings();

  return (
    <Root
      header={<TrainingsHeader />}
      content={
        <div className="mt-4 md:px-16 px-4 mb-22">
          <TrainingTable
            data={trainingState.data}
            totalCount={trainingState.totalCount}
            loading={trainingState.loading}
            error={trainingState.error}
            pagination={trainingState.pagination}
            globalFilter={trainingState.globalFilter}
            onPaginationChange={trainingState.setPagination}
            onGlobalFilterChange={trainingState.setGlobalFilter}
            onEnroll={trainingState.enrollInTraining}
            onMarkCompleted={trainingState.markAsCompleted}
            onMarkOngoing={trainingState.markAsOngoing}
            onSubmitFeedback={trainingState.submitFeedback}
          />
        </div>
      }
      scroll={isMobile ? 'normal' : 'page'}
    />
  );
}

export default Trainings;
