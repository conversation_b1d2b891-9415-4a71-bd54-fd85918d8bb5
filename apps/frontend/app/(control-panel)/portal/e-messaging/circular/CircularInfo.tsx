import { CircularResponseDto } from '@/services/api/types/circular.types';
import clsx from 'clsx';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import moment from 'moment';
import AvatarGroup from '@mui/material/AvatarGroup';
import Avatar from '@mui/material/Avatar';
import { darken } from '@mui/material/styles';
import Tooltip from '@mui/material/Tooltip';
import { SignatureIcon } from 'lucide-react';
import CircularStatus from './CircularStatus';

type CircularInfoProps = {
  circular: CircularResponseDto;
  className?: string;
};

function CircularInfo({ circular, className }: CircularInfoProps) {
  if (!circular) {
    return null;
  }

  return (
    <div className={clsx('w-full', className)}>
      <div className="flex items-center justify-between mb-4">
        <CircularStatus status={circular.status} />
      </div>

      <Typography className="text-lg font-medium">{circular.title}</Typography>

      <Typography className="text-md mt-0.5 line-clamp-2" color="text.secondary">
        {circular.reference}
      </Typography>

      <Divider className="w-12 my-6 border-1" />

      <Typography className="flex items-center space-x-1.5 text-md" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:user
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          From: {circular.fromEmployee.firstName} {circular.fromEmployee.lastName}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <FuseSvgIcon color="disabled" size={20}>
          heroicons-outline:clock
        </FuseSvgIcon>
        <span className="whitespace-nowrap leading-none">
          Created: {moment(circular.createdAt).format('MMMM Do YYYY, h:mm a')}
        </span>
      </Typography>

      <Typography className="flex items-center space-x-1.5 text-md mt-2" color="text.secondary">
        <SignatureIcon color="#bdbdbd" className="font-light" />
        <span className="whitespace-nowrap leading-none">
          Signed By:{' '}
          {circular.status === 'Publish'
            ? `${circular.fromEmployee.firstName} ${circular.fromEmployee.lastName}`
            : 'Not yet signed'}
        </span>
      </Typography>

      <div className="flex items-center mt-5 gap-2">
        <Typography className="flex items-center space-x-1.5 text-md " color="text.secondary">
          <FuseSvgIcon color="disabled" size={20}>
            heroicons-outline:building-office-2
          </FuseSvgIcon>
          <span className="whitespace-nowrap leading-none">To Departments:</span>
        </Typography>
        <AvatarGroup max={3} className="justify-end">
          {circular.circularDepartment?.map((department) => (
            <Tooltip key={department.id} title={department.name} placement="top" arrow={true}>
              <Avatar
                key={department.id}
                sx={(theme) => ({
                  background: (theme) => darken(theme.palette.background.paper, 0.05),
                  color: theme.palette.text.secondary,
                })}
                className="avatar text-sm w-6 h-6"
                alt={department.name}
              >
                {department.name.charAt(0)}
              </Avatar>
            </Tooltip>
          ))}
        </AvatarGroup>
      </div>
    </div>
  );
}

export default CircularInfo;
