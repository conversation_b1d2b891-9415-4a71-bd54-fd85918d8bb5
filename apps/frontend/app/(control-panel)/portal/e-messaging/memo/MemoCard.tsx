import { Memo } from '@/app/(control-panel)/portal/e-messaging/memo/memo-data';
import Card from '@mui/material/Card';
import {
  Button,
  CardActions,
  CardContent,
  IconButton,
  lighten,
  ListItemIcon,
  ListItemText,
  Menu,
  Tooltip,
} from '@mui/material';
import MemoInfo from './MemoInfo';
import MemoProgress from './MemoProgress';
import Link from '@/@fuse/core/Link';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import useNavigate from '@fuse/hooks/useNavigate';
import { MouseEvent, useState } from 'react';
import MenuItem from '@mui/material/MenuItem';

type MemoCardProps = {
  memo: Memo;
};

function MemoCard({ memo }: MemoCardProps) {
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Card className="flex flex-col h-96 shadow-sm">
      <CardContent className="flex flex-col flex-auto p-4">
        <MemoInfo memo={memo} />
      </CardContent>
      <MemoProgress />
      <CardActions
        className="items-center justify-end py-4 px-4"
        sx={(theme) => ({
          backgroundColor: lighten('#fff', 0.03),
          ...theme.applyStyles('light', {
            backgroundColor: lighten('#fff', 0.4),
          }),
        })}
      >
        <div className="">
          <Tooltip title="Edit Memo" placement="top">
            <IconButton id="basic-button" aria-controls="basic-menu" aria-haspopup="true">
              <FuseSvgIcon>heroicons-outline:pencil</FuseSvgIcon>
            </IconButton>
          </Tooltip>
        </div>
        <Button
          to={`/portal/e-messaging/memo/${memo.id}`}
          component={Link}
          className="px-3"
          color="secondary"
          variant="contained"
          size="small"
          endIcon={<FuseSvgIcon size={16}>heroicons-outline:arrow-small-right</FuseSvgIcon>}
        >
          View Memo
        </Button>
      </CardActions>
    </Card>
  );
}

export default MemoCard;
