import FuseScrollbars from '@fuse/core/FuseScrollbars';
import { styled } from '@mui/material/styles';
import Divider from '@mui/material/Divider';
import { motion } from 'motion/react';
import { memo, useMemo, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import clsx from 'clsx';
import { Box, CircularProgress } from '@mui/material';
import { selectSelectedChatId, setSelectedChatId, openChatPanel } from './messengerPanelSlice';
import ContactButton from './ContactButton';
import { useChatApiData } from '../hooks/useChatApiData';

const Root = styled(FuseScrollbars)(({ theme }) => ({
  background: theme.palette.background.paper,
}));

const container = {
  show: {
    transition: {
      staggerChildren: 0.025,
    },
  },
};
const item = {
  hidden: { opacity: 0, scale: 0.6 },
  show: { opacity: 1, scale: 1 },
};

type ContactListProps = {
  className?: string;
};

/**
 * The contact list.
 */
function ContactList(props: ContactListProps) {
  const { className } = props;
  const dispatch = useAppDispatch();
  const selectedChatId = useAppSelector(selectSelectedChatId);
  const contactListScroll = useRef<HTMLDivElement>(null);
  const { chatList: chats, contacts, user, loading, startNewConversation } = useChatApiData();

  const chatListContacts = useMemo(() => {
    console.log('🔄 ContactList re-computing chatListContacts:', { 
      chatsCount: chats?.length, 
      contactsCount: contacts?.length,
      refreshCounter,
      unreadCounts: chats?.map(c => ({ id: c.id, unread: c.unreadCount }))
    });

    return contacts?.length > 0 && chats?.length > 0
      ? chats.map((_chat) => ({
          ..._chat,
          ...contacts.find((_contact) => _chat.contactIds.includes(_contact.id)),
        }))
      : [];
  }, [contacts, chats, refreshCounter]);

  const scrollToTop = () => {
    if (!contactListScroll.current) {
      return;
    }

    contactListScroll.current.scrollTop = 0;
  };

  const handleContactClick = (contactId: string) => {
    dispatch(openChatPanel());

    const chat = chats.find((chat) => chat.contactIds.includes(contactId));

    if (chat) {
      dispatch(setSelectedChatId(chat.id));
      scrollToTop();
    } else {
      // Create new conversation using the hook
      if (startNewConversation && user) {
        const newChatId = startNewConversation(contactId);
        dispatch(setSelectedChatId(newChatId));
        scrollToTop();
      }
    }
  };

  if (loading || !contacts || !chats) {
    return (
      <Box
        className="flex justify-center py-3"
        sx={{
          width: 70,
          minWidth: 70,
        }}
      >
        <CircularProgress color="secondary" />
      </Box>
    );
  }

  return (
    <Root
      className={clsx('flex shrink-0 flex-col overflow-y-auto py-2 overscroll-contain', className)}
      ref={contactListScroll}
      option={{ suppressScrollX: true, wheelPropagation: false }}
    >
      {contacts?.length > 0 && (
        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="flex flex-col shrink-0"
        >
          {chatListContacts &&
            chatListContacts.map((contact) => {
              return (
                <motion.div variants={item} key={contact.id}>
                  <ContactButton
                    contact={contact}
                    selectedChatId={selectedChatId}
                    onClick={handleContactClick}
                  />
                </motion.div>
              );
            })}
          <Divider className="mx-6 my-2" />
          {contacts.map((contact) => {
            const chatContact = chats.find((_chat) => _chat.contactIds.includes(contact.id));

            return !chatContact ? (
              <motion.div variants={item} key={contact.id}>
                <ContactButton
                  contact={contact}
                  selectedChatId={selectedChatId}
                  onClick={handleContactClick}
                />
              </motion.div>
            ) : null;
          })}
        </motion.div>
      )}
    </Root>
  );
}

export default memo(ContactList);
