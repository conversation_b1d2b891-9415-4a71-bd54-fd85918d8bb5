'use client';

import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { useQueryDetail } from '../../[...query]/useQueryDetail';
import { useParams, useRouter } from 'next/navigation';
import { styled } from '@mui/material/styles';
import FusePageSimple from '@/@fuse/core/FusePageSimple';
import EditQueryHeader from './EditQueryHeader';
import Card from '@mui/material/Card';
import { motion } from 'motion/react';
import {
  Alert,
  AlertTitle,
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useRef } from 'react';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import Box from '@mui/material/Box';
import { QueryStatusEnums } from '@/services/api/types/query.types';
import { useQueryEdit } from './useQueryEdit';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.text.primary,
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

interface QueryFormData {
  title: string;
  message: string;
}

const querySchema = z.object({
  title: z.string().min(1, 'Title is required.'),
  message: z.string().min(1, 'Message is required.'),
});

function EditQuery() {
  const editorRef = useRef<RTEEditorRef>(null);

  const {
    control,
    handleSubmit,
    setValue,
    reset: resetForm,
    formState: { errors, isSubmitting: isRHFSubmitting },
  } = useForm<QueryFormData>({
    resolver: zodResolver(querySchema),
    defaultValues: {
      title: '',
      message: '',
    },
  });

  const params = useParams();
  const router = useRouter();
  const [queryId] = params.query as string;

  const { query, loading: loadingQuery, error: queryError } = useQueryDetail(queryId);
  const { employeeDetails } = useAuth();

  const {
    submitQuery: submitQueryToApi,
    isSubmitting: isApiSubmitting,
    submissionError: apiSubmissionError,
  } = useQueryEdit(queryId, query);

  // Check if query can be edited (must be draft status and created by current user)
  const canEdit =
    query?.status === QueryStatusEnums.Draft &&
    query?.issuedByEmployee?.userId === employeeDetails?.userId;

  // Populate form with existing query data
  useEffect(() => {
    if (query && canEdit) {
      resetForm({
        title: query.title || '',
        message: query.message || '',
      });
    }
  }, [query, canEdit, resetForm]);

  // Redirect if query cannot be edited
  useEffect(() => {
    if (query && !canEdit) {
      router.push(`/portal/communication/query/${queryId}`);
    }
  }, [query, canEdit, router, queryId]);

  const handleEditorChange = (content: string) => {
    setValue('message', content, { shouldValidate: true });
  };

  const onSubmit = async (data: QueryFormData) => {
    await submitQueryToApi({
      title: data.title,
      message: data.message,
    });
  };

  return (
    <Root
      header={<EditQueryHeader />}
      content={
        <motion.div
          className="md:px-16 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, transition: { delay: 0 } }}
        >
          <Card className="p-4 sm:p-6 max-w-5-xl mb-20">
            {loadingQuery ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                <Typography>Loading query data...</Typography>
              </Box>
            ) : queryError ? (
              <Box sx={{ padding: 2 }}>
                <Typography color="error">Error loading query: {queryError}</Typography>
              </Box>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                {apiSubmissionError && (
                  <Alert severity="error" className="mb-4">
                    <AlertTitle>Query Update Failed</AlertTitle>
                    {apiSubmissionError}
                  </Alert>
                )}

                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Query Title"
                      id="title"
                      variant="outlined"
                      fullWidth
                      error={!!errors.title}
                      helperText={errors.title?.message}
                      disabled={isApiSubmitting}
                    />
                  )}
                />

                <div className="mt-2 mb-4">
                  <InputLabel required error={!!errors.message} sx={{ mb: 1 }}>
                    Query Message
                  </InputLabel>
                  <Controller
                    name="message"
                    control={control}
                    render={({ field }) => (
                      <RTEEditor
                        ref={editorRef}
                        onChange={handleEditorChange}
                        value={query?.message || ''}
                      />
                    )}
                  />
                  {errors.message && <FormHelperText error>{errors.message.message}</FormHelperText>}
                </div>

                {/* Read-only fields with grey background */}
                <TextField
                  className="mt-2 mb-4"
                  label="Reference No."
                  variant="outlined"
                  fullWidth
                  value={query?.reference || ''}
                  InputProps={{
                    readOnly: true,
                  }}
                  sx={{
                    '& .MuiInputBase-input': {
                      backgroundColor: '#f5f5f5',
                    },
                  }}
                />

                <FormControl className="mt-2 mb-4" sx={{ width: '100%' }}>
                  <InputLabel>Addressed To</InputLabel>
                  <Select
                    label="Addressed To"
                    disabled={true}
                    value={query?.employee?.userId || ''}
                    sx={{
                      backgroundColor: '#f5f5f5',
                    }}
                  >
                    <MenuItem value={query?.employee?.userId || ''}>
                      {query?.employee
                        ? `${query.employee.firstName} ${query.employee.lastName}`
                        : ''}
                    </MenuItem>
                  </Select>
                </FormControl>

                <FormControl className="mt-2 mb-4" sx={{ width: '100%' }}>
                  <InputLabel>Issued By</InputLabel>
                  <Select
                    label="Issued By"
                    disabled={true}
                    value={query?.issuedByEmployee?.userId || ''}
                    sx={{
                      backgroundColor: '#f5f5f5',
                    }}
                  >
                    <MenuItem value={query?.issuedByEmployee?.userId || ''}>
                      {query?.issuedByEmployee
                        ? `${query.issuedByEmployee.firstName} ${query.issuedByEmployee.lastName}`
                        : ''}
                    </MenuItem>
                  </Select>
                </FormControl>

                {query?.dueDate && (
                  <TextField
                    className="mt-2 mb-4"
                    label="Due Date"
                    variant="outlined"
                    fullWidth
                    value={new Date(query.dueDate).toLocaleString()}
                    InputProps={{
                      readOnly: true,
                    }}
                    sx={{
                      '& .MuiInputBase-input': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  />
                )}

                {/* Existing Attachments Display */}
                {query?.attachments && query.attachments.length > 0 && (
                  <div className="mt-2 mb-4">
                    <Typography variant="subtitle1" gutterBottom>
                      Existing Attachments
                    </Typography>
                    <div className="space-y-2">
                      {query.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 border border-gray-300 rounded"
                        >
                          <div className="flex flex-col space-y-1 flex-1">
                            <div className="flex items-center space-x-2">
                              <Typography variant="body2" className="font-medium">
                                {attachment.name}
                              </Typography>
                            </div>
                            <div className="flex items-center space-x-3">
                              {attachment.type && (
                                <Typography variant="caption" color="textSecondary">
                                  Type: {attachment.type}
                                </Typography>
                              )}
                            </div>
                          </div>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => window.open(attachment.url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-end space-x-2 mt-10">
                  <Button
                    variant="outlined"
                    onClick={() => router.push(`/portal/communication/query/${queryId}`)}
                    disabled={isApiSubmitting || isRHFSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={isRHFSubmitting || isApiSubmitting}
                  >
                    {isApiSubmitting ? (
                      <CircularProgress size={15} color="inherit" />
                    ) : (
                      'Update Query'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </Card>
        </motion.div>
      }
    />
  );
}

export default EditQuery;