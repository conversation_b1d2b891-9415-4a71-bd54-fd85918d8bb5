import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { AttachmentDto } from './memo.dto';
import { QueryStatusEnums } from '../enums';
import { EmployeeResponseDto } from '@igov/employee';
import { QueryOptionsDto } from '@igov/common';

export class CreateQueryDto {
  @ApiProperty({ description: 'Query reference number' })
  @IsDefined({ message: 'Reference number is required' })
  @IsNotEmpty({ message: 'Reference number cannot be empty' })
  @IsString({ message: 'Reference number must be a string' })
  reference!: string;

  @ApiProperty({ description: 'Title of the query' })
  @IsDefined({ message: 'Title is required' })
  @IsNotEmpty({ message: 'Title cannot be empty' })
  @IsString({ message: 'Title must be a string' })
  title!: string;

  @ApiProperty({ description: 'Message body/content of the query' })
  @IsDefined({ message: 'Message is required' })
  @IsNotEmpty({ message: 'Message cannot be empty' })
  @IsString({ message: 'Message must be a string' })
  message!: string;

  @ApiProperty({
    description: 'Query attachments (optional)',
    type: [AttachmentDto],
    required: false,
  })
  @IsOptional()
  attachments?: AttachmentDto[];

  @ApiProperty({ description: 'ID of the employee the query is addressed to' })
  @IsDefined({ message: 'Employee ID is required' })
  @IsNotEmpty({ message: 'Employee ID cannot be empty' })
  @IsUUID()
  employeeId!: string;

  @ApiProperty({ description: 'ID of the user who issued the query' })
  @IsDefined({ message: 'IssuedBy ID is required' })
  @IsNotEmpty({ message: 'IssuedBy ID cannot be empty' })
  @IsUUID()
  issuedBy!: string;

  @ApiProperty({ description: 'Due date for responding to the query (optional)', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'Due date must be a valid ISO date string' })
  dueDate?: string;
}
export class QueryHistoryDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  queryId!: string;

  @ApiProperty()
  performedBy!: string;

  @ApiProperty()
  action!: string;

  @ApiProperty({ type: Object })
  snapshot!: object;

  @ApiProperty()
  createdAt!: Date;
}

export class QueryResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  reference!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  message!: string;

  @ApiProperty({ type: [AttachmentDto], required: false })
  attachments?: AttachmentDto[];

  @ApiProperty()
  employeeId!: string;

  @ApiProperty()
  draftedBy!: string;

  @ApiProperty()
  issuedBy!: string;

  @ApiProperty({ enum: QueryStatusEnums })
  status!: QueryStatusEnums;

  @ApiProperty({ required: false, type: String, format: 'date-time' })
  dueDate?: Date;

  @ApiProperty({ type: () => EmployeeResponseDto })
  employee!: EmployeeResponseDto;

  @ApiProperty({ type: () => EmployeeResponseDto })
  draftedByEmployee!: EmployeeResponseDto;

  @ApiProperty({ type: () => EmployeeResponseDto })
  issuedByEmployee!: EmployeeResponseDto;

  @ApiProperty({ type: [QueryHistoryDto], required: false })
  history?: QueryHistoryDto[];

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class CreateQueryAnnouncementDto {
  @ApiProperty({ description: 'Comment' })
  @IsDefined({ message: 'Comment is required' })
  @IsNotEmpty({ message: 'Comment cannot be empty' })
  @IsString({ message: 'Comment must be a string' })
  comment!: string;

  @ApiProperty({
    description: 'Query attachments (optional)',
    type: AttachmentDto,
    required: false,
  })
  @IsOptional()
  attachment?: AttachmentDto;

  @ApiProperty({ description: 'ID of the query' })
  @IsDefined({ message: 'Query ID is required' })
  @IsNotEmpty({ message: 'Query ID cannot be empty' })
  @IsUUID()
  queryId!: string;
}
export class QueryAnnouncementResponseDto {
  @ApiProperty({ description: 'Comment' })
  @IsDefined({ message: 'Comment is required' })
  @IsNotEmpty({ message: 'Comment cannot be empty' })
  @IsString({ message: 'Comment must be a string' })
  comment!: string;

  @ApiProperty({
    description: 'Query attachments (optional)',
    type: [AttachmentDto],
    required: false,
  })
  @IsOptional()
  attachment?: AttachmentDto[];

  @ApiProperty({ description: 'ID of the employee' })
  @IsDefined({ message: 'Employee ID is required' })
  @IsNotEmpty({ message: 'Employee ID cannot be empty' })
  @IsUUID()
  employeeId!: string;
}

export class PaginatedQueryResponseDto {
  @ApiProperty({ description: 'Total number of queries', example: 125 })
  total!: number;

  @ApiProperty({ type: [QueryResponseDto], description: 'List of queries' })
  data!: QueryResponseDto[];
}

export class QueryFilterDto extends PartialType(QueryOptionsDto) {
  @ApiPropertyOptional({ description: 'Filter by employee ID (UUID)' })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiPropertyOptional({ description: 'Filter by Drafted by (UUID)' })
  @IsOptional()
  @IsUUID()
  draftedById?: string;

  @ApiPropertyOptional({ description: 'Filter by Issued by (UUID)' })
  @IsOptional()
  @IsUUID()
  issuedById?: string;

  @ApiPropertyOptional({ description: 'Filter by status', enum: QueryStatusEnums })
  @IsOptional()
  @IsEnum(QueryStatusEnums, { each: true })
  status?: QueryStatusEnums;

  @ApiPropertyOptional({ description: 'Search keyword (optional)' })
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  useOrConditionForEmployees = false;
}

export class UpdateQueryDto {
  @ApiPropertyOptional({ description: 'Title of the query' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Message or content of the query' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ description: 'Status of the query', enum: QueryStatusEnums })
  @IsOptional()
  @IsEnum(QueryStatusEnums)
  status?: QueryStatusEnums;

  @IsOptional()
  isSelfService = true;
}
export class UpdateStatusDto {
  @ApiPropertyOptional({ description: 'Status of the query', enum: QueryStatusEnums })
  @IsOptional()
  @IsEnum(QueryStatusEnums)
  status!: QueryStatusEnums;

  @IsOptional()
  isSelfService = true;
}
