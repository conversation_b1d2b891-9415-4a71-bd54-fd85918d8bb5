'use client';

import { lighten, styled } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import clsx from 'clsx';
import { formatDistanceToNow } from 'date-fns/formatDistanceToNow';
import { format } from 'date-fns/format';
import { useEffect, useRef, useState, useMemo, useContext } from 'react';
import InputBase from '@mui/material/InputBase';
import Paper from '@mui/material/Paper';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import Toolbar from '@mui/material/Toolbar';
import Box from '@mui/material/Box';
import { v4 as uuidv4 } from 'uuid';
import { Message } from '../types/chat.types';
import { useChatApiData } from '../hooks/useChatApiData';
import UserAvatar from './UserAvatar';
import ChatAppContext from '../contexts/ChatAppContext';
import AppContext from '@/contexts/AppContext';
import ChatMoreMenu from '../[chatId]/ChatMoreMenu';

const StyledMessageRow = styled('div')(({ theme }) => ({
  '&.contact': {
    '& .bubble': {
      backgroundColor: lighten(theme.palette.secondary.main, 0.1),
      color: theme.palette.secondary.contrastText,
      borderTopLeftRadius: 4,
      borderBottomLeftRadius: 4,
      borderTopRightRadius: 12,
      borderBottomRightRadius: 12,
    },
    '&.first-of-group': {
      '& .bubble': {
        borderTopLeftRadius: 12,
      },
    },
    '&.last-of-group': {
      '& .bubble': {
        borderBottomLeftRadius: 12,
      },
    },
  },
  '&.me': {
    paddingLeft: 36,
    '& .bubble': {
      marginLeft: 'auto',
      backgroundColor: lighten(theme.palette.primary.main, 0.1),
      color: theme.palette.primary.contrastText,
      borderTopLeftRadius: 12,
      borderBottomLeftRadius: 12,
      borderTopRightRadius: 4,
      borderBottomRightRadius: 4,
    },
    '&.first-of-group': {
      '& .bubble': {
        borderTopRightRadius: 12,
      },
    },
    '&.last-of-group': {
      '& .bubble': {
        borderBottomRightRadius: 12,
      },
    },
  },
  '&.contact + .me, &.me + .contact': {
    paddingTop: 20,
    marginTop: 20,
  },
  '&.first-of-group': {
    '& .bubble': {
      borderTopLeftRadius: 12,
      paddingTop: 8,
    },
  },
  '&.last-of-group': {
    '& .bubble': {
      borderBottomLeftRadius: 12,
      paddingBottom: 8,
    },
  },
}));

interface UnifiedChatProps {
  /** Chat ID for the conversation */
  chatId: string;
  /** Display mode - affects layout and styling */
  mode: 'fullpage' | 'sidebar';
  /** Additional CSS classes */
  className?: string;
  /** Show loading state */
  loading?: boolean;
  /** Show error state */
  error?: string | null;
}

/**
 * Unified Chat Component
 *
 * Replaces both the full-page chat and sidebar chat components with a single,
 * responsive component that can render in different modes.
 *
 * Key improvements:
 * - Single WebSocket subscription per instance
 * - Proper UUID generation for temporary messages
 * - Unified state management
 * - Mode-based responsive design
 */
function UnifiedChat(props: UnifiedChatProps) {
  const { chatId, mode, className, loading: externalLoading, error: externalError } = props;

  // Context and state management
  const { setMainSidebarOpen, setContactSidebarOpen } = useContext(ChatAppContext);
  const { webSocket } = useContext(AppContext);
  const chatRef = useRef<HTMLDivElement>(null);
  const [message, setMessage] = useState('');

  // Data management - WebSocket handling is now centralized in the hook
  const {
    chatList,
    contacts,
    user,
    loadConversation,
    activeConversation,
    startNewConversation,
    addMessage,
    loading: dataLoading,
    contactsLoading,
  } = useChatApiData();

  // Find chat and contact data
  const chat = chatList.find((chat) => chat.id === chatId);
  const contactId = chat?.contactIds?.find((id) => id !== user?.id) || chatId;
  const selectedContact = contacts.find((contact) => contact.id === contactId);

  // Determine messages source with proper fallback
  const messages = useMemo<Message[]>(() => {
    if (activeConversation.chat?.id === chatId) {
      return activeConversation.messages;
    }
    return chat?.messages || [];
  }, [chat?.messages, activeConversation.messages, activeConversation.chat?.id, chatId]);

  // Load conversation when chatId changes
  useEffect(() => {
    if (chatId && user) {
      if (chat) {
        if (loadConversation) {
          loadConversation(chatId);
        }
      } else {
        const isValidContact = contacts.find((contact) => contact.id === chatId);
        if (isValidContact && startNewConversation) {
          startNewConversation(chatId);
        }
      }
    }
  }, [chatId, loadConversation, startNewConversation, contacts, user]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messages) {
      setTimeout(scrollToBottom);
    }
  }, [messages]);

  function scrollToBottom() {
    if (!chatRef.current) return;

    chatRef.current.scrollTo({
      top: chatRef.current.scrollHeight,
      behavior: mode === 'sidebar' ? 'instant' : 'smooth',
    });
  }

  function isFirstMessageOfGroup(item: Message, i: number) {
    return i === 0 || (messages[i - 1] && messages[i - 1].senderId !== item.senderId);
  }

  function isLastMessageOfGroup(item: Message, i: number) {
    return (
      i === messages.length - 1 || (messages[i + 1] && messages[i + 1].senderId !== item.senderId)
    );
  }

  function onInputChange(ev: React.ChangeEvent<HTMLInputElement>) {
    setMessage(ev.target.value);
  }



  function onMessageSubmit(ev: React.FormEvent<HTMLFormElement>) {
    ev.preventDefault();

    if (message.trim() === '' || !user) {
      return;
    }

    try {
      if (!webSocket.isConnected) {
        console.error('Cannot send message: WebSocket not connected');
        return;
      }

      // Create message with proper UUID for temporary ID
      const newMessage: Message = {
        id: `temp-${uuidv4()}`, // Unique temporary ID
        senderId: user.id,
        receiverId: chatId,
        content: message.trim(),
        createdAt: new Date().toISOString(),
        isRead: false,
        isEdited: false,
      };

      // Add message immediately for instant feedback
      addMessage(newMessage);

      // Send via WebSocket
      webSocket.emit('chat:message', {
        receiverId: chatId,
        content: message.trim(),
      });

      setMessage('');
    } catch (error) {
      console.error(`Failed to send message (${mode}):`, error);
    }
  }

  // Show loading states
  const isLoading = externalLoading || dataLoading || contactsLoading;
  if (isLoading && !chat && !selectedContact) {
    return (
      <div className="flex items-center justify-center h-full">
        <Typography>Loading...</Typography>
      </div>
    );
  }

  // Show error states
  const hasError = externalError;
  if (hasError) {
    return (
      <div className="flex items-center justify-center h-full">
        <Typography color="error">{hasError}</Typography>
      </div>
    );
  }

  // Render based on mode
  if (mode === 'fullpage') {
    return (
      <>
        {/* Full-page header */}
        <Box
          className="w-full border-b-1"
          sx={(theme) => ({
            backgroundColor: lighten('#E8E8E8', 0.02),
            ...theme.applyStyles('light', {
              backgroundColor: lighten('#E8E8E8', 0.4),
            }),
          })}
        >
          <Toolbar className="flex items-center justify-between px-4 w-full">
            <div className="flex items-center">
              <IconButton
                aria-label="Open drawer"
                onClick={() => setMainSidebarOpen(true)}
                className="border border-divider flex lg:hidden"
              >
                <FuseSvgIcon>heroicons-outline:chat-bubble-left-right</FuseSvgIcon>
              </IconButton>
              <div
                className="flex items-center cursor-pointer"
                onClick={() => setContactSidebarOpen(contactId)}
                onKeyDown={() => setContactSidebarOpen(contactId)}
                role="button"
                tabIndex={0}
              >
                <UserAvatar className="relative mx-2" user={selectedContact} />
                <Typography color="inherit" className="text-lg font-semibold px-1">
                  {selectedContact?.name}
                </Typography>
              </div>
            </div>
            <ChatMoreMenu className="-mx-2" />
          </Toolbar>
        </Box>

        {/* Full-page chat area */}
        <div className="flex flex-auto h-full min-h-0 w-full">
          <div className={clsx('flex flex-1 z-10 flex-col relative', className)}>
            <div ref={chatRef} className="flex flex-1 flex-col overflow-y-auto">
              {messages?.length > 0 && (
                <div className="flex flex-col pt-4 px-4 pb-10">
                  {messages.map((item, i) => (
                    <StyledMessageRow
                      key={item.id || i}
                      className={clsx(
                        'flex flex-col grow-0 shrink-0 items-start justify-end relative px-4 pb-1',
                        String(item.senderId) === String(user?.id) ? 'me' : 'contact',
                        { 'first-of-group': isFirstMessageOfGroup(item, i) },
                        { 'last-of-group': isLastMessageOfGroup(item, i) },
                        i + 1 === messages.length && 'pb-18',
                      )}
                    >
                      <div
                        className={`bubble flex flex-col relative px-3 py-2 ${mode === 'fullpage' ? 'max-w-xl' : 'max-w-full'}`}
                      >
                        <Typography className="whitespace-pre-wrap text-md">
                          {item.content}
                        </Typography>
                        <Typography className="text-xs mt-1 opacity-80 text-right">
                          {item.createdAt ? format(new Date(item.createdAt), 'MMM d, h:mm a') : ''}
                        </Typography>
                      </div>
                    </StyledMessageRow>
                  ))}
                </div>
              )}
            </div>

            {/* Full-page message input */}
            {messages && (
              <Paper
                square
                component="form"
                onSubmit={onMessageSubmit}
                className="absolute border-t-1 bottom-0 right-0 left-0 py-4 px-4"
                sx={(theme) => ({
                  backgroundColor: lighten('#E8E8E8', 0.02),
                  ...theme.applyStyles('light', {
                    backgroundColor: lighten('#E8E8E8', 0.4),
                  }),
                })}
              >
                <div className="flex items-center relative">
                  <InputBase
                    autoFocus={false}
                    id="message-input"
                    className="flex-1 flex grow shrink-0 mx-2 border-2"
                    placeholder="Type your message"
                    onChange={onInputChange}
                    value={message}
                    sx={{ backgroundColor: 'background.paper' }}
                  />
                  <IconButton type="submit">
                    <FuseSvgIcon color="action">heroicons-outline:paper-airplane</FuseSvgIcon>
                  </IconButton>
                </div>
              </Paper>
            )}
          </div>
        </div>
      </>
    );
  }

  // Sidebar mode
  return (
    <Paper
      className={clsx('flex flex-col relative pb-16 shadow-sm', className)}
      sx={(theme) => ({
        background: '#E8E8E8',
      })}
    >
      <div ref={chatRef} className="flex flex-1 flex-col overflow-y-auto overscroll-contain">
        <div className="flex flex-col pt-4">
          {messages?.length > 0
            ? messages.map((item, i) => (
                <StyledMessageRow
                  key={item.id || i}
                  className={clsx(
                    'flex flex-col grow-0 shrink-0 items-start justify-end relative px-4 pb-1',
                    String(item.senderId) === String(user?.id) ? 'me' : 'contact',
                    { 'first-of-group': isFirstMessageOfGroup(item, i) },
                    { 'last-of-group': isLastMessageOfGroup(item, i) },
                    i + 1 === messages.length && 'pb-10',
                  )}
                >
                  <div className="bubble flex flex-col relative px-3 py-2 max-w-full">
                    <Typography className="whitespace-pre-wrap text-md">{item.content}</Typography>
                    <Typography className="text-xs mt-1 opacity-80 text-right">
                      {item.createdAt ? format(new Date(item.createdAt), 'MMM d, h:mm a') : ''}
                    </Typography>
                  </div>
                </StyledMessageRow>
              ))
            : null}
        </div>

        {/* Empty state for sidebar */}
        {messages?.length === 0 && (
          <div className="flex flex-col flex-1">
            <div className="flex flex-col flex-1 items-center justify-center">
              <FuseSvgIcon size={128} color="disabled">
                heroicons-outline:chat-bubble-left-right
              </FuseSvgIcon>
            </div>
            <Typography className="px-4 pb-6 text-center" color="text.secondary">
              Start a conversation by typing your message below.
            </Typography>
          </div>
        )}
      </div>

      {/* Sidebar message input */}
      {chat && (
        <form onSubmit={onMessageSubmit} className="pb-4 px-2 absolute bottom-0 left-0 right-0">
          <Paper className="flex items-center relative shadow-sm">
            <InputBase
              autoFocus={false}
              id="message-input"
              className="flex flex-1 grow shrink-0 ltr:mr-12 rtl:ml-12"
              placeholder="Type your message"
              onChange={onInputChange}
              value={message}
            />
            <IconButton className="absolute ltr:right-0 rtl:left-0" type="submit">
              <FuseSvgIcon className="" color="action">
                heroicons-outline:paper-airplane
              </FuseSvgIcon>
            </IconButton>
          </Paper>
        </form>
      )}
    </Paper>
  );
}

export default UnifiedChat;
