# EC2 Module Main Configuration

# Create Elastic IP
resource "aws_eip" "server_eip" {
  domain = "vpc"
  instance = aws_instance.main_server.id

  lifecycle {
    prevent_destroy = false  # Allow recreation
  }

  tags = {
    Name        = "${var.project_name}-${var.environment}-eip"
    Environment = var.environment
  }
}

# Security Group for the EC2 Instance
resource "aws_security_group" "server_sg" {
  name        = "${var.project_name}-${var.environment}-sg"
  description = "Security group for ${var.environment} server"
  vpc_id      = var.vpc_id

  # HTTP access
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS access
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # SSH access - consider restricting to your IP
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.ssh_allowed_cidrs
  }

  # Outbound internet access
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project_name}-${var.environment}-sg"
    Environment = var.environment
  }
}

# Fetch the latest Ubuntu AMI
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# Generate SSH Key
resource "tls_private_key" "server_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

# Create AWS Key Pair
resource "aws_key_pair" "generated_key" {
  key_name   = "${var.project_name}-${var.environment}-key"
  public_key = tls_private_key.server_key.public_key_openssh

  lifecycle {
    create_before_destroy = true
  }
}

# to only be run locally to allow ssh to ec2 instance
# resource "local_file" "private_key" {
#   content  = tls_private_key.server_key.private_key_pem
#   filename = "${path.module}/${var.project_name}-${var.environment}-key.pem"
#   file_permission = "0400"
# }

resource "aws_iam_role" "ec2_role" {
  name = "${var.project_name}-ec2-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecr_access" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
}

resource "aws_iam_instance_profile" "ec2_profile" {
  name = "${var.project_name}-ec2-profile-${var.environment}"
  role = aws_iam_role.ec2_role.name
}

# Create EC2 Instance
resource "aws_instance" "main_server" {
  ami           = data.aws_ami.ubuntu.id
  instance_type = var.instance_type
  subnet_id     = var.subnet_id

  # Security Group
  vpc_security_group_ids = [aws_security_group.server_sg.id]

  iam_instance_profile = aws_iam_instance_profile.ec2_profile.name

  # SSH Key
  key_name = aws_key_pair.generated_key.key_name

  # Root block device configuration
  root_block_device {
    volume_type = "gp3"
    volume_size = var.root_volume_size
    encrypted   = true
  }

  # User data for initial setup
  user_data = templatefile("${path.module}/templates/user_data.sh.tpl", {
    environment = var.environment
    project_name = var.project_name
  })

  # Enable detailed monitoring
  monitoring = var.enable_detailed_monitoring

  lifecycle {
    create_before_destroy = true  # Critical for zero-downtime replacement
    ignore_changes = [ami]       # Prevent unintended recreations when AMI updates
  }

  tags = {
    Name        = "${var.project_name}-${var.environment}-server"
    Environment = var.environment
  }
}

