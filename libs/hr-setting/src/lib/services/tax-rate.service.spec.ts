import { NotFoundException } from '@nestjs/common';
import { TaxRateService } from './tax-rate.service';
import { TaxRateRepository } from '../repositories';
import { CreateTaxRateDto, UpdateTaxRateDto, TaxRateResponseDto } from '../dtos';

describe('TaxRateService', () => {
  let service: TaxRateService;
  let repo: jest.Mocked<TaxRateRepository>;

  const mockTaxRate = {
    id: 'rate-1',
  } as TaxRateResponseDto;

  beforeEach(() => {
    repo = {
      save: jest.fn(),
      findOne: jest.fn(),
      findAll: jest.fn(),
      delete: jest.fn(),
    } as unknown as jest.Mocked<TaxRateRepository>;

    service = new TaxRateService(repo);
  });

  describe('create', () => {
    it('should create and return a tax rate', async () => {
      const dto = {} as CreateTaxRateDto;

      repo.save.mockResolvedValue(mockTaxRate);

      const result = await service.create(dto);

      expect(repo.save).toHaveBeenCalledWith(dto);
      expect(result).toBeInstanceOf(TaxRateResponseDto);
    });
  });

  describe('findOne', () => {
    it('should return a tax rate if found', async () => {
      repo.findOne.mockResolvedValue(mockTaxRate);

      const result = await service.findOne('rate-1');

      expect(repo.findOne).toHaveBeenCalledWith('rate-1');
      expect(result).toBeInstanceOf(TaxRateResponseDto);
    });

    it('should throw NotFoundException if not found', async () => {
      repo.findOne.mockResolvedValue(null);

      await expect(service.findOne('missing')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findAll', () => {
    it('should return all tax rates', async () => {
      repo.findAll.mockResolvedValue([mockTaxRate]);

      const result = await service.findAll();

      expect(repo.findAll).toHaveBeenCalled();
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(TaxRateResponseDto);
    });
  });

  describe('update', () => {
    it('should update and return the tax rate', async () => {
      const updateDto = {} as UpdateTaxRateDto;
      repo.findOne.mockResolvedValue(mockTaxRate);
      repo.save.mockResolvedValue({ ...mockTaxRate });

      const result = await service.update('rate-1', updateDto);

      expect(repo.findOne).toHaveBeenCalledWith('rate-1');
      expect(repo.save).toHaveBeenCalledWith({ ...mockTaxRate, ...updateDto });
      expect(result).toBeInstanceOf(TaxRateResponseDto);
    });
  });

  describe('delete', () => {
    it('should delete the tax rate if it exists', async () => {
      repo.findOne.mockResolvedValue(mockTaxRate);
      repo.delete.mockResolvedValue(undefined);

      await service.delete('rate-1');

      expect(repo.findOne).toHaveBeenCalledWith('rate-1');
      expect(repo.delete).toHaveBeenCalledWith('rate-1');
    });

    it('should throw NotFoundException if not found', async () => {
      repo.findOne.mockResolvedValue(null);

      await expect(service.delete('not-found')).rejects.toThrow(NotFoundException);
    });
  });
});
