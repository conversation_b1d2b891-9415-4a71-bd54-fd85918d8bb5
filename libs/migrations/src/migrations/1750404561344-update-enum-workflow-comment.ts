import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEnumWorkflowComment1750404561344 implements MigrationInterface {
  name = 'UpdateEnumWorkflowComment1750404561344';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "workflow_comments" DROP COLUMN "date"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "workflow_comments" ADD "date" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }
}
