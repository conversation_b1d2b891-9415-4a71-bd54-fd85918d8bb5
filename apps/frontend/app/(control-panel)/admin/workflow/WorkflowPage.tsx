'use client';

import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  Box,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Tooltip,
  CircularProgress,
  Paper,
  ListItem,
  Breadcrumbs,
  Typography,
  Link,
  ListItemText,
} from '@mui/material';
import FusePageCarded from '@fuse/core/FusePageCarded/FusePageCarded';
import { Edit, Trash } from 'lucide-react';
import { apiClient } from '@/services/api/apiClient';
import { enqueueSnackbar } from 'notistack';
import { Form, Formik, FormikHelpers, FormikProps } from 'formik';
import FuseLoading from '@fuse/core/FuseLoading';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import TextInput from '@/components/formInputs/TextInput';
import SelectInput from '@/components/formInputs/SelectInput';
import { Employee } from '../manage-employee/page';
import { Visibility } from '@mui/icons-material';

interface Workflow {
  id?: string;
  workflowTypeId?: string;
  authorizingUser?: string;
  title?: string;
  description?: string;
  amount?: number;
  status?: string;
}

export default function WorkflowPage() {
  const [workflows, setWorkflows] = useState([]);
  const [workflowTypes, setWorkflowTypes] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [addmode, setAddMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);

  const handleOpenAdd = () => {
    setEditMode(false);
    setAddMode(true);
  };

  const handleOpenEdit = (workflow) => {
    setEditMode(true);
    setCurrentWorkflow(workflow);
    setAddMode(false);
  };
  const handleOpenView = (workflow) => {
    setViewMode(true);
    setCurrentWorkflow(workflow);
    fetchSingleWorkflow(workflow.id);
  };

  const handleDelete = async (id) => {
    try {
      const confirmDelete = window.confirm(
        'Are you sure you want to delete this workflow? This action cannot be undone.',
      );
      if (!confirmDelete) {
        return;
      }
      await apiClient.delete(`/workflows/${id}`);
      enqueueSnackbar('Workflow type deleted successfully', {
        variant: 'success',
      });
      fetchWorkflows();
    } catch (error) {
      enqueueSnackbar('Attempt to delete workflow type failed', {
        variant: 'error',
      });
    } finally {
      fetchWorkflows();
    }
  };

  const handleDialogClose = () => {
    setAddMode(false);
    setEditMode(false);
    setCurrentWorkflow(null);
  };

  const fetchSingleWorkflow = async (id) => {
    setLoading(true);
    try {
      const responseData: Workflow = await apiClient.get(`/workflows/${id}`);
      setCurrentWorkflow(responseData);
    } catch (_err) {
      console.error('Failed to fetch workflow:', _err);
    } finally {
      setLoading(false);
    }
  };
  const fetchWorkflowTypes = async () => {
    try {
      const responseData: [] = await apiClient.get('/workflow-types');
      setWorkflowTypes(responseData);
    } catch (_err) {
      console.error('Failed to fetch workflow types:', _err);
    }
  };

  const fetchEmployees = async () => {
    try {
      const { employees } = await apiClient.get(`/employees`);
      const emps = employees.map((emp: Employee) => ({
        ...emp,
        name: `${emp.lastName}, ${emp.firstName}`,
      }));
      setEmployees(emps);
    } catch (_err) {
      console.error('Failed to fetch requested data:', _err);
    }
  };
  const fetchWorkflows = async () => {
    setLoading(true);
    try {
      const responseData: [] = await apiClient.get('/workflows');
      setWorkflows(responseData);
    } catch (_err) {
      console.error('Failed to fetch workflows:', _err);
    } finally {
      setLoading(false);
    }
  };
  const handleFormSubmit = async (values: Workflow, actions: FormikHelpers<Workflow>) => {
    try {
      if (editMode) {
        await apiClient.patch(`/workflows/${currentWorkflow.id}`, values);
      } else {
        await apiClient.post('/workflows', values);
      }
      enqueueSnackbar('Workflow data saved successfully', {
        variant: 'success',
      });
      fetchWorkflows();
      handleDialogClose();
    } catch (error) {
      enqueueSnackbar(
        error?.message || 'An error occurred while trying to save the data provided!',
        {
          variant: 'error',
        },
      );
    } finally {
      actions.setSubmitting(false);
    }
  };

  const tableColumns = useMemo<GridColDef<Workflow>[]>(
    () => [
      {
        field: 'number',
        headerName: '#',
        renderCell: (params) => params.api.getAllRowIds().indexOf(params.id) + 1,
      },
      { field: 'title', headerName: 'Title', flex: 1 },
      { field: 'description', headerName: 'Description', flex: 2 },
      {
        field: 'initiatedBy',
        headerName: 'Initiator',
        flex: 1,
        valueFormatter: (params: Employee) => {
          return `${params.lastName}, ${params.firstName}`;
        },
      },
      {
        field: 'actions',
        headerName: 'Actions',
        flex: 1,
        renderCell: (params) => (
          <Box display="flex" justifyContent="flex-end">
            <Tooltip title="View">
              <IconButton
                aria-label="View"
                onClick={() => handleOpenView(params.row)}
                sx={{ mr: 1 }}
              >
                <Visibility />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit">
              <IconButton
                aria-label="Edit"
                onClick={() => handleOpenEdit(params.row)}
                sx={{ mr: 1 }}
              >
                <Edit size={18} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                aria-label="Delete"
                onClick={() => handleDelete(params.row.id)}
                sx={{ color: 'error.main' }}
              >
                <Trash size={18} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [],
  );

  useEffect(() => {
    fetchWorkflows();
    fetchEmployees();
    fetchWorkflowTypes();
  }, []);

  return (
    <FusePageCarded
      header={
        <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
          <Box>
            <ListItem>
              <ListItemText
                primary={
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 'bold',
                      color: 'white',
                    }}
                  >
                    Workflow
                  </Typography>
                }
                secondary={
                  <Breadcrumbs aria-label="breadcrumb">
                    <Link underline="hover" href="/admin" color="inherit">
                      Dashboard
                    </Link>
                    <Link href="/admin/workflow" color="inherit">
                      Workflow
                    </Link>
                  </Breadcrumbs>
                }
              />
            </ListItem>
          </Box>
          <Box p={2}>
            <Button
              variant="outlined"
              sx={{
                backgroundColor: 'primary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              }}
              style={{ color: 'white' }}
              onClick={handleOpenAdd}
            >
              Add Workflow
            </Button>
          </Box>
        </Grid>
      }
      content={
        <Box p={2}>
          {loading ? (
            <FuseLoading />
          ) : (
            <Grid container spacing={2} mb={2}>
              <Grid item xs={12}>
                <Paper>
                  <DataGrid
                    rows={workflows}
                    columns={tableColumns}
                    autoHeight
                    sx={(theme) => ({
                      backgroundColor: 'transparent',
                      borderColor: theme.palette.primary.main,
                      '& .MuiDataGrid-columnHeaderRow': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeaders': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-columnHeader': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row': {
                        border: '1px solid lightgray',
                      },
                      '& .MuiDataGrid-columnHeaderTitle': {
                        fontWeight: 'bold',
                        color: theme.palette.common.white,
                      },
                      '& .MuiDataGrid-overlay': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row:first-of-type': {
                        // Changed from first-child
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-row.Mui-hovered': {
                        backgroundColor: 'transparent',
                      },
                      // Take out the hover colour
                      '& .MuiDataGrid-row:hover': {
                        backgroundColor: 'transparent',
                      },
                      '& .MuiDataGrid-cell': {
                        backgroundColor: 'transparent',
                      },
                    })}
                    getRowId={(row) => row.id}
                  />
                </Paper>
              </Grid>
            </Grid>
          )}

          <Dialog open={addmode || editMode} onClose={handleDialogClose} maxWidth="sm" fullWidth>
            <DialogTitle>{editMode ? 'Edit Workflow Type' : 'Add Workflow Type'}</DialogTitle>
            <DialogContent dividers>
              <Formik
                initialValues={{
                  workflowTypeId: currentWorkflow?.workflowTypeId || '',
                  authorizingUser: currentWorkflow?.authorizingUser || '',
                  title: currentWorkflow?.title || '',
                  description: currentWorkflow?.description || '',
                  amount: currentWorkflow?.amount || 0,
                  status: currentWorkflow?.status || 'New',
                }}
                onSubmit={handleFormSubmit}
              >
                {({ values, isSubmitting }: FormikProps<Workflow>) => {
                  return (
                    <Form>
                      <Grid container spacing={2}>
                        <Grid item xs={12} p={2}>
                          <TextInput
                            label="Workflow Title"
                            id="title"
                            value={values.title}
                            name="title"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <TextInput
                            label="Workflow Description"
                            id="description"
                            value={values.description}
                            name="description"
                            multiline
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <TextInput
                            label="Amount"
                            id="amount"
                            value={values.amount}
                            name="amount"
                            type="number"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            label="Authorizing User"
                            id="authorizingUser"
                            field="userId"
                            options={employees}
                            fieldDisplay={'workflowType'}
                            value={values.authorizingUser}
                            name="authorizingUser"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid item xs={12} p={2}>
                          <SelectInput
                            label="Workflow Type"
                            id="workflowType"
                            field="id"
                            options={workflowTypes}
                            fieldDisplay={'workflowType'}
                            value={values.workflowTypeId}
                            name="workflowTypeId"
                            fullWidth
                            required
                          />
                        </Grid>
                        <Grid
                          item
                          container
                          justifyContent={'space-between'}
                          alignItems={'center'}
                          xs={12}
                        >
                          <Button variant="outlined" onClick={handleDialogClose} type="button">
                            Cancel
                          </Button>
                          <Button variant="contained" type="submit" color="primary">
                            {isSubmitting ? <CircularProgress /> : 'Submit'}
                          </Button>
                        </Grid>
                      </Grid>
                    </Form>
                  );
                }}
              </Formik>
            </DialogContent>
          </Dialog>
        </Box>
      }
    />
  );
}
