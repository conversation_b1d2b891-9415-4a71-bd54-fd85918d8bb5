//Hmong Daw , Hmong Daw
RTE_DefaultConfig.text_language = "lus";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "Ncua tseg";	//"Cancel"
RTE_DefaultConfig.text_normal = "Li qub";	//"Normal"
RTE_DefaultConfig.text_h1 = "1 xav xwm tshiab";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Xav xwm tshiab 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Xav xwm tshiab 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Xav xwm tshiab 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "5 xav xwm tshiab";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "6 xav xwm tshiab";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "7 xav xwm tshiab";	//"Headline 7"
RTE_DefaultConfig.text_close = "kaw";	//"Close"
RTE_DefaultConfig.text_bold = "Ntawv tuab";	//"Bold"
RTE_DefaultConfig.text_italic = "Ntawv qaij";	//"Italic"
RTE_DefaultConfig.text_underline = "Kos kab hauv qab";	//"Underline"
RTE_DefaultConfig.text_strike = "Thos kab";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Qaum rooj plaub";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Rooj plaub no tsawg dua";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Tshem cov hom ntawv";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Ntxig Link";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Qhib Link";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Kho kom raug Link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Tshem cov Link";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Kab qhov siab";	//"Line Height"
RTE_DefaultConfig.text_indent = "Txav tom ntej";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Thaiv tsocai";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Sau tau txiav txim kom";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Unordered sau";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Ntxig txoj cai";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Ntxig hnub";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Ntxig rooj";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Ntxig duab";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Ntxig yees duab";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Ntxig Code";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Tsim PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Ntxig Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Cim tshwj xeeb";	//"Special characters"
RTE_DefaultConfig.text_characters = "Cim";	//"Characters"
RTE_DefaultConfig.text_fontname = "Font";	//"Font"
RTE_DefaultConfig.text_fontsize = "Raws li cov";	//"Size"
RTE_DefaultConfig.text_forecolor = "Ntawv xim";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Xim rov tom qab";	//"Back Color"
RTE_DefaultConfig.text_justify = "Hais kom ncaj ncees";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Txoj kev sab laug";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Txoj cai";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Hais kom ncaj ncees Center";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Nrhiav pov thawj kom tas";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Luj xyuas tsis";	//"Justify None"
RTE_DefaultConfig.text_delete = "Rho tawm";	//"Delete"
RTE_DefaultConfig.text_save = "Tseg cia";	//"Save file"
RTE_DefaultConfig.text_selectall = "Xaiv cov";	//"Select All"
RTE_DefaultConfig.text_code = "HTML Code";	//"HTML Code"
RTE_DefaultConfig.text_preview = "Saib ua ntej";	//"Preview"
RTE_DefaultConfig.text_print = "sau";	//"Print"
RTE_DefaultConfig.text_undo = "Undo";	//"Undo"
RTE_DefaultConfig.text_redo = "Redo";	//"Redo"
RTE_DefaultConfig.text_more = "Ntau...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Doc tshiab";	//"New Doc"
RTE_DefaultConfig.text_help = "pab";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Dhos rau qhov rai";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Tawm puv npo";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor duab";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Duab yeej";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline yeej";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Pawg lus yeej";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Txuas yeej";	//"Link Styles"
RTE_DefaultConfig.text_link = "Txuas";	//"Link"
RTE_DefaultConfig.text_style = "Yeej";	//"Styles"
RTE_DefaultConfig.text_cssclass = "CSS kev kawm";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "Ntawm qhov Url";	//"By Url"
RTE_DefaultConfig.text_upload = "Upload";	//"Upload"
RTE_DefaultConfig.text_size = "Raws li cov";	//"Size"
RTE_DefaultConfig.text_text = "Ntawv nyeem";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Qhib tshiab tab";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "ntxig";	//"Insert"
RTE_DefaultConfig.text_update = "Hloov tshiab";	//"Update"
RTE_DefaultConfig.text_find = "Xyuas thiab hloov";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "nrhiav";	//"Find"
RTE_DefaultConfig.text_replacewith = "theej";	//"Replace"
RTE_DefaultConfig.text_findnext = "lwm";	//"Next"
RTE_DefaultConfig.text_replaceonce = "theej";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Hloov tag nrho";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Me rooj plaub";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Phim lo lus";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Tsiv mus nyob";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Txav mus rau";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Nws pib me me";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% dav";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% dav";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% dav";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% dav";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Muab me me";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt ntawv nyeem";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "Hais kom ncaj ncees";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Duab npe duab";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Merge hlwb";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Split hlwb ntsug";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Split hlwb txoj";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Ntawm tes ntawv xim";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Ntawm tes xim rov tom qab";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Ntxig leej hais los saum toj";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Ntxig rau kab hauv qab";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Ntxig sab laug";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Ntxig txoj cai qhia kiag";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Rho tawm sab";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Rho tawm leej";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Rho tawm rooj";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Nws pib me me";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Rooj Header";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Ntxiv ib pawg lus tshiab";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "Muab tshuaj";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "Muab tshuaj";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Phau ntawv muab tshuaj";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Muab cov tshuaj raws li cov Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Muab tshuaj lo lus";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Thov siv CTRL + V los paste rau cov lus qhia rau hauv qab no. \r\nThe ntawv yuav nawb yeej.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "Paragraphs";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "Paragraphs";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Txav mus rau";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Tsiv mus nyob";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "Theej tawm";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "Rho tawm";	//"Delete"
RTE_DefaultConfig.text_pmore = "Ntau...";	//"More.."
RTE_DefaultConfig.text_togglemore = "Ntau...";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Toggle ciam teb";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "hlais";	//"Cut"
RTE_DefaultConfig.text_copy = "Daim ntawv";	//"Copy"
RTE_DefaultConfig.text_copied = "tau theej";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Tsab ntawv nrog thaum Gallery";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Ntxig tsab ntawv";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Ntxig Template";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "Saib ua ntej";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "Li qub";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "Txawb";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "Ntsiav tshuaj";	//"Tablet"
RTE_DefaultConfig.text_table = "rooj";	//"Table"
RTE_DefaultConfig.text_tablecell = "Rooj Txheej Txheem";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Rooj Txheej Txheem";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Rooj Sab Laj";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "Tsis siv neeg";	//"Automatic"
RTE_DefaultConfig.text_colormore = "ntau";	//"More"
RTE_DefaultConfig.text_colorpicker = "Xim Picker";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Web Palette";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Raug teev hais xim";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "Yooj yim";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "Sib ntxiv";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Luag thiab poob";	//"Drag and drop"
RTE_DefaultConfig.text_or = "lossis";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Nyem qhov upload";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Vim duab npe duab";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "Nrhiav";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Cov ntawv nyeem los yuav tau mus txog lub cim tsis pub tshaj rau daim teb no.";	//"The text to be added has reached the character limit for this field."
