import React from 'react';
import { pdfService, PDFResult } from './pdfService';
import { MemoResponseDto } from '@/services/api/types/memo.types';
import { OrganizationProfileResponseDto } from '@/services/api/types/organizationProfile.types';
import { organizationProfileApiService } from '@/services/api/organizationProfileService';
import MemoPDFGenerator from './MemoPDFGenerator';

/**
 * Memo-specific PDF service
 * Handles PDF generation for memo documents
 */
export class MemoPDFService {
  /**
   * Generates or retrieves a memo PDF
   * 
   * @param memo - The memo data
   * @param forceRegenerate - Whether to force regeneration even if PDF exists
   * @param fromEmployeeSignature - The signature of the from employee for approved memos
   * @returns Promise<PDFResult> - The PDF result with URL and blob
   */
  async getMemoPDF(
    memo: MemoResponseDto,
    forceRegenerate: boolean = false,
    fromEmployeeSignature?: string | null
  ): Promise<PDFResult> {
    try {
      // Fetch organization profile for the PDF
      let orgProfile: OrganizationProfileResponseDto | null = null;
      try {
        orgProfile = await organizationProfileApiService.getOrganizationProfile();
      } catch (error) {
        console.warn('Failed to fetch organization profile for PDF, using fallback');
      }

      // Create the PDF document component
      const documentComponent = React.createElement(MemoPDFGenerator, {
        memo,
        orgProfile,
        fromEmployeeSignature,
      });

      // Generate S3 key for this memo
      const s3Key = `memo/${memo.id}.pdf`;

      // Use the generic PDF service
      const result = await pdfService.getOrGeneratePDF({
        s3Key,
        documentComponent,
        forceRegenerate,
        metadata: {
          'memo-id': memo.id,
          'memo-title': memo.title,
          'memo-reference': memo.reference,
          'memo-status': memo.status,
          'from-employee': memo.fromEmployee?.firstName && memo.fromEmployee?.lastName
            ? `${memo.fromEmployee.firstName} ${memo.fromEmployee.lastName}`
            : 'Unknown',
        },
      });

      return result;
    } catch (error: any) {
      console.error('Memo PDF service error:', error);
      throw new Error(`Failed to generate memo PDF: ${error.message}`);
    }
  }

  /**
   * Opens a memo PDF in a new tab
   * 
   * @param memo - The memo data
   * @param forceRegenerate - Whether to force regeneration
   * @param fromEmployeeSignature - The signature of the from employee for approved memos
   */
  async openMemoPDF(
    memo: MemoResponseDto,
    forceRegenerate: boolean = false,
    fromEmployeeSignature?: string | null
  ): Promise<void> {
    try {
      const result = await this.getMemoPDF(memo, forceRegenerate, fromEmployeeSignature);
      const filename = `${memo.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      
      pdfService.openPDFInNewTab(result, filename);
    } catch (error: any) {
      console.error('Error opening memo PDF:', error);
      throw error;
    }
  }

  /**
   * Opens a memo PDF for printing
   * 
   * @param memo - The memo data
   * @param forceRegenerate - Whether to force regeneration
   * @param fromEmployeeSignature - The signature of the from employee for approved memos
   */
  async printMemoPDF(
    memo: MemoResponseDto,
    forceRegenerate: boolean = false,
    fromEmployeeSignature?: string | null
  ): Promise<void> {
    try {
      const result = await this.getMemoPDF(memo, forceRegenerate, fromEmployeeSignature);
      const filename = `${memo.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      
      pdfService.printPDF(result, filename);
    } catch (error: any) {
      console.error('Error printing memo PDF:', error);
      throw error;
    }
  }

  /**
   * Downloads a memo PDF
   * 
   * @param memo - The memo data
   * @param forceRegenerate - Whether to force regeneration
   * @param fromEmployeeSignature - The signature of the from employee for approved memos
   */
  async downloadMemoPDF(
    memo: MemoResponseDto,
    forceRegenerate: boolean = false,
    fromEmployeeSignature?: string | null
  ): Promise<void> {
    try {
      const result = await this.getMemoPDF(memo, forceRegenerate, fromEmployeeSignature);
      const filename = `${memo.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
      
      pdfService.downloadPDF(result.blob, filename);
    } catch (error: any) {
      console.error('Error downloading memo PDF:', error);
      throw error;
    }
  }

}

// Export a singleton instance
export const memoPDFService = new MemoPDFService();