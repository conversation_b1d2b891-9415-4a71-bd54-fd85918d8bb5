#!/bin/bash
# User data script for ${environment} environment

set -e  # Exit immediately if any command fails
exec > >(tee /var/log/user-data.log|logger -t user-data -s 2>/dev/console) 2>&1  # Log everything

# Update package lists and upgrade existing packages
sudo apt-get update -y
sudo apt-get upgrade -y

# Install essential tools
sudo apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    software-properties-common \
    jq \
    unzip \
    amazon-ecr-credential-helper  # Critical for ECR access

# Install Docker (official recommended way)
sudo install -m 0755 -d /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
sudo chmod a+r /etc/apt/keyrings/docker.gpg
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

sudo apt-get update -y
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Configure Docker to use ECR credential helper
sudo mkdir -p /root/.docker
sudo bash -c 'cat > /root/.docker/config.json <<EOF
{
  "credsStore": "ecr-login"
}
EOF'

# Add ubuntu user to docker group and configure
sudo usermod -aG docker ubuntu
sudo mkdir -p /home/<USER>/.docker
sudo bash -c 'cat > /home/<USER>/.docker/config.json <<EOF
{
  "credsStore": "ecr-login"
}
EOF'
sudo chown -R ubuntu:ubuntu /home/<USER>/.docker

# Configure Docker daemon
sudo bash -c 'cat > /etc/docker/daemon.json <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF'

# Enable and start Docker
sudo systemctl enable docker
sudo systemctl restart docker

# Install AWS CLI v2 (better than apt version)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
rm -rf awscliv2.zip aws

# Create directory for application
sudo mkdir -p /opt/${project_name}
sudo chown ubuntu:ubuntu /opt/${project_name}

# Create cleanup script for Docker
sudo bash -c 'cat > /usr/local/bin/docker-cleanup <<EOF
#!/bin/bash
docker system prune -af --volumes
EOF'
sudo chmod +x /usr/local/bin/docker-cleanup

# Add weekly cleanup cron job
(crontab -l 2>/dev/null; echo "0 3 * * 0 /usr/local/bin/docker-cleanup") | crontab -

# Install Certbot for Let's Encrypt
sudo apt-get install -y certbot python3-certbot-nginx

# Create directory for certificates
sudo mkdir -p /etc/letsencrypt
sudo chmod 0755 /etc/letsencrypt

# Verify installations
echo "=== Docker Version ==="
docker --version
echo "=== Docker Compose Version ==="
docker compose version
echo "=== AWS CLI Version ==="
aws --version
