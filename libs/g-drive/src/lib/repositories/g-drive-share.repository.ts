import { BaseRepository } from '@igov/common';
import { Injectable, Logger } from '@nestjs/common';
import { IGDriveShare } from '../interfaces';
import { GDriveShareEntity } from '../entities';
import { SharedItemType } from '../enums';

@Injectable()
export class GDriveShareRepository extends BaseRepository<GDriveShareEntity> {
  private readonly logger = new Logger(GDriveShareRepository.name);
  override entityClassName = GDriveShareEntity;

  async findShareByIdAndFromEmployeeId(
    id: string,
    employeeId: string,
  ): Promise<GDriveShareEntity | null> {
    return this.repository.findOne({ where: { id, fromEmployeeId: employeeId } });
  }

  async findShareByIdAndToEmployeeId(
    id: string,
    employeeId: string,
  ): Promise<GDriveShareEntity | null> {
    return this.repository.findOne({ where: { id, toEmployeeId: employeeId } });
  }

  async findIncomingShares(query: {
    toEmployeeId: string;
    skip?: number;
    limit?: number;
    sharedItemType?: SharedItemType;
  }): Promise<{ data: GDriveShareEntity[]; total: number }> {
    const { toEmployeeId, skip = 0, limit = 10, sharedItemType } = query;
    const queryBuilder = this.repository
      .createQueryBuilder('share')
      .where('share.toEmployeeId = :toEmployeeId', { toEmployeeId });
    if (sharedItemType) {
      queryBuilder.andWhere('share.sharedItemType = :sharedItemType', { sharedItemType });
    }
    queryBuilder.orderBy('share.createdAt', 'DESC');

    const total = await queryBuilder.clone().getCount();
    const data = await queryBuilder.skip(skip).take(limit).getMany();

    return { data, total };
  }

  async findOutgoingShares(query: {
    fromEmployeeId: string;
    skip?: number;
    limit?: number;
    sharedItemType?: SharedItemType;
  }): Promise<{ data: GDriveShareEntity[]; total: number }> {
    const { fromEmployeeId, skip = 0, limit = 10, sharedItemType } = query;
    const queryBuilder = this.repository
      .createQueryBuilder('share')
      .where('share.fromEmployeeId = :fromEmployeeId', { fromEmployeeId });
    if (sharedItemType) {
      queryBuilder.andWhere('share.sharedItemType = :sharedItemType', { sharedItemType });
    }
    queryBuilder.orderBy('share.createdAt', 'DESC');

    const total = await queryBuilder.clone().getCount();
    const data = await queryBuilder.skip(skip).take(limit).getMany();

    return { data, total };
  }

  async findShareByEmployeeIdAndSharedItemId(query: {
    employeeId: string;
    sharedItemId: string;
    sharedItemType: SharedItemType;
  }): Promise<GDriveShareEntity | null> {
    const { employeeId, sharedItemId, sharedItemType } = query;
    return this.repository.findOne({
      where: {
        toEmployeeId: employeeId,
        sharedItemId,
        sharedItemType,
      },
    });
  }

  async saveShare(share: Partial<IGDriveShare>): Promise<GDriveShareEntity> {
    return this.repository.save(share);
  }

  async revokeShare(id: string, employeeId: string): Promise<void> {
    await this.repository.delete({
      id,
      fromEmployeeId: employeeId,
    });
  }
}
