//Klingon (pIqaD) , Klingon (pIqaD)
RTE_DefaultConfig.text_language = "";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "";	//"Cancel"
RTE_DefaultConfig.text_normal = "";	//"Normal"
RTE_DefaultConfig.text_h1 = " ";	//"Headline 1"
RTE_DefaultConfig.text_h2 = " ";	//"Headline 2"
RTE_DefaultConfig.text_h3 = " ";	//"Headline 3"
RTE_DefaultConfig.text_h4 = " ";	//"Headline 4"
RTE_DefaultConfig.text_h5 = " ";	//"Headline 5"
RTE_DefaultConfig.text_h6 = " ";	//"Headline 6"
RTE_DefaultConfig.text_h7 = " ";	//"Headline 7"
RTE_DefaultConfig.text_close = "";	//"Close"
RTE_DefaultConfig.text_bold = "";	//"Bold"
RTE_DefaultConfig.text_italic = "";	//"Italic"
RTE_DefaultConfig.text_underline = "";	//"Underline"
RTE_DefaultConfig.text_strike = " ";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "";	//"Superscript"
RTE_DefaultConfig.text_subscript = "";	//"Subcript"
RTE_DefaultConfig.text_ucase = " ";	//"Upper Case"
RTE_DefaultConfig.text_lcase = " ";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = " ";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = " ";	//"Insert Link"
RTE_DefaultConfig.text_openlink = " ";	//"Open Link"
RTE_DefaultConfig.text_editlink = " ";	//"Edit Link"
RTE_DefaultConfig.text_unlink = " ";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = " ";	//"Line Height"
RTE_DefaultConfig.text_indent = "";	//"Indent"
RTE_DefaultConfig.text_outdent = "";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = " ";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = " ";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = " ";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "  ";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = " ";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = " ";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = " ";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = " ";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = " ";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = " ";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = " ";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = " ";	//"Special characters"
RTE_DefaultConfig.text_characters = "";	//"Characters"
RTE_DefaultConfig.text_fontname = "";	//"Font"
RTE_DefaultConfig.text_fontsize = "";	//"Size"
RTE_DefaultConfig.text_forecolor = "   ";	//"Text Color"
RTE_DefaultConfig.text_backcolor = " ";	//"Back Color"
RTE_DefaultConfig.text_justify = "";	//"Justify"
RTE_DefaultConfig.text_justifyleft = " ";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = " ";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = " ";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = " ";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = " ";	//"Justify None"
RTE_DefaultConfig.text_delete = "";	//"Delete"
RTE_DefaultConfig.text_save = " ";	//"Save file"
RTE_DefaultConfig.text_selectall = " ";	//"Select All"
RTE_DefaultConfig.text_code = " ";	//"HTML Code"
RTE_DefaultConfig.text_preview = "";	//"Preview"
RTE_DefaultConfig.text_print = "";	//"Print"
RTE_DefaultConfig.text_undo = "";	//"Undo"
RTE_DefaultConfig.text_redo = "";	//"Redo"
RTE_DefaultConfig.text_more = "";	//"More..."
RTE_DefaultConfig.text_newdoc = " ";	//"New Doc"
RTE_DefaultConfig.text_help = "";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = " ";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "  ";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = " ";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = " ";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = " ";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = " ";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = " ";	//"Link Styles"
RTE_DefaultConfig.text_link = "";	//"Link"
RTE_DefaultConfig.text_style = "";	//"Styles"
RTE_DefaultConfig.text_cssclass = " ";	//"Css Classes"
RTE_DefaultConfig.text_url = "";	//"Url"
RTE_DefaultConfig.text_byurl = " ";	//"By Url"
RTE_DefaultConfig.text_upload = "";	//"Upload"
RTE_DefaultConfig.text_size = "";	//"Size"
RTE_DefaultConfig.text_text = "  ";	//"Text"
RTE_DefaultConfig.text_opennewwin = "   ";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "";	//"Insert"
RTE_DefaultConfig.text_update = "";	//"Update"
RTE_DefaultConfig.text_find = "    ";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "";	//"Find"
RTE_DefaultConfig.text_replacewith = "  ";	//"Replace"
RTE_DefaultConfig.text_findnext = "";	//"Next"
RTE_DefaultConfig.text_replaceonce = "  ";	//"Replace"
RTE_DefaultConfig.text_replaceall = "   ";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "  ";	//"Match Case"
RTE_DefaultConfig.text_matchword = "  ";	//"Match Word"
RTE_DefaultConfig.text_move_down = "";	//"Move Down"
RTE_DefaultConfig.text_move_up = "";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = " ";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "  ";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "  ";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "  ";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "  ";	//"25% width"
RTE_DefaultConfig.text_controlsize = " ";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "   ";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "";	//"Justify"
RTE_DefaultConfig.text_imagecaption = " ";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = " ";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "  ";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "  ";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "    ";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "  ";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "  ";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "  ";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "  ";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "  ";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = " ";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = " ";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = " ";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = " ";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = " ";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "  ";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "";	//"Paste"
RTE_DefaultConfig.text_pastetext = "   ";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "  ";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = " ";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "  +           \\   ";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "";	//"Delete"
RTE_DefaultConfig.text_pmore = "";	//"More.."
RTE_DefaultConfig.text_togglemore = "";	//"More.."
RTE_DefaultConfig.text_toggleborder = " ";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "";	//"Cut"
RTE_DefaultConfig.text_copy = "";	//"Copy"
RTE_DefaultConfig.text_copied = "";	//"copied"
RTE_DefaultConfig.text_insertgallery = " ";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = " ";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = " ";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "";	//"Tablet"
RTE_DefaultConfig.text_table = "";	//"Table"
RTE_DefaultConfig.text_tablecell = " ";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = " ";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "";	//"Automatic"
RTE_DefaultConfig.text_colormore = "";	//"More"
RTE_DefaultConfig.text_colorpicker = " ";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "  ";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "  ";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "  ";	//"Drag and drop"
RTE_DefaultConfig.text_or = "";	//"or"
RTE_DefaultConfig.text_clicktoupload = " ";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "  ";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "       ";	//"The text to be added has reached the character limit for this field."
