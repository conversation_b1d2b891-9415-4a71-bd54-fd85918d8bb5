import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAnnouncementReadEntity1749276887042 implements MigrationInterface {
  name = 'UpdateAnnouncementReadEntity1749276887042';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" DROP CONSTRAINT "FK_5c268ef658d17b1b8304459d655"`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD "read_by_id" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" DROP CONSTRAINT "FK_cb77a88ef4468daa76eaadbae44"`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ALTER COLUMN "read_by" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD CONSTRAINT "FK_5c268ef658d17b1b8304459d655" FOREIGN KEY ("announcement_id") REFERENCES "announcements"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD CONSTRAINT "FK_cb77a88ef4468daa76eaadbae44" FOREIGN KEY ("read_by") REFERENCES "employees"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" DROP CONSTRAINT "FK_cb77a88ef4468daa76eaadbae44"`,
    );
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" DROP CONSTRAINT "FK_5c268ef658d17b1b8304459d655"`,
    );
    await queryRunner.query(`ALTER TABLE "announcement_reads" ALTER COLUMN "read_by" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD CONSTRAINT "FK_cb77a88ef4468daa76eaadbae44" FOREIGN KEY ("read_by") REFERENCES "employees"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "announcement_reads" DROP COLUMN "read_by_id"`);
    await queryRunner.query(`ALTER TABLE "announcement_reads" DROP COLUMN "updated_at"`);
    await queryRunner.query(`ALTER TABLE "announcement_reads" DROP COLUMN "created_at"`);
    await queryRunner.query(
      `ALTER TABLE "announcement_reads" ADD CONSTRAINT "FK_5c268ef658d17b1b8304459d655" FOREIGN KEY ("announcement_id") REFERENCES "announcements"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
