import { useState, useCallback, useEffect } from 'react';
import { announcementApiService } from '@/services/api/announcementService';
import {
  AnnouncementResponseDto,
  PaginatedAnnouncementResponseDto,
  AnnouncementQueryOptions,
} from '@/services/api/types/announcement.types';

interface UseAnnouncementsPaginationState {
  pageIndex: number;
  pageSize: number;
}

interface UseAnnouncementsState {
  data: AnnouncementResponseDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  pagination: UseAnnouncementsPaginationState;
  globalFilter: string;
}

interface UseAnnouncementsActions {
  setPagination: (
    updaterOrValue:
      | UseAnnouncementsPaginationState
      | ((old: UseAnnouncementsPaginationState) => UseAnnouncementsPaginationState),
  ) => void;
  setGlobalFilter: (filter: string) => void;
  refetch: () => Promise<void>;
}

export function useAnnouncements(): UseAnnouncementsState & UseAnnouncementsActions {
  const [state, setState] = useState<UseAnnouncementsState>({
    data: [],
    totalCount: 0,
    loading: true,
    error: null,
    pagination: { pageIndex: 0, pageSize: 10 },
    globalFilter: '',
  });

  const fetchAnnouncements = useCallback(
    async (overrides?: {
      globalFilter?: string;
      pagination?: UseAnnouncementsPaginationState;
    }) => {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const currentPagination = overrides?.pagination || state.pagination;
        const currentGlobalFilter =
          overrides?.globalFilter !== undefined ? overrides.globalFilter : state.globalFilter;

        const queryOptions: AnnouncementQueryOptions = {
          skip: currentPagination.pageIndex * currentPagination.pageSize,
          limit: currentPagination.pageSize,
          search: currentGlobalFilter || undefined,
        };

        const response: PaginatedAnnouncementResponseDto = await announcementApiService.getAnnouncements(queryOptions);

        setState((prev) => ({
          ...prev,
          data: Array.isArray(response.data) ? response.data : [],
          totalCount: response.total || 0,
          loading: false,
        }));
      } catch (error: any) {
        console.error('=== Announcements API Error ===', error);
        setState((prev) => ({
          ...prev,
          data: [],
          totalCount: 0,
          loading: false,
          error: error.message || 'Failed to fetch announcements',
        }));
      }
    },
    [state.pagination, state.globalFilter],
  );

  // Fetch data when dependencies change
  useEffect(() => {
    fetchAnnouncements();
  }, [fetchAnnouncements]);

  const setPagination = useCallback(
    (
      updaterOrValue:
        | UseAnnouncementsPaginationState
        | ((old: UseAnnouncementsPaginationState) => UseAnnouncementsPaginationState),
    ) => {
      setState((prev) => {
        const newPagination =
          typeof updaterOrValue === 'function' ? updaterOrValue(prev.pagination) : updaterOrValue;

        return {
          ...prev,
          pagination: newPagination,
          loading: true, // Set loading when pagination changes
        };
      });
    },
    [],
  );

  const setGlobalFilter = useCallback(
    (filter: string) => {
      const newPagination = { ...state.pagination, pageIndex: 0 }; // Reset to first page when searching

      setState((prev) => ({
        ...prev,
        globalFilter: filter,
        pagination: newPagination,
        loading: true, // Set loading when global filter changes
      }));

      // Immediately fetch with the new filter value
      fetchAnnouncements({
        globalFilter: filter,
        pagination: newPagination,
      });
    },
    [fetchAnnouncements, state.pagination],
  );

  const refetch = useCallback(async () => {
    await fetchAnnouncements();
  }, [fetchAnnouncements]);

  return {
    ...state,
    setPagination,
    setGlobalFilter,
    refetch,
  };
}