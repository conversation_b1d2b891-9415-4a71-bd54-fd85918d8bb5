import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WorkflowTypeService } from '../services';
import { createWorkflowTypeDto, updateWorkflowTypeDto, WorkflowTypeResponseDto } from '../dtos';
import { JwtAuthGuard } from '@igov/auth';

@ApiTags('Workflow - Admin')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workflow-types')
export class WorkflowTypeController {
  constructor(private readonly workflowTypeService: WorkflowTypeService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new workflow type' })
  @ApiResponse({ status: 201, type: WorkflowTypeResponseDto })
  createWorkflowType(@Body() dto: createWorkflowTypeDto): Promise<WorkflowTypeResponseDto> {
    return this.workflowTypeService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all workflow types' })
  @ApiResponse({ status: 200, type: [WorkflowTypeResponseDto] })
  getAllWorkflowTypes(): Promise<WorkflowTypeResponseDto[]> {
    return this.workflowTypeService.findAll();
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get a single workflow type by ID' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: 200, type: WorkflowTypeResponseDto })
  getWorkflowType(@Param('id') id: string): Promise<WorkflowTypeResponseDto> {
    return this.workflowTypeService.findOne(id);
  }

  @Patch('/:id')
  @ApiOperation({ summary: 'Update a workflow type by ID' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: 200, type: WorkflowTypeResponseDto })
  updateWorkflowType(
    @Param('id') id: string,
    @Body() dto: updateWorkflowTypeDto,
  ): Promise<WorkflowTypeResponseDto> {
    return this.workflowTypeService.update(id, dto);
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a workflow type by ID' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({ status: 204 })
  async deleteWorkflowType(@Param('id') id: string): Promise<string> {
    await this.workflowTypeService.delete(id);
    return 'Workflow type deleted successfully';
  }
}
