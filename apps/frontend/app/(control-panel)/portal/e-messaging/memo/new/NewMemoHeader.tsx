'use client';

import PageBreadcrumb from '@/components/PageBreadcrumb';
import Typography from '@mui/material/Typography';
import { motion } from 'motion/react';
import { Button } from '@mui/material';
import { useRouter } from 'next/navigation';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';

function NewMemoHeader() {
  const router = useRouter();
  const handleGoBack = () => {
    router.push('/portal/e-messaging/memo');
  };

  return (
    <div className="flex flex-col sm:flex-row flex-1 w-full items-center justify-between space-y-2 sm:space-y-0 py-6 sm:py-8 md:px-16 px-4">
      <div className="flex flex-col items-start space-y-2 sm:space-y-0 w-full sm:max-w-full min-w-0">
        <motion.div
          initial={{
            x: 20,
            opacity: 0,
          }}
          animate={{
            x: 0,
            opacity: 1,
            transition: { delay: 0.3 },
          }}
        >
          <PageBreadcrumb className="mb-2" />
        </motion.div>

        <div className="flex items-center max-w-full space-x-3">
          <motion.div
            className="hidden sm:flex"
            initial={{ scale: 0 }}
            animate={{ scale: 1, transition: { delay: 0.3 } }}
          >
            <img
              className="w-8 sm:w-12 rounded-sm"
              src="/assets/images/icons/create-edit-memo.png"
              alt={'create edit memo'}
            />
          </motion.div>
          <motion.div
            className="flex flex-col min-w-0"
            initial={{ x: -20 }}
            animate={{ x: 0, transition: { delay: 0.3 } }}
          >
            <Typography className="text-lg sm:text-2xl truncate font-semibold" color="textPrimary">
              New Memo
            </Typography>
            <Typography variant="caption" className="font-medium" color="textSecondary">
              Fill in the form below to create a new memo.
            </Typography>
          </motion.div>
        </div>
      </div>

      <div>
        <Button
          className="whitespace-nowrap mx-1"
          variant="contained"
          color="secondary"
          startIcon={<FuseSvgIcon size={20}>heroicons-outline:arrow-left-circle</FuseSvgIcon>}
          onClick={handleGoBack}
        >
          Go Back
        </Button>
      </div>
    </div>
  );
}

export default NewMemoHeader;
