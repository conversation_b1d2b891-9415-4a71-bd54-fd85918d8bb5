import { apiClient } from './apiClient';
import { UnitResponseDto, CreateUnitDto, UpdateUnitDto } from './types/unit.types';

/**
 * Service for Unit related API calls within the organization structure.
 */
class UnitApiService {
  private baseUrl = '/organization-structure/units'; // Standard base URL for units

  /**
   * Get a single unit by its ID.
   *
   * @param id - The ID of the unit to retrieve.
   * @returns Promise with the unit data.
   */
  async getUnitById(id: string): Promise<UnitResponseDto> {
    if (!id) {
      throw new Error('Unit ID is required');
    }
    try {
      return await apiClient.get<UnitResponseDto>(`${this.baseUrl}/${id}`);
    } catch (error: any) {
      console.error(`Failed to get unit with id ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create a new unit.
   *
   * @param createUnitDto - The data for creating the new unit.
   * @returns Promise with the created unit data.
   */
  async createUnit(createUnitDto: CreateUnitDto): Promise<UnitResponseDto> {
    try {
      return await apiClient.post<UnitResponseDto>(this.baseUrl, createUnitDto);
    } catch (error: any) {
      console.error('Failed to create unit:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        requestData: createUnitDto,
      });
      throw error;
    }
  }

  /**
   * Update an existing unit by its ID.
   *
   * @param id - The ID of the unit to update.
   * @param updateUnitDto - The data for updating the unit.
   * @returns Promise with the updated unit data.
   */
  async updateUnit(
    id: string,
    updateUnitDto: UpdateUnitDto,
  ): Promise<UnitResponseDto> {
    if (!id) {
      throw new Error('Unit ID is required for update');
    }
    try {
      return await apiClient.patch<UnitResponseDto>(
        `${this.baseUrl}/${id}`,
        updateUnitDto,
      );
    } catch (error: any) {
      console.error(`Failed to update unit with id ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        requestData: updateUnitDto,
      });
      throw error;
    }
  }

  /**
   * Delete a unit by its ID.
   *
   * @param id - The ID of the unit to delete.
   * @returns Promise that resolves when the unit is deleted.
   */
  async deleteUnit(id: string): Promise<void> {
    if (!id) {
      throw new Error('Unit ID is required for deletion');
    }
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`);
    } catch (error: any) {
      console.error(`Failed to delete unit with id ${id}:`, {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      throw error;
    }
  }

  // Add other methods like getAllUnits if needed, e.g.:
  // async getAllUnits(queryParams?: any): Promise<UnitResponseDto[]> {
  //   try {
  //     return await apiClient.get<UnitResponseDto[]>(this.baseUrl, { params: queryParams });
  //   } catch (error: any) {
  //     console.error('Failed to get all units:', {
  //       message: error.message,
  //       status: error.response?.status,
  //       data: error.response?.data,
  //     });
  //     throw error;
  //   }
  // }
}

// Export a single instance for easy import
export const unitApiService = new UnitApiService();
