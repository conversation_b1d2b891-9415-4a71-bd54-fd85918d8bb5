'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  ListItem,
  ListItemText,
} from '@mui/material';
import { Radio, RadioChangeEvent, Tabs } from 'antd';
import FusePageCarded from '@fuse/core/FusePageCarded';
import CustomFilterPanel from '@/components/data-table/CustomFilterPanel';
import SelectInput from '@/components/formInputs/SelectInput';
import { Form, Formik } from 'formik';
import { Employee } from '../manage-employee/page';
import { enqueueSnackbar } from 'notistack';
import { apiClient } from '@/services/api/apiClient';
import { from } from 'stylis';

// Placeholder types for folder, file, user, share
type Folder = {
  id: number;
  folderName: string;
  parentId: number;
  userId: string;
  createdDate: string;
};

type File = {
  id: number;
  fileName: string;
  userId: string;
  folderId: number;
  readDate: string | null;
  archive: boolean;
};

export default function GDrivepage() {
  const [selectedUser, setSelectedUser] = useState<Employee | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);

  const [mode, setMode] = useState<string>('folders');

  const [folders, setFolders] = useState<Folder[]>([]);
  const [subFolders, setSubFolders] = useState<Folder[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [folderPagination, setFolderPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [subfolderPagination, setSubFolderPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [filePagination, setFilePagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });

  const [currentFolderId, setCurrentFolderId] = useState<number>(0); // 0 means root

  // Search filters state
  const [filters, setFilters] = useState<{ fileName?: string; date?: string }>({});

  // Dialog states for rename, share, delete
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameTarget, setRenameTarget] = useState<{
    type: 'file' | 'folder';
    id: number;
    currentName: string;
  } | null>(null);
  const [newName, setNewName] = useState('');

  const fetchEmployees = async () => {
    try {
      const { employees }: any = await apiClient.get('/employees');
      setEmployees(employees ?? []);
    } catch (error) {
      enqueueSnackbar('Failed to fetch employees', { variant: 'error' });
    }
  };
  const fetchFolders = async (searchString?: string) => {
    try {
      let url = `/g-drive/folders/root?skip=${(folderPagination.page - 1) * folderPagination.pageSize}&limit=${folderPagination.pageSize}`;
      if (searchString) {
        url += `&search=${encodeURIComponent(searchString)}`;
      }
      const { data }: any = await apiClient.get(url);
      setFolders(data || []);
    } catch (err) {
      enqueueSnackbar('Failed to fetch folders', { variant: 'error' });
    }
  };
  const fetchSubFolders = async (parentId: string, searchString?: string) => {
    try {
      let url = `/g-drive/folders/subfolders?parentId=${parentId}&skip=${(subfolderPagination.page - 1) * subfolderPagination.pageSize}&limit=${subfolderPagination.pageSize}`;
      if (searchString) {
        url += `&search=${encodeURIComponent(searchString)}`;
      }
      const { data }: any = await apiClient.get(url);
      setSubFolders(data || []);
    } catch (err) {
      enqueueSnackbar('Failed to fetch subfolders', { variant: 'error' });
    }
  };
  const fetchFilesByFolderId = async (folderId: string, searchString?: string) => {
    try {
      let url = `/g-drive/files/${folderId}?skip=${(filePagination.page - 1) * filePagination.pageSize}&limit=${filePagination.pageSize}`;
      if (searchString) {
        url += `&search=${encodeURIComponent(searchString)}`;
      }
      const { data }: any = await apiClient.get(url);
      setFiles(data || []);
    } catch (err) {
      enqueueSnackbar('Failed to fetch subfolders', { variant: 'error' });
    }
  };
  // Placeholder: Fetch users for admin to select
  useEffect(() => {
    fetchEmployees();
  }, []);

  // Placeholder: Fetch folders and files for selected user and current folder
  useEffect(() => {
    if (mode === 'folders') {
      fetchFolders();
    } else {
      fetchFolders();
    }
  }, [mode]);

  // Handlers
  const handleUserChange = (val) => {
    const user = employees.find((u) => u.userId === val) || null;
    setSelectedUser(user);
    setCurrentFolderId(0);
  };

  const handleFolderClick = (folderId: number) => {
    setCurrentFolderId(folderId);
  };

  const handleRenameOpen = (type: 'file' | 'folder', id: number, currentName: string) => {
    setRenameTarget({ type, id, currentName });
    setNewName(currentName);
    setRenameDialogOpen(true);
  };

  const handleModeChange = (e: RadioChangeEvent) => {
    setMode(e.target.value);
  };
  const handleRenameSave = () => {
    if (!renameTarget) return;
    // TODO: Call API to rename file or folder
    // Update local state accordingly
    setRenameDialogOpen(false);
  };

  const handleDelete = (type: 'file' | 'folder', id: number) => {
    // TODO: Call API to delete file or folder
    // Update local state accordingly
  };

  const handleShare = (fileId: number) => {
    // TODO: Implement share dialog and API call
  };

  const handleFilterApply = (values: any) => {
    setFilters(values);
  };

  return (
    <>
      <FusePageCarded
        header={
          <Grid container justifyContent="space-between" alignItems="center" sx={{ paddingX: 2 }}>
            <Box>
              <ListItem>
                <ListItemText
                  primary={
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 'bold',
                        color: 'white',
                      }}
                    >
                      G-Drive
                    </Typography>
                  }
                />
              </ListItem>
            </Box>
          </Grid>
        }
        content={
          <Grid container spacing={2} sx={{ padding: 2 }}>
            <Grid item xs={12}>
              <Radio.Group onChange={handleModeChange} value={mode} style={{ marginBottom: 8 }}>
                <Radio.Button value="folders">Folder</Radio.Button>
                <Radio.Button value="files">Files</Radio.Button>
              </Radio.Group>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ mb: 2, maxWidth: 300 }}>
                <Formik
                  initialValues={{ selectedUser: selectedUser?.id || '' }}
                  onSubmit={() => {}}
                >
                  {({ values, setFieldValue }) => (
                    <Form>
                      <SelectInput
                        label="Select User"
                        name="selectedUser"
                        id="selectUser"
                        field="userId"
                        fieldDisplay="name"
                        value={selectedUser?.userId || ''}
                        customChange={handleUserChange}
                        options={employees.map((emp) => ({
                          ...emp,
                          name: `${emp.lastName} ${emp.firstName} ${emp.otherNames || ''}`,
                        }))}
                        fullWidth
                      />
                    </Form>
                  )}
                </Formik>
              </Box>

              <CustomFilterPanel
                filterFields={[
                  { label: 'File Name', filterFieldName: 'fileName', fieldType: 'text' },
                  { label: 'Date', filterFieldName: 'date', fieldType: 'date' },
                ]}
                onApplyFilters={handleFilterApply}
              />

              <Box sx={{ mb: 2 }}>
                <Typography variant="h6">Folders</Typography>
                {folders.length === 0 && <Typography>No folders in this directory.</Typography>}
                <ul>
                  {folders.map((folder) => (
                    <li key={folder.id}>
                      <Button onClick={() => handleFolderClick(folder.id)}>
                        {folder.folderName}
                      </Button>
                      <Button
                        onClick={() => handleRenameOpen('folder', folder.id, folder.folderName)}
                      >
                        Rename
                      </Button>
                      <Button onClick={() => handleDelete('folder', folder.id)}>Delete</Button>
                    </li>
                  ))}
                </ul>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="h6">Files</Typography>
                {files.length === 0 && <Typography>No files in this directory.</Typography>}
                <ul>
                  {files.map((file) => (
                    <li key={file.id}>
                      <a href="#" onClick={() => alert('Download file: ' + file.fileName)}>
                        {file.fileName}
                      </a>
                      <Button onClick={() => handleRenameOpen('file', file.id, file.fileName)}>
                        Rename
                      </Button>
                      <Button onClick={() => handleDelete('file', file.id)}>Delete</Button>
                      <Button onClick={() => handleShare(file.id)}>Share</Button>
                    </li>
                  ))}
                </ul>
              </Box>

              {/* Rename Dialog */}
              <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)}>
                <DialogTitle>Rename {renameTarget?.type}</DialogTitle>
                <DialogContent>
                  <TextField
                    autoFocus
                    margin="dense"
                    label="New Name"
                    fullWidth
                    value={newName}
                    onChange={(e) => setNewName(e.target.value)}
                  />
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setRenameDialogOpen(false)}>Cancel</Button>
                  <Button onClick={handleRenameSave} variant="contained" color="primary">
                    Save
                  </Button>
                </DialogActions>
              </Dialog>
            </Grid>
          </Grid>
        }
        scroll="content"
        classes={{
          root: 'flex',
          header: 'min-h-72 h-72 sm:h-136',
          content: 'flex flex-col flex-auto',
        }}
      />
      {/* <AdminDashboard>
        <AdminDashboardHeader />
        <Root>
          <Typography variant="h4" gutterBottom>
            GDrive Admin Panel
          </Typography>

          <Box sx={{ mb: 2, maxWidth: 300 }}>
            <SelectInput
              label="Select User"
              name="selectedUser"
              value={selectedUser?.id || ''}
              onChange={handleUserChange}
              options={users.map((user) => ({ label: user.name, value: user.id }))}
              fullWidth
            />
          </Box>

          <CustomFilterPanel
            filterFields={[
              { label: 'File Name', filterFieldName: 'fileName', fieldType: 'text' },
              { label: 'Date', filterFieldName: 'date', fieldType: 'date' },
            ]}
            onApplyFilters={handleFilterApply}
          />

          <Box sx={{ mb: 2 }}>
            <Typography variant="h6">Folders</Typography>
            {folders.length === 0 && <Typography>No folders in this directory.</Typography>}
            <ul>
              {folders.map((folder) => (
                <li key={folder.id}>
                  <Button onClick={() => handleFolderClick(folder.id)}>{folder.folderName}</Button>
                  <Button onClick={() => handleRenameOpen('folder', folder.id, folder.folderName)}>
                    Rename
                  </Button>
                  <Button onClick={() => handleDelete('folder', folder.id)}>Delete</Button>
                </li>
              ))}
            </ul>
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="h6">Files</Typography>
            {files.length === 0 && <Typography>No files in this directory.</Typography>}
            <ul>
              {files.map((file) => (
                <li key={file.id}>
                  <a href="#" onClick={() => alert('Download file: ' + file.fileName)}>
                    {file.fileName}
                  </a>
                  <Button onClick={() => handleRenameOpen('file', file.id, file.fileName)}>
                    Rename
                  </Button>
                  <Button onClick={() => handleDelete('file', file.id)}>Delete</Button>
                  <Button onClick={() => handleShare(file.id)}>Share</Button>
                </li>
              ))}
            </ul>
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="h6">Upload Files</Typography>
            <FileUpload
              files={uploadFiles}
              onFileSelect={handleFileUploadSelect}
              onFileRemove={handleFileRemove}
            />
            {uploadFiles.length > 0 && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleUploadSubmit}
                sx={{ mt: 2 }}
              >
                Upload
              </Button>
            )}
          </Box>

          <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)}>
            <DialogTitle>Rename {renameTarget?.type}</DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                label="New Name"
                fullWidth
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setRenameDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleRenameSave} variant="contained" color="primary">
                Save
              </Button>
            </DialogActions>
          </Dialog>
        </Root>
      </AdminDashboard> */}
    </>
  );
}
