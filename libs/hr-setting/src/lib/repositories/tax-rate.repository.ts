import { Injectable, Logger } from '@nestjs/common';
import { BaseRepository } from '@igov/common';
import { TaxRate } from '../entities';
import { ITaxRate } from '../interfaces';

@Injectable()
export class TaxRateRepository extends BaseRepository<TaxRate> {
  private readonly logger = new Logger(TaxRateRepository.name);
  override entityClassName = TaxRate;

  findOne(id: string): Promise<TaxRate | null> {
    return this.repository.findOne({ where: { id } });
  }

  save(rate: ITaxRate): Promise<TaxRate> {
    return this.repository.save(rate);
  }

  findAll(): Promise<TaxRate[]> {
    return this.repository.find();
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}
