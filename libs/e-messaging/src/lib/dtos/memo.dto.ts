import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { MemoActionEnum, MemoStatusEnums } from '../enums';
import { IsDefined, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { IAttachment } from '../interfaces';
import { QueryOptionsDto } from '@igov/common';
import { EmployeeResponseDto } from '@igov/employee';

export class MemoCommentDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  comment!: string;

  @ApiProperty({ type: () => EmployeeResponseDto, description: 'Commenter Employee' })
  commenter!: EmployeeResponseDto;

  @ApiProperty()
  createdAt!: Date;
}

export class MemoCommentCreateDto {
  @ApiProperty({ description: 'Comment body' })
  @IsDefined({ message: 'Comment is required' })
  @IsNotEmpty({ message: 'Comment is required' })
  comment!: string;
}

export class MemoHistoryDto {
  @ApiProperty()
  id!: string;

  @ApiProperty({ enum: MemoActionEnum })
  action!: MemoActionEnum;

  @ApiProperty()
  memoId!: string;

  @ApiProperty()
  snapshot!: object;

  @ApiProperty()
  performedBy!: string;

  @ApiProperty({ type: () => EmployeeResponseDto, description: 'From Employee' })
  employee?: EmployeeResponseDto;

  @ApiProperty()
  createdAt!: Date;
}

export class QueryMemoDto extends PartialType(QueryOptionsDto) {
  @ApiPropertyOptional({ description: 'Search keyword (title, content, etc.)' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by memo status', enum: MemoStatusEnums })
  @IsOptional()
  @IsEnum(MemoStatusEnums, { each: true })
  status?: MemoStatusEnums[];

  @ApiPropertyOptional({ description: 'User ID of the creator' })
  @IsOptional()
  @IsString()
  createdById?: string;

  @ApiPropertyOptional({ description: 'User ID of the From' })
  @IsOptional()
  @IsUUID()
  fromEmployeeId?: string;

  @ApiPropertyOptional({ description: 'Recepients Ids' })
  @IsOptional()
  @IsUUID('all', { each: true })
  toEmployeeIds?: string[];

  @ApiPropertyOptional({ description: 'Through Receipients IDs' })
  @IsOptional()
  @IsUUID('all', { each: true })
  throughEmployeeIds?: string[];

  @ApiPropertyOptional({ description: 'Start date for filtering' })
  @IsOptional()
  @IsString()
  fromDate?: Date;

  @ApiPropertyOptional({ description: 'End date for filtering' })
  @IsOptional()
  @IsString()
  toDate?: Date;

  @IsOptional()
  useOrConditionForEmployees = false;
}

export class PaginatedMemoResponseDto {
  @ApiProperty({ type: () => [MemoResponseDto], description: 'List of memos' })
  data!: MemoResponseDto[];

  @ApiProperty({ description: 'Total number of memos' })
  total!: number;
}

export class CreateMemoDto {
  @ApiProperty({ description: 'Title of the memo' })
  @IsDefined({ message: 'Title is required' })
  @IsNotEmpty({ message: 'Title cannot be empty' })
  @IsString({ message: 'Title must be a string' })
  title!: string;

  @ApiProperty({ description: 'Body/content of the memo' })
  @IsDefined({ message: 'Content is required' })
  @IsNotEmpty({ message: 'Content cannot be empty' })
  @IsString({ message: 'Content must be a string' })
  body!: string;

  @ApiProperty({ description: 'Memo reference number' })
  @IsDefined({ message: 'Reference number is required' })
  @IsNotEmpty({ message: 'Reference number cannot be empty' })
  @IsString({ message: 'Reference number must be a string' })
  reference!: string;

  @ApiProperty({ description: 'Memo attachments' })
  @IsOptional()
  attachments?: AttachmentDto[];

  @ApiProperty({ description: 'ID of the sender (from user)' })
  @IsDefined({ message: 'ID of the sender is required' })
  @IsNotEmpty({ message: 'ID of the sender is required' })
  @IsUUID()
  createdBy!: string;

  @ApiProperty({ description: 'ID of the from sender (from user)' })
  @IsDefined({ message: 'ID of the from sender is required' })
  @IsNotEmpty({ message: 'ID of the from sender is required' })
  @IsUUID()
  fromEmployeeId!: string;

  @ApiProperty({ type: [String], description: 'IDs of the users the memo is addressed to' })
  @IsDefined({ message: 'IDs of the users of the memo is addressed to is required' })
  @IsNotEmpty({ message: 'IDs of the users of the memo is addressed to is required' })
  toEmployees!: string[];

  @ApiPropertyOptional({ type: [String], description: 'IDs of the users the memo passes through' })
  @IsOptional()
  throughEmployees?: string[];
}

export class AttachmentDto {
  @ApiProperty({ description: 'Actual file name' })
  @IsNotEmpty({ message: 'Provide file name' })
  name!: string;

  @ApiProperty({ description: 'File url' })
  @IsNotEmpty({ message: 'File url' })
  url!: string;

  @ApiProperty({ description: 'File type' })
  @IsNotEmpty({ message: 'Indicate file type' })
  type?: string;
}

export class MemoResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  body!: string;

  @ApiProperty()
  attachments?: IAttachment[];

  @ApiProperty({ enum: MemoStatusEnums, description: 'Staus of Memo' })
  status!: MemoStatusEnums;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty({ type: () => EmployeeResponseDto, description: 'CreatedBy Employee' })
  createdByEmployee!: EmployeeResponseDto;

  @ApiProperty({ type: () => EmployeeResponseDto, description: 'From Employee' })
  fromEmployee!: EmployeeResponseDto;

  @ApiProperty({ type: () => [EmployeeResponseDto], description: 'To Employees' })
  toEmployees!: EmployeeResponseDto[];

  @ApiProperty({ type: () => [EmployeeResponseDto], description: 'Through Employees' })
  throughEmployees!: EmployeeResponseDto[];

  @ApiProperty({ type: () => [MemoCommentDto], description: 'Comments on Memo' })
  comments?: MemoCommentDto[];

  @ApiProperty({ type: () => [MemoHistoryDto], description: 'History of Memo' })
  history?: MemoHistoryDto[];
}

export class UpdateMemoDto {
  @ApiProperty({ description: 'Updated title of the memo', required: true })
  @IsOptional()
  @IsNotEmpty({ message: 'Title cannot be empty' })
  @IsString()
  title?: string;

  @ApiProperty({ description: 'Updated body/content of the memo', required: true })
  @IsOptional()
  @IsNotEmpty({ message: 'Body cannot be empty' })
  @IsString()
  body?: string;

  @ApiProperty({ enum: MemoStatusEnums, description: 'Staus of Memo' })
  @IsOptional()
  @IsEnum(MemoStatusEnums)
  status?: MemoStatusEnums;
}
