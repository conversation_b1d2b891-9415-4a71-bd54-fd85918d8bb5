import { Module } from '@nestjs/common';
import { AuthService } from './services/auth.service';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './jwt/jwt-auth.strategy';
import { DatabaseModule, RabbitMQModule } from '@igov/common';
import { User } from './entities';
import { UserRepository } from './repositories';
import { UserService } from './services/user.service';
@Module({
  imports: [
    DatabaseModule.forFeature([User]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'defaultSecret'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION', '1h') },
      }),
    }),
    RabbitMQModule,
  ],
  controllers: [],
  providers: [AuthService, JwtStrategy, UserRepository, JwtService, UserService],
  exports: [AuthService, UserRepository, JwtService, UserService],
})
export class AuthCoreModule {}
