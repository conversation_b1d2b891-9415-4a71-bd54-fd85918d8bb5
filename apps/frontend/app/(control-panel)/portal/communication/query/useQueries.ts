import { useState, useEffect, useCallback } from 'react';
import { queryApiService } from '@/services/api/queryService';
import { QueryResponseDto } from '@/services/api/types/query.types';

export type QueryTabType = 'inbox' | 'issued' | 'outbox';

export interface UseQueriesReturn {
  data: QueryResponseDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  pagination: {
    pageIndex: number;
    pageSize: number;
  };
  globalFilter: string;
  activeTab: QueryTabType;
  setPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setGlobalFilter: (filter: string) => void;
  setActiveTab: (tab: QueryTabType) => void;
  refresh: () => void;
}

export function useQueries(): UseQueriesReturn {
  const [data, setData] = useState<QueryResponseDto[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTabState] = useState<QueryTabType>('inbox');
  const [pagination, setPaginationState] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [globalFilter, setGlobalFilterState] = useState('');

  const fetchQueries = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const options = {
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: globalFilter || undefined,
      };

      let response;
      switch (activeTab) {
        case 'inbox':
          response = await queryApiService.getInboxQueries(options);
          break;
        case 'issued':
          response = await queryApiService.getIssuedQueries(options);
          break;
        case 'outbox':
          response = await queryApiService.getOutboxQueries(options);
          break;
        default:
          response = await queryApiService.getInboxQueries(options);
      }

      setData(response.data || []);
      setTotalCount(response.total || 0);
    } catch (err: any) {
      console.error('Error fetching queries:', err);
      setError(err.message || 'Failed to fetch queries');
      setData([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize, globalFilter, activeTab]);

  useEffect(() => {
    fetchQueries();
  }, [fetchQueries]);

  const setPagination = useCallback((newPagination: { pageIndex: number; pageSize: number }) => {
    setPaginationState(newPagination);
  }, []);

  const setGlobalFilter = useCallback((filter: string) => {
    setGlobalFilterState(filter);
    // Reset to first page when searching
    setPaginationState(prev => ({ ...prev, pageIndex: 0 }));
  }, []);

  const setActiveTab = useCallback((tab: QueryTabType) => {
    setActiveTabState(tab);
    // Reset pagination when switching tabs
    setPaginationState({ pageIndex: 0, pageSize: 10 });
    setGlobalFilterState('');
  }, []);

  const refresh = useCallback(() => {
    fetchQueries();
  }, [fetchQueries]);

  return {
    data,
    totalCount,
    loading,
    error,
    pagination,
    globalFilter,
    activeTab,
    setPagination,
    setGlobalFilter,
    setActiveTab,
    refresh,
  };
}