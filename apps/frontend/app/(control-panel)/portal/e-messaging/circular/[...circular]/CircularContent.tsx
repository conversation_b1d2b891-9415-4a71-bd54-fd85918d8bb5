import { CircularResponseDto } from '@/services/api/types/circular.types';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import OrgProfile from '@/app/(control-panel)/portal/shared/components/OrgProfile/OrgProfile';

type CircularContentProps = {
  circular: CircularResponseDto;
  fromEmployeeSignature: string | null;
};

function CircularContent({ circular, fromEmployeeSignature }: CircularContentProps) {
  return (
    <Paper 
      className="w-full mx-auto sm:my-2 lg:mt-4 p-6 sm:p-10 sm:py-12 rounded-xl shadow-sm"
      sx={{
        minHeight: 'fit-content',
        height: 'auto',
        maxHeight: 'none',
        overflow: 'visible',
      }}
    >
      {/* Organization Profile (centered) */}
      <div className="mt-3 mb-5">
        <OrgProfile size="medium" showFullDetails={true} />
      </div>

      {/* CIRCULAR (underlined, centered) */}
      <div className="text-center mb-8">
        <Typography 
          variant="h5" 
          className="font-bold" 
          sx={{ textDecoration: 'underline' }}
        >
          CIRCULAR
        </Typography>
      </div>

      {/* Left-aligned circular details */}
      <div className="flex flex-col gap-2 mb-8">
        <Typography variant="body1" color="text.secondary">
          <strong>Subject:</strong> {circular.title}
        </Typography>
        
        <Typography variant="body1" color="text.secondary">
          <strong>Reference No:</strong> {circular.reference}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>To Departments:</strong>{' '}
          {circular.circularDepartment && circular.circularDepartment.length > 0
            ? circular.circularDepartment.map(dept => dept.name).join(', ')
            : 'N/A'}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>From:</strong>{' '}
          {circular.fromEmployee
            ? `${circular.fromEmployee.firstName} ${circular.fromEmployee.lastName}`
            : 'N/A'}
        </Typography>

        <Typography variant="body1" color="text.secondary">
          <strong>Date:</strong> {moment(circular.createdAt).format('DD MMM YYYY, h:mm A')}
        </Typography>
      </div>

      <div
        className="prose prose-sm dark:prose-invert w-full max-w-full min-h-fit pb-6"
        style={{
          wordWrap: 'break-word',
          overflowWrap: 'break-word',
          hyphens: 'auto',
        }}
        dangerouslySetInnerHTML={{ __html: circular?.body || '' }}
        dir="ltr"
      />

      {/* Signature Section - Only show for published circulars (no divider) */}
      {circular?.status === 'Publish' && circular?.fromEmployee && (
        <div className="mt-8">
          <Typography variant="body2" className="mb-3 font-medium text-gray-700">
            Signatory:
          </Typography>
          {fromEmployeeSignature ? (
            <img
              className="w-32 h-16 object-contain mb-2"
              src={fromEmployeeSignature}
              alt="Employee signature"
              style={{
                maxWidth: '128px',
                maxHeight: '64px',
                width: 'auto',
                height: 'auto',
              }}
            />
          ) : null}
          <Typography variant="body2" className="font-medium text-gray-800">
            {circular?.fromEmployee?.firstName} {circular?.fromEmployee?.lastName}
          </Typography>
        </div>
      )}
    </Paper>
  );
}

export default CircularContent;
