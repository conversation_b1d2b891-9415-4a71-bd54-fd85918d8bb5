'use client';

import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import NavLinkAdapter from '@fuse/core/NavLinkAdapter';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import Box from '@mui/material/Box';
import { useAppDispatch } from '@/store/hooks'; // Updated path
import ItemIcon from './ItemIcon'; // Uses our ItemIcon
import { GDriveFolderResponseDto } from '@/services/api/types/g-drive.types'; // Using real API types
import { setSelectedItemId } from './gDriveSlice'; // Actions from our GDrive slice
import { formatDate } from './utils/formatters';
import React from 'react';

type GDriveFolderItemProps = {
  item: GDriveFolderResponseDto; // Using real API type
};

/**
 * The GDrive folder item.
 */
function GDriveFolderItem(props: GDriveFolderItemProps) {
  const { item } = props;
  const dispatch = useAppDispatch();

  if (!item) {
    return null;
  }

  return (
    <Box
      sx={{ backgroundColor: 'background.paper' }}
      className="relative w-full sm:w-40 h-40 m-2 p-4 shadow-sm rounded-xl cursor-pointer hover:shadow-lg transition-shadow duration-150 ease-in-out" // Added cursor, hover effect
    >
      <IconButton
        className="absolute z-20 top-0 right-0 m-1.5 w-8 h-8 min-h-8"
        onClick={(e) => {
          e.stopPropagation(); // Prevent NavLink click when opening details
          e.preventDefault();
          dispatch(setSelectedItemId(item.id));
        }}
        aria-label={`More info for ${item.folderName}`}
      >
        <FuseSvgIcon size={20}>heroicons-solid:information-circle</FuseSvgIcon>
      </IconButton>
      <NavLinkAdapter
        className="flex flex-col h-full w-full"
        to={`/portal/e-office/g-drive/${item.id}`} // Adjusted navigation path for GDrive
        role="button"
        aria-label={`Open folder ${item.folderName}`}
      >
        <div className="flex flex-auto w-full items-center justify-center">
          <ItemIcon type="folder" /> {/* Always folder for GDriveFolderItem */}
        </div>
        <div className="flex shrink flex-col justify-center text-center">
          <Typography className="truncate text-md font-medium">{item.folderName}</Typography>
          {item.createdAt && (
            <Typography
              className="truncate text-sm" // Slightly smaller text for date
              color="text.secondary"
            >
              {formatDate(item.createdAt)}
            </Typography>
          )}
        </div>
      </NavLinkAdapter>
    </Box>
  );
}

export default GDriveFolderItem;
