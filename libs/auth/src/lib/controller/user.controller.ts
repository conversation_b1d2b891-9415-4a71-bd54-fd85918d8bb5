import { Controller, UseGuards, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../jwt/jwt-auth.guard';
import { UserService } from '../services/user.service';
import { PaginatedUserResponseDto, QueryUserDto } from '../dtos';

@ApiTags('User')
@Controller('users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'List of users', type: PaginatedUserResponseDto })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async getUsers(@Query() query: QueryUserDto): Promise<PaginatedUserResponseDto> {
    return this.userService.getUsers(query);
  }
}
