import { DataSource, DataSourceOptions } from 'typeorm';
import * as env from 'env-var';
import * as dotenv from 'dotenv';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

dotenv.config();

const HOST = env.get('MIGRATIONS_DATABASE_HOST').required().asString();
const PORT = env.get('MIGRATIONS_DATABASE_PORT').required().asPortNumber();
const DATABASE_NAME = env.get('DATABASE_NAME').required().asString();
const USERNAME = env.get('DATABASE_USERNAME').required().asString();
const PASSWORD = env.get('DATABASE_PASSWORD').required().asString();

const config: DataSourceOptions = {
  type: 'postgres',
  host: HOST,
  port: PORT,
  username: USERNA<PERSON>,
  password: PASSWORD,
  database: DATABASE_NAME,
  namingStrategy: new SnakeNamingStrategy(),
  entities: ['./**/*.entity.ts'],
  migrations: ['libs/migrations/src/migrations/*.ts'],
  migrationsTableName: 'migrations_typeorm',
  synchronize: false,
  logging: false,
};

export default new DataSource(config);
