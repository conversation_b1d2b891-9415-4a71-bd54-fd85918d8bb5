//Gujarati , ગુજરાતી
RTE_DefaultConfig.text_language = "ભાષા";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "રદ્દ";	//"Cancel"
RTE_DefaultConfig.text_normal = "સામાન્ય";	//"Normal"
RTE_DefaultConfig.text_h1 = "મથાળા ૧";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "મથાળા ૨";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "મથાળા ૩";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "મથાળા ૪";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "મથાળા ૫";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "મથાળા ૬";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "મથાળા ૭";	//"Headline 7"
RTE_DefaultConfig.text_close = "બંધ કરો";	//"Close"
RTE_DefaultConfig.text_bold = "બોલ્ડ";	//"Bold"
RTE_DefaultConfig.text_italic = "ઇટાલિક";	//"Italic"
RTE_DefaultConfig.text_underline = "રેખાંકન";	//"Underline"
RTE_DefaultConfig.text_strike = "સ્ટ્રાઇક લાઇન";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "સુપરસ્ક્રિપ્ટ";	//"Superscript"
RTE_DefaultConfig.text_subscript = "ઉપક્રિપ્ટ";	//"Subcript"
RTE_DefaultConfig.text_ucase = "ઉપરનો કેસ";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "નીચલો કેસ";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "બંધારણ દૂર કરો";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "કડી દાખલ કરો";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "કડી ખોલો";	//"Open Link"
RTE_DefaultConfig.text_editlink = "કડીમાં સબધું કરો";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "કડી દૂર કરો";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "લીટી ઊંચાઈ";	//"Line Height"
RTE_DefaultConfig.text_indent = "indent";	//"Indent"
RTE_DefaultConfig.text_outdent = "આઉટડન્ટ";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "બ્લોક અવતરણ";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "ક્રમિત યાદી";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "અનઓર્ડર થયેલ યાદી";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "આડી નિયમ દાખલ કરો";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "તારીખ દાખલ કરો";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "કોષ્ટક દાખલ કરો";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "ચિત્ર દાખલ કરો";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "વિડિઓ દાખલ કરો";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "કોડ દાખલ કરો";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF બનાવો";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "ઇમોજી દાખલ કરો";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "વિશિષ્ટ અક્ષરો";	//"Special characters"
RTE_DefaultConfig.text_characters = "અક્ષરો";	//"Characters"
RTE_DefaultConfig.text_fontname = "ફોન્ટ";	//"Font"
RTE_DefaultConfig.text_fontsize = "માપ";	//"Size"
RTE_DefaultConfig.text_forecolor = "લખાણ રંગ";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "પાછળનો રંગ";	//"Back Color"
RTE_DefaultConfig.text_justify = "વાજબી";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "ડાબેને વાજબી ઠેરવો";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "યોગ્ય યોગ્ય";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "કેન્દ્રને વાજબી ઠેરવો";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "સંપૂર્ણને યોગ્ય ઠેરવો";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "કોઈપણ ને યોગ્ય ઠેરવો";	//"Justify None"
RTE_DefaultConfig.text_delete = "કાઢી નાંખો";	//"Delete"
RTE_DefaultConfig.text_save = "ફાઇલ સંગ્રહો";	//"Save file"
RTE_DefaultConfig.text_selectall = "બધાને પસંદ કરો";	//"Select All"
RTE_DefaultConfig.text_code = "HTML કોડ";	//"HTML Code"
RTE_DefaultConfig.text_preview = "પૂર્વદર્શન";	//"Preview"
RTE_DefaultConfig.text_print = "છાપો";	//"Print"
RTE_DefaultConfig.text_undo = "રદ કરો";	//"Undo"
RTE_DefaultConfig.text_redo = "પુનઃદો";	//"Redo"
RTE_DefaultConfig.text_more = "વધુ...";	//"More..."
RTE_DefaultConfig.text_newdoc = "નવું ડોક";	//"New Doc"
RTE_DefaultConfig.text_help = "મદદ";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "વિન્ડોને બંધબેસે છે";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "સંપૂર્ણ સ્ક્રીનમાંથી બહાર નીકળો";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "ચિત્ર સંપાદક";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "ચિત્ર શૈલીઓ";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "ઇનલાઇન શૈલીઓ";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "ફકરા શૈલીઓ";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "કડી શૈલીઓ";	//"Link Styles"
RTE_DefaultConfig.text_link = "કડી";	//"Link"
RTE_DefaultConfig.text_style = "શૈલીઓ";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css વર્ગો";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url દ્દારા";	//"By Url"
RTE_DefaultConfig.text_upload = "અપલોડ કરો";	//"Upload"
RTE_DefaultConfig.text_size = "માપ";	//"Size"
RTE_DefaultConfig.text_text = "લખાણ";	//"Text"
RTE_DefaultConfig.text_opennewwin = "નવી ટેબમાં ખોલો";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "દાખલ કરો";	//"Insert"
RTE_DefaultConfig.text_update = "સુધારો";	//"Update"
RTE_DefaultConfig.text_find = "શોધો અને બદલો";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "શોધો";	//"Find"
RTE_DefaultConfig.text_replacewith = "બદલો";	//"Replace"
RTE_DefaultConfig.text_findnext = "આગળ";	//"Next"
RTE_DefaultConfig.text_replaceonce = "બદલો";	//"Replace"
RTE_DefaultConfig.text_replaceall = "બધાને બદલો";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "બંધબેસતી કેસ";	//"Match Case"
RTE_DefaultConfig.text_matchword = "શબ્દ ને બંધબેસતો કરો";	//"Match Word"
RTE_DefaultConfig.text_move_down = "નીચે ખસેડો";	//"Move Down"
RTE_DefaultConfig.text_move_up = "ઉપર ખસેડો";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "આપોઆપ માપ";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "૧૦૦% પહોળાઈ";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "૭૫% પહોળાઈ";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "૫૦% પહોળાઈ";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% પહોળાઈ";	//"25% width"
RTE_DefaultConfig.text_controlsize = "માપ સુયોજિત કરો";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "alt લખાણ";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "વાજબી";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "ચિત્ર કેપ્શન";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "સેલ ને ભેગા કરો";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "વિભાજિત સેલ ઊભા";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "વિભાજિત સેલ આડી";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "સેલ લખાણ રંગ";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "સેલ બેક રંગ";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "ઉપર હરોળ દાખલ કરો";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "નીચે હરોળ દાખલ કરો";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "સ્તંભ ડાબે દાખલ કરો";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "સ્તંભ જમણે દાખલ કરો";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "સ્તંભ કાઢી નાંખો";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "હરોળ કાઢી નાંખો";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "કોષ્ટક કાઢી નાંખો";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "આપોઆપ માપ";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "કોષ્ટક હેડર";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "નવો ફકરો ઉમેરો";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "ચોંટાડો";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "ચોંટાડો";	//"Paste"
RTE_DefaultConfig.text_pastetext = "લખાણ ચોંટાડો";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Html તરીકે ચોંટાડો";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "શબ્દ ચોંટાડો";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "કૃપા કરીને નીચેના બોક્સમાં સામગ્રી ચોંટાડવા માટે CTRL+V નો ઉપયોગ કરો. \r\n સમાવિષ્ટ આપોઆપ સાફ થઈ જશે.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "ફકરાઓ";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "ફકરાઓ";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "ઉપર ખસેડો";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "નીચે ખસેડો";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "નકલ કરો";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "કાઢી નાંખો";	//"Delete"
RTE_DefaultConfig.text_pmore = "વધુ..";	//"More.."
RTE_DefaultConfig.text_togglemore = "વધુ..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "કિનારી ને ફેરવો";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "કાપો";	//"Cut"
RTE_DefaultConfig.text_copy = "નકલ કરો";	//"Copy"
RTE_DefaultConfig.text_copied = "નકલ કરેલ";	//"copied"
RTE_DefaultConfig.text_insertgallery = "ગેલરિ દાખલ કરો";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "દસ્તાવેજ દાખલ કરો";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "ટેમ્પલેટ દાખલ કરો";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "પૂર્વદર્શન";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "સામાન્ય";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "મોબાઇલ";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "ટેબ્લેટ";	//"Tablet"
RTE_DefaultConfig.text_table = "કોષ્ટક";	//"Table"
RTE_DefaultConfig.text_tablecell = "કોષ્ટક સેલ";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "કોષ્ટક હરોળ";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "કોષ્ટક સ્તંભ";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "આપોઆપ";	//"Automatic"
RTE_DefaultConfig.text_colormore = "વધુ";	//"More"
RTE_DefaultConfig.text_colorpicker = "રંગ પસંદ કરનાર";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "વેબ તકતી";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "નામ રંગ";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "મૂળભૂત";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "ઉમેરો";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "ખેંચો અને મૂકો";	//"Drag and drop"
RTE_DefaultConfig.text_or = "અથવા";	//"or"
RTE_DefaultConfig.text_clicktoupload = "અપલોડ કરવા માટે ક્લિક કરો";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "મૂળભૂત ચિત્ર કેપ્શન";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "શોધો";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "ઉમેરવા માટેનું લખાણ આ ક્ષેત્ર માટે અક્ષર મર્યાદા સુધી પહોંચ્યું છે.";	//"The text to be added has reached the character limit for this field."
