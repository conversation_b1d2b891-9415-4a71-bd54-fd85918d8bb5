'use client';

import FusePageSimple from '@/@fuse/core/FusePageSimple';
import { styled } from '@mui/material/styles';
import useThemeMediaQuery from '@/@fuse/hooks/useThemeMediaQuery';
import AnnouncementHeader from './AnnouncementHeader';
import AnnouncementTable from './AnnouncementTable';
import { useAnnouncements } from './useAnnouncements';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.getContrastText(theme.palette.primary.main),
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

function Announcements() {
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down('lg'));

  const {
    data,
    totalCount,
    loading,
    error,
    pagination,
    globalFilter,
    setPagination,
    setGlobalFilter,
  } = useAnnouncements();

  return (
    <Root
      header={<AnnouncementHeader />}
      content={
        <div className="mt-4 md:px-16 px-4 mb-22">
          <AnnouncementTable
            data={data}
            totalCount={totalCount}
            loading={loading}
            error={error}
            pagination={pagination}
            globalFilter={globalFilter}
            onPaginationChange={setPagination}
            onGlobalFilterChange={setGlobalFilter}
          />
        </div>
      }
      scroll={isMobile ? 'normal' : 'page'}
    />
  );
}

export default Announcements;
