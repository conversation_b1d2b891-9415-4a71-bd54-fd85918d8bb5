'use client';

import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { EmployeeResponseDto } from '@/services/api/types/employee.types';

interface AccountDetails {
  employee: EmployeeResponseDto | null;
  isLoading: boolean;
  error: Error | null;
  refresh: () => Promise<boolean>;
}

/**
 * Custom hook to fetch and manage account details for the ETokenTab.
 * It fetches the latest employee details from the auth context.
 */
export function useAccountDetails(): AccountDetails {
  const {
    employeeDetails: currentEmployee,
    loading: authLoading,
    refresh: refreshAuth,
  } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const refresh = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      await refreshAuth();
      return true;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to refresh account details');
      console.error('Error refreshing account details:', error);
      setError(error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshAuth]);

  // Initial load
  useEffect(() => {
    if (!authLoading) {
      setIsLoading(false);
    }
  }, [authLoading]);

  return {
    employee: currentEmployee || null,
    isLoading: authLoading || isLoading,
    error,
    refresh,
  };
}
