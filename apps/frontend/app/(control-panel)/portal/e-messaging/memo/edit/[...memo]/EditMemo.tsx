'use client';

import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '@/contexts/AuthContext';
import { useMemoEdit } from './useMemoEdit';
import { useMemoDetail } from '../../[...memo]/useMemoDetail';
import { useEmployees } from '@/app/(control-panel)/portal/shared/hooks/useEmployees';

import { useParams, useRouter } from 'next/navigation';
import { styled } from '@mui/material/styles';
import FusePageSimple from '@/@fuse/core/FusePageSimple';
import EditMemoHeader from './EditMemoHeader';
import Card from '@mui/material/Card';
import { motion } from 'motion/react';
import {
  Alert,
  AlertTitle,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  FormHelperText,
  InputLabel,
  ListSubheader,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useRef, useMemo } from 'react';
import RTEEditor, { RTEEditorRef } from '@/components/RTEEditor';
import Box from '@mui/material/Box';
import { MemoStatusEnums } from '@/services/api/types/memo.types';

const Root = styled(FusePageSimple)(({ theme }) => ({
  '& .FusePageSimple-header': {
    backgroundColor: '#E8E8E8',
    color: theme.palette.text.primary,
  },
  '& .FusePageSimple-content': {
    backgroundColor: '#E8E8E8',
  },
}));

interface MemoFormData {
  referenceNo: string;
  to: string[];
  through?: string[];
  from: string;
  subject: string;
  body: string;
}

const memoSchema = z.object({
  referenceNo: z.string(),
  to: z.array(z.string()).min(1, 'At least one recipient is required for "To" field.'),
  through: z.array(z.string()).optional(),
  from: z.string().min(1, 'From field is required.'),
  subject: z.string().min(1, 'Subject is required.'),
  body: z.string().min(1, 'Body is required.'),
});

function EditMemo() {
  const editorRef = useRef<RTEEditorRef>(null);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset: resetForm,
    formState: { errors, isSubmitting: isRHFSubmitting },
  } = useForm<MemoFormData>({
    resolver: zodResolver(memoSchema),
    defaultValues: {
      referenceNo: '',
      to: [],
      through: [],
      from: '',
      subject: '',
      body: '',
    },
  });

  const params = useParams();
  const router = useRouter();
  const [memoId] = params.memo as string;

  const { memo, isLoading: loadingMemo, error: memoError } = useMemoDetail(memoId);
  const { employeeDetails } = useAuth();

  const {
    submitMemo: submitMemoToApi,
    isSubmitting: isApiSubmitting,
    submissionError: apiSubmissionError,
  } = useMemoEdit(memoId, memo);

  const {
    groupedEmployeesByDepartment,
    isLoading: isLoadingEmployees,
    error: employeesError,
  } = useEmployees();

  // Check if memo can be edited (must be draft status and created by current user)
  const canEdit =
    memo?.status === MemoStatusEnums.Draft &&
    memo?.createdByEmployee.userId === employeeDetails?.userId;

  // Populate form with existing memo data
  useEffect(() => {
    if (memo && canEdit) {
      resetForm({
        referenceNo: memo.reference || '',
        to: memo.toEmployees?.map((emp) => emp.userId).filter(Boolean) || [],
        through: memo.throughEmployees?.map((emp) => emp.userId).filter(Boolean) || [],
        from: memo.fromEmployee?.userId || '',
        subject: memo.title || '',
        body: memo.body || '',
      });

      // Note: Editor content is now set via value prop, no need for setContent call
    }
  }, [memo, canEdit, resetForm]);

  // Redirect if memo cannot be edited
  useEffect(() => {
    if (memo && !canEdit) {
      router.push(`/portal/e-messaging/memo/${memoId}`);
    }
  }, [memo, canEdit, router, memoId]);

  const handleEditorChange = (content: string) => {
    setValue('body', content, { shouldValidate: true });
  };

  const onSubmit = async (data: MemoFormData) => {
    await submitMemoToApi({
      title: data.subject,
      body: data.body,
    });
  };

  // Helper function to get employee name by userId
  const getEmployeeName = (userId: string) => {
    for (const department of groupedEmployeesByDepartment) {
      const employee = department.employees.find((emp) => emp.value === userId);
      if (employee) {
        // Extract name from label (format: "FirstName LastName (Position)")
        const nameMatch = employee.label.match(/^(.+?)\s*\(/);
        return nameMatch ? nameMatch[1] : employee.label;
      }
    }
    return userId;
  };

  // Show loading state while fetching memo
  // if (loadingMemo) {
  //   return (
  //     <Root
  //       header={<EditMemoHeader />}
  //       content={
  //         <div className="flex flex-col items-center justify-center h-full">
  //           <CircularProgress />
  //           <Typography variant="h6" className="mt-4">
  //             Loading memo...
  //           </Typography>
  //         </div>
  //       }
  //     />
  //   );
  // }

  // Show error state if memo fetch failed
  // if (memoError || !memo) {
  //   return (
  //     <Root
  //       header={<EditMemoHeader />}
  //       content={
  //         <div className="flex flex-col items-center justify-center h-full">
  //           <Typography variant="h6" color="error">
  //             Error loading memo: {memoError || 'Memo not found'}
  //           </Typography>
  //           <Button
  //             variant="contained"
  //             onClick={() => router.push('/portal/e-messaging/memo')}
  //             className="mt-4"
  //           >
  //             Back to Memos
  //           </Button>
  //         </div>
  //       }
  //     />
  //   );
  // }

  // Show unauthorized access message
  // if (!canEdit) {
  //   return (
  //     <Root
  //       header={<EditMemoHeader />}
  //       content={
  //         <div className="flex flex-col items-center justify-center h-full">
  //           <Typography variant="h6" color="warning">
  //             This memo cannot be edited. Only draft memos created by you can be modified.
  //           </Typography>
  //           <Button
  //             variant="contained"
  //             onClick={() => router.push(`/portal/e-messaging/memo/${memoId}`)}
  //             className="mt-4"
  //           >
  //             View Memo
  //           </Button>
  //         </div>
  //       }
  //     />
  //   );
  // }

  return (
    <Root
      header={<EditMemoHeader />}
      content={
        <motion.div
          className="md:px-16 px-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, transition: { delay: 0 } }}
        >
          <Card className="p-4 sm:p-6 max-w-5-xl mb-20">
            {loadingMemo ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                <Typography>Loading memo data...</Typography>
              </Box>
            ) : memoError ? (
              <Box sx={{ padding: 2 }}>
                <Typography color="error">Error loading memo: {memoError}</Typography>
              </Box>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                {apiSubmissionError && (
                  <Alert severity="error" className="mb-4">
                    <AlertTitle>Memo Update Failed</AlertTitle>
                    {apiSubmissionError}
                  </Alert>
                )}

                <Controller
                  name="referenceNo"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Reference No."
                      id="ref-no"
                      variant="outlined"
                      fullWidth
                      InputProps={{
                        readOnly: true,
                      }}
                      sx={{
                        '& .MuiInputBase-input': {
                          backgroundColor: '#f5f5f5',
                        },
                      }}
                      error={!!errors.referenceNo}
                      helperText={errors.referenceNo?.message}
                    />
                  )}
                />

                <FormControl className="mt-2 mb-4" sx={{ width: '100%' }} error={!!errors.to}>
                  <InputLabel htmlFor="to-select" required>
                    To
                  </InputLabel>
                  <Controller
                    name="to"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        id="to-select"
                        label="To"
                        multiple
                        disabled={true}
                        sx={{
                          backgroundColor: '#f5f5f5',
                        }}
                        renderValue={(selected) => {
                          if (!Array.isArray(selected)) return '';
                          return (
                            memo.toEmployees
                              ?.map((emp) => `${emp.firstName} ${emp.lastName}`)
                              .join(', ') || ''
                          );
                        }}
                      >
                        {memo.toEmployees?.map((employee) => (
                          <MenuItem key={employee.userId} value={employee.userId}>
                            {`${employee.firstName} ${employee.lastName}`}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.to && <FormHelperText>{errors.to.message}</FormHelperText>}
                </FormControl>

                <Controller
                  name="subject"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mt-2 mb-4"
                      required
                      label="Subject"
                      id="subject"
                      variant="outlined"
                      fullWidth
                      error={!!errors.subject}
                      helperText={errors.subject?.message}
                      disabled={isApiSubmitting}
                    />
                  )}
                />

                <div className="mt-2 mb-4">
                  <InputLabel required error={!!errors.body} sx={{ mb: 1 }}>
                    Memo Body
                  </InputLabel>
                  <Controller
                    name="body"
                    control={control}
                    render={({ field }) => (
                      <RTEEditor
                        ref={editorRef}
                        onChange={handleEditorChange}
                        value={memo.body || ''}
                      />
                    )}
                  />
                  {errors.body && <FormHelperText error>{errors.body.message}</FormHelperText>}
                </div>

                {/* Existing Attachments Display */}
                {memo.attachments && memo.attachments.length > 0 && (
                  <div className="mt-2 mb-4">
                    <Typography variant="subtitle1" gutterBottom>
                      Existing Attachments
                    </Typography>
                    <div className="space-y-2">
                      {memo.attachments.map((attachment) => (
                        <div
                          key={attachment.id}
                          className="flex items-center justify-between p-3 border border-gray-300 rounded"
                        >
                          <div className="flex flex-col space-y-1 flex-1">
                            <div className="flex items-center space-x-2">
                              <Typography variant="body2" className="font-medium">
                                {attachment.name}
                              </Typography>
                              {attachment.size && (
                                <Typography variant="caption" color="textSecondary">
                                  • {(attachment.size / 1024).toFixed(1)} KB
                                </Typography>
                              )}
                            </div>
                            <div className="flex items-center space-x-3">
                              {attachment.type && (
                                <Typography variant="caption" color="textSecondary">
                                  Type: {attachment.type}
                                </Typography>
                              )}
                              {attachment.createdAt && (
                                <Typography variant="caption" color="textSecondary">
                                  • Uploaded: {new Date(attachment.createdAt).toLocaleDateString()}
                                </Typography>
                              )}
                            </div>
                          </div>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => window.open(attachment.url, '_blank')}
                          >
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5 mb-4">
                  <FormControl sx={{ width: '100%' }} error={!!errors.through}>
                    <InputLabel htmlFor="through-select">Through (Optional)</InputLabel>
                    <Controller
                      name="through"
                      control={control}
                      defaultValue={[]}
                      render={({ field }) => (
                        <Select
                          {...field}
                          id="through-select"
                          label="Through (Optional)"
                          multiple
                          disabled={true}
                          sx={{
                            backgroundColor: '#f5f5f5',
                          }}
                          renderValue={(selected) => {
                            if (!Array.isArray(selected)) return '';
                            return (
                              memo.throughEmployees
                                ?.map((emp) => `${emp.firstName} ${emp.lastName}`)
                                .join(', ') || ''
                            );
                          }}
                        >
                          {memo.throughEmployees?.map((employee) => (
                            <MenuItem key={employee.userId} value={employee.userId}>
                              {`${employee.firstName} ${employee.lastName}`}
                            </MenuItem>
                          ))}
                        </Select>
                      )}
                    />
                    {errors.through && <FormHelperText>{errors.through.message}</FormHelperText>}
                  </FormControl>

                  <FormControl sx={{ width: '100%' }} error={!!errors.from}>
                    <InputLabel htmlFor="from-select">From</InputLabel>
                    <Controller
                      name="from"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          id="from-select"
                          label="From"
                          disabled={true}
                          sx={{
                            backgroundColor: '#f5f5f5',
                          }}
                        >
                          <MenuItem value={field.value}>
                            {memo.fromEmployee
                              ? `${memo.fromEmployee.firstName} ${memo.fromEmployee.lastName}`
                              : field.value}
                          </MenuItem>
                        </Select>
                      )}
                    />
                    {errors.from && <FormHelperText>{errors.from.message}</FormHelperText>}
                  </FormControl>
                </div>

                <div className="flex items-center justify-end space-x-2 mt-10">
                  <Button
                    variant="outlined"
                    onClick={() => router.push(`/portal/e-messaging/memo/${memoId}`)}
                    disabled={isApiSubmitting || isRHFSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={isRHFSubmitting || isApiSubmitting}
                  >
                    {isApiSubmitting ? (
                      <CircularProgress size={15} color="inherit" />
                    ) : (
                      'Update Memo'
                    )}
                  </Button>
                </div>
              </form>
            )}
          </Card>
        </motion.div>
      }
    />
  );
}

export default EditMemo;
