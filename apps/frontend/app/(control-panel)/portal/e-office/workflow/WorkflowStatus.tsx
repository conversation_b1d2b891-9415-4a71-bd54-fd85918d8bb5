import { Chip, darken, lighten } from '@mui/material';
import { WorkflowStatusEnum } from '@/services/api/types/workflow.types';

interface WorkflowStatusConfig {
  title: string;
  color: string;
}

const workflowStatusConfig: Record<WorkflowStatusEnum, WorkflowStatusConfig> = {
  [WorkflowStatusEnum.New]: {
    title: 'New',
    color: '#2196f3', // Blue
  },
  [WorkflowStatusEnum.Ongoing]: {
    title: 'Ongoing',
    color: '#ff9800', // Orange
  },
  [WorkflowStatusEnum.Completed]: {
    title: 'Completed',
    color: '#4caf50', // Green
  },
  [WorkflowStatusEnum.Declined]: {
    title: 'Declined',
    color: '#f44336', // Red
  },
};

interface WorkflowStatusProps {
  status: WorkflowStatusEnum;
  size?: 'small' | 'medium';
}

function WorkflowStatus({ status, size = 'small' }: WorkflowStatusProps) {
  const statusConfig = workflowStatusConfig[status];
  
  if (!statusConfig) {
    // Fallback for unknown status
    return (
      <Chip
        className="font-semibold text-md"
        label="Unknown"
        sx={{
          color: '#9e9e9e',
          backgroundColor: '#f5f5f5',
        }}
        size={size}
      />
    );
  }
  
  return (
    <Chip
      className="font-semibold text-md"
      label={statusConfig.title}
      sx={(theme) => ({
        color: lighten(statusConfig.color, 0.8),
        backgroundColor: darken(statusConfig.color, 0.1),
        ...theme.applyStyles('light', {
          color: darken(statusConfig.color, 0.4),
          backgroundColor: lighten(statusConfig.color, 0.8),
        }),
      })}
      size={size}
    />
  );
}

export default WorkflowStatus;

// Helper function to get status color for other use cases
export function getWorkflowStatusColor(status: WorkflowStatusEnum): string {
  return workflowStatusConfig[status]?.color || '#9e9e9e';
}

// Helper function to get status title
export function getWorkflowStatusTitle(status: WorkflowStatusEnum): string {
  return workflowStatusConfig[status]?.title || 'Unknown';
}